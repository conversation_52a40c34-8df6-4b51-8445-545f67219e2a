<?php

namespace common\service\companyPackage;

use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\helpers\TimeHelper;
use common\libs\WxWork;
use yii\db\Exception;

class BaseService
{
    /**
     * @var BaseCompany
     */
    protected $companyModel;

    /**
     * @var BaseCompanyPackageConfig
     */
    protected $companyPackageConfigModel;

    public $adminId;
    public $adminName;
    public $logList;

    public $noticeTitle;
    public $noticeContent;
    public $noticeUrl;

    public $isSendStytem = false;

    /**
     * @throws Exception
     */
    public function setCompany($companyId): BaseService
    {
        $this->companyModel = BaseCompany::findOne($companyId);
        if (!$this->companyModel) {
            throw new Exception('企业不存在');
        }

        return $this;
    }

    /**
     * @throws Exception
     */
    public function setPackage($inspect = true)
    {
        // 企业自己操作的
        $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
            'company_id' => $this->companyModel->id,
            'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
        ]);

        $packId = $this->companyPackageConfigModel->package_id;

        if (strlen($packId) < 1) {
            throw new Exception('企业没有套餐');
        }

        if ($inspect) {
            if ($this->companyPackageConfigModel->expire_time < CUR_DATETIME) {
                throw new Exception('套餐已过期');
            }

            if ($this->companyPackageConfigModel->effect_time > CUR_DATETIME) {
                throw new Exception('套餐未生效');
            }
        }
    }

    /**
     * @throws Exception
     */
    public function checkCompanyPackageConfig($companyId)
    {
        $companyPackageConfig = BaseCompanyPackageConfig::find()
            ->select([
                'id',
                'expire_time',
                'code',
            ])
            ->where(['company_id' => $companyId])
            ->asArray()
            ->one();

        if ($companyPackageConfig) {
            // 实际的过期时间
            $expireDay  = str_replace(' 00:00:00', '', $companyPackageConfig['expire_time']);
            $expireTime = TimeHelper::dayToEndTime($expireDay);
            if (strtotime($expireTime) > time() && $companyPackageConfig['code'] != "free") {
                throw new Exception('当前单位套餐使用内，不可配置');
            }

            return $companyPackageConfig['id'];
        } else {
            return 0;
        }
    }

    /**
     * 设置操作
     * @return $this
     */
    public function setOperator($operatorId): BaseService
    {
        $this->adminId   = $operatorId;
        $this->adminName = BaseAdmin::findOneVal(['id' => $operatorId], 'name');

        return $this;
    }

    /**
     * 添加记套餐变更记录
     * @throws \yii\base\Exception
     */
    public function addChangLog(): bool
    {
        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($this->logList);

        return true;
    }

    /**
     * 添加记套餐变更记录
     * @throws Exception
     */
    public function changPackageConfig($data, $companyId): bool
    {
        $model = BaseCompanyPackageConfig::findOne(['company_id' => $companyId]);

        foreach ($data as $k => $item) {
            $model->$k = $item;
        }

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    // public function noticeAdmin()
    // {
    //     $app       = WxWork::getInstance();
    //     $noticeIds = [];
    //
    //     if ($this->isSendStytem) {
    //         $system    = \Yii::$app->params['wxWorkContact']['system'];
    //         $noticeIds = $system;
    //     }
    //
    //     if ($this->adminId) {
    //         // 通知操作的人,避免不知道账号被用作什么了
    //         $noticeIds[] = $this->adminId;
    //     }
    //
    //     if (!$this->title) {
    //
    //     }
    //
    // }

    public function setWxWorkMessage($content, $title = '', $url = '')
    {
        $this->noticeContent = $content;
        $this->noticeTitle   = $title;
        $this->noticeUrl     = $url;
    }
}
