<div class="release-container w">
    <div class="new-announcement component">
        <div class="common-title">
            <h2>最新公告&简章</h2>
        </div>
        <div class="data">
            <div class="amount">
                共<span>
                    {{latestAnnouncementTotal}}
                </span>则公告
            </div>
        </div>
    </div>

    <div class="type-select">
        <el-form :model="latestAnnouncementForm" inline>
            <?php  foreach ($tapList as $key => $item) {?>
            <el-form-item>
                <el-select @change="() => handleLatestAnnouncement()"
                           v-model="<?php echo 'latestAnnouncementForm.'.$key?>" class="m-2" size="small">
                    <?php foreach ($item as $i => $value) { ?>
                    <el-option label="<?php echo $value['v'];?>" value="<?php echo $value['k'];?>"></el-option>
                    <?php } ?>
                </el-select>
            </el-form-item>
            <?php }?>
        </el-form>
    </div>

    <?= frontendPc\components\LatestAnnouncementWidget::widget(['columnId'=>$columnId]) ?>

    <el-pagination background :layout="paginationLayout" :current-page="latestAnnouncementForm.page"
                   :page-size="latestAnnouncementPageSize"
                   :total="latestAnnouncementTotal" @current-change="(val) => handleLatestAnnouncement(val)">
    </el-pagination>
</div>