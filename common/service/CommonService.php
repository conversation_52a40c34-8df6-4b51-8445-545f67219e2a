<?php

namespace common\service;

use common\base\models\BaseMember;
use Yii;
use yii\base\Exception;

/**
 * 服务层公共继承
 * 采用info文件日志记录所有操作日志
 * 1、公共配置
 * 2、公共封装
 * 3、写日志
 * 4、操作平台思想
 *    --Redis Key:redis_prefix+operationPlatform
 *    --Platform Category Name:WEB H5 ADMIN APP MINI
 *
 */
class CommonService
{
    /** 服务Debug Log */
    protected $serviceDebug = true;
    /** 执行的类名 */
    protected $class;
    /** 开始执行时间 */
    protected $startDatetime;
    /** 开始执行时间戳 */
    protected $startTimestamp;
    /** 操作平台 */
    protected $operationPlatform;
    /** 操作类型 */
    protected $operationType;

    /** 全web端 */
    const PLATFORM_WEB = 1;
    /** web端 求职者 */
    const PLATFORM_WEB_PERSON = 2;
    /** web端 单位 */
    const PLATFORM_WEB_COMPANY = 3;
    /** H5端 */
    const PLATFORM_H5 = 5;
    /** ADMIN端 */
    const PLATFORM_ADMIN = 10;
    /** APP端 */
    const PLATFORM_APP = 15;
    /** MINI端 */
    const PLATFORM_MINI = 20;
    /** 海外端 */
    const PLATFORM_HAIWAI = 25;
    /** 博士后端 */
    const PLATFORM_BOSHIHOU = 30;
    /** 招聘会端 */
    const PLATFORM_ZHAOPINHUI = 35;

    //重点一二线城市ID
    const TOP_AREA_KEY_CITY = [
        2,//北京
        20,//天津
        802,//上海
        2324,//重庆
        1965,//广州
        1988,//深圳
        1710,//武汉
        821,//南京
        2899,//西安
        2368,//成都
        934,//杭州
        1047,//合肥
        1828,//长沙
        1533,//郑州
    ];

    /** redis缓存时间 单位：s 24*3600*/
    protected $cacheRedisTime = 86400;

    public function __construct()
    {
        $this->class          = get_class($this);
        $this->startDatetime  = date("Y-m-d H:i:s");
        $this->startTimestamp = time();
        $this->beforeExec();
    }

    /**
     * 设置操作平台
     * @param $operation
     * @return $this
     */
    public function setPlatform($platform)
    {
        $this->operationPlatform = $platform;

        return $this;
    }

    /**
     * 设置操作类型
     * @param $operation
     * @return $this
     */
    public function setOparetion($operation)
    {
        $this->operationType = $operation;

        return $this;
    }

    /**
     * 重定向去404页面
     */
    public function redirect404()
    {
        //        Yii::$app->response->setStatusCode(404)->send();
        Yii::$app->response->setStatusCode(404)
            ->redirect('/home/<USER>', 301)
            ->send();
        //        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    /**
     * 服务执行前输出日志
     */
    protected function beforeExec()
    {
        Yii::info('Start Exec Method Path:' . $this->class, 'service');
        Yii::info('Start Datetime:' . $this->startDatetime, 'service');
        Yii::info('Start Timestamp:' . $this->startTimestamp, 'service');
        Yii::info('Client IP:' . $_SERVER['REMOTE_ADDR'], 'service');
    }

    /**
     * 服务执行后输出日志
     */
    protected function afterExec()
    {
        $endDatetime  = date("Y-m-d H:i:s");
        $endTimestamp = time();
        $expendTime   = $endTimestamp - $this->startTimestamp;
        Yii::info('End Datetime:' . $endDatetime, 'service');
        Yii::info('End Timestamp:' . $endTimestamp, 'service');
        Yii::info('Expend Time:' . $expendTime . 's', 'service');
        Yii::info('End Exec Method Path:' . $this->class, 'service');
        Yii::info('------------------------Exit-----------------------------' . PHP_EOL, 'service');
    }

    /**
     * 用户未登录重定向
     */
    protected function redirectLogin()
    {
        echo json_encode([
            'code'   => 403,
            'result' => 0,
            'msg'    => '未授权用户',
        ]);
        exit;
    }

    /**
     * 输出日志
     * @param $message string|array 输出信息
     */
    protected function line($message)
    {
        if (!empty($message) && $this->serviceDebug) {
            Yii::info('Execute Info:' . json_encode($message), 'service');
        }
    }

    /**
     * 输出数据
     * @param $data
     */
    protected static function writeEcho($data)
    {
        echo '<pre>';
        if (is_array($data)) {
            echo json_encode($data);
            exit;
        } else {
            echo $data;
            exit;
        }
    }

    /**
     * 字符串替换
     * @param              $text
     * @param string|array $replace
     * @return string|string[]
     */
    protected function textReplace($text, $replace)
    {
        if (is_string($replace) || is_numeric($replace)) {
            return str_replace('{p1}', $replace, $text);
        } elseif (is_array($replace)) {
            $search = [];
            for ($i = 1; $i <= count($replace); $i++) {
                array_push($search, '{p' . $i . '}');
            }

            return str_replace($search, $replace, $text);
        } else {
            return '';
        }
    }

    /**
     * 年时间替换
     * @param $text
     * @param $replace
     * @return string|string[]
     */
    protected function yearTimeReplace($text)
    {
        return str_replace('{y}', CUR_YEAR, $text);
    }

    public static function setPage($count, $page, $pageSize)
    {
        $page = $page ?: 1;
        if ($count > 100) {
            $count = 100;
        }

        return [
            'offset' => ($page - 1) * $pageSize,
            'limit'  => $pageSize,
            'total'  => ceil($count / $pageSize),
            'page'   => $page,
        ];
    }
}