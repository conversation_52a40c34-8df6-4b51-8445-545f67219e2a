<?php

namespace common\service\job;

use common\base\models\BaseAdmin;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\helpers\IpHelper;
use yii\base\Exception;
use yii\validators\ValidationAsset;

class RefreshService extends BaseService
{

    /**
     * 执行添加
     * @throws Exception
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_ADD;

        $this->beforeRun();
        $this->refresh();
        $this->afterRun();
    }

    public function setStaging(): RefreshService
    {
        $this->saveType = self::SAVE_TYPE_STAGING;

        return $this;
    }

    public function setAudit(): RefreshService
    {
        $this->saveType = self::SAVE_TYPE_AUDIT;

        return $this;
    }

    private function refresh()
    {
        try {
            $idArr      = explode(',', $data['id']);
            $jobWhere   = ['and'];
            $jobWhere[] = [
                'in',
                'id',
                $idArr,
            ];
            $jobSelect  = [
                'id',
                'refresh_time',
                'name',
            ];
            $jobList    = BaseJob::selectInfos($jobWhere, $jobSelect);
            if (!$jobList || (count($jobList) != count($idArr))) {
                throw new Exception('非法请求');
            }

            //关联公告条件
            $nowDateTime = date("Y-m-d H-i-s");
            if (!BaseJob::updateAll([
                'refresh_time' => $nowDateTime,
                'refresh_date' => CUR_DATE,
            ], [
                'in',
                'id',
                $idArr,
            ])) {
                throw new Exception('失败');
            }
            // foreach ($jobList as $job) {
            //     $list                  = [];
            //     $list['job_id']        = $job['id'];
            //     $list['handle_type']   = (string)BaseJobHandleLog::HANDLE_TYPE_REFRESH;
            //     $list['handle_before'] = ['刷新时间' => $job['refresh_time']];
            //     $list['handle_after']  = ['刷新时间' => $nowDateTime];
            //     BaseJob::createJobHandleLog($list);
            // }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 这里把所有应该有的数据都设置好
     *
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setDate($data): RefreshService
    {
        // 默认情况下,职位的类型是纯职位,
        $this->type      = $data['type'] ?: self::TYPE_ALONE;
        $this->companyId = $data['companyId'];
        if (!$this->companyId) {
            throw new Exception('请选择所属企业');
        }

        $this->setCompany();

        if ($data['jobId']) {
            $this->setJob($data['jobId']);
        }

        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $this->adminModel   = BaseAdmin::findOne($this->operatorId);
            $this->operatorName = $this->adminModel->name;
        }

        if ($data['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            $data['applyAuditTime'] = CUR_DATETIME;
        }
        // 开始组装数据
        $data['memberId'] = $this->companyMemberId;

        // 薪资范围(非年薪)
        if ($data['wageType'] != BaseJob::WAGE_TYPE_YEAR) {
            // 如果非面议(前端直接传了类型过来)
            if ($data['isNegotiable'] == BaseJob::IS_NEGOTIABLE_NO) {
                if (!empty($data['wageId'])) {
                    $wageInfo        = BaseDictionary::getMinAndMaxWage($data['wageId']);
                    $data['minWage'] = (int)$wageInfo['min'];
                    $data['maxWage'] = (int)$wageInfo['max'];
                }
            }
        }

        // 这里应该检查一下数值的合法或者非法?

        $this->jobData = $data;

        return $this;
    }

    /**
     * 暂存信息
     * @throws Exception
     */
    //    private function staging()
    //    {
    //        $this->add();
    //    }

    /**
     * @throws Exception
     */
    //    private function audit()
    //    {
    //        // 有两种情况,第一种是直接提交审核,第二种是从编辑页面提交审核
    //        if ($this->jobId) {
    //            $this->edit();
    //        } else {
    //            $this->add();
    //        }
    //    }

    //    private function preview()
    //    {
    //    }
    //
    //    /**
    //     * @throws Exception
    //     */
    //    private function add()
    //    {
    //        $data = $this->jobData;
    //        // 开始对数据进行校验
    //        $model = new BaseJob();
    //        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
    //            // 企业的
    //            $model->create_type = BaseJob::CREATE_TYPE_SELF;
    //            $model->creator     = $this->companyMemberModel->username;
    //        }
    //
    //        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
    //            // 运营后台添加的x
    //            $model->create_type = BaseJob::CREATE_TYPE_AGENT;
    //            $model->creator     = $this->adminModel->name;
    //        }
    //
    //        $model->create_id        = $this->operatorId;
    //        $model->member_id        = $this->companyMemberId;
    //        $model->company_id       = $this->companyId;
    //        $model->name             = $data['name'];
    //        $model->code             = $data['code'];
    //        $model->job_category_id  = $data['jobCategoryId'];
    //        $model->education_type   = $data['educationType'];
    //        $model->major_id         = $data['majorId'];
    //        $model->nature_type      = $data['natureType'];
    //        $model->wage_type        = $data['wageType'];
    //        $model->is_negotiable    = $data['isNegotiable'];
    //        $model->min_wage         = $data['minWage'];
    //        $model->max_wage         = $data['maxWage'];
    //        $model->experience_type  = $data['experienceType'];
    //        $model->age_type         = $data['ageType'];
    //        $model->title_type       = $data['titleType'];
    //        $model->political_type   = $data['politicalType'];
    //        $model->abroad_type      = $data['abroadType'];
    //        $model->amount           = $data['amount'];
    //        $model->department       = $data['department'];
    //        $model->province_id      = $data['provinceId'];
    //        $model->city_id          = $data['cityId'];
    //        $model->district_id      = '';
    //        $model->address          = $data['address'];
    //        $model->welfare_tag      = $data['welfareTag'];
    //        $model->period_date      = $data['periodDate'];
    //        $model->duty             = $data['duty'];
    //        $model->requirement      = $data['requirement'];
    //        $model->remark           = $data['remark'];
    //        $model->apply_type       = $data['applyType'] ?: '';
    //        $model->apply_address    = $data['applyAddress'] ?: '';
    //        $model->status           = $data['status'] ?: BaseJob::STATUS_WAIT;
    //        $model->is_show          = BaseJob::IS_SHOW_YES;
    //        $model->is_article       = BaseJob::IS_ARTICLE_NO;
    //        $model->audit_status     = $data['auditStatus'] ?: BaseJob::AUDIT_STATUS_WAIT;
    //        $model->apply_audit_time = $data['applyAuditTime'] ?: '0000-00-00 00:00:00';
    //
    //        if (!$model->save()) {
    //            throw new Exception($model->getFirstErrorsMessage());
    //        }
    //
    //        $this->jobModel = $model;
    //    }
    //
    //    /**
    //     * @throws Exception
    //     *
    //     *  这个职位已经有了,其实现在算是编辑了
    //     */
    //    private function edit()
    //    {
    //        $jobId    = $this->jobId;
    //        $jobModel = $this->jobModel;
    //        $data     = $this->jobData;
    //
    //        // 校验一下什么状态下是允许修改的
    //        /**
    //         * self::STATUS_OFFLINE
    //         * self::STATUS_ONLINE
    //         * self::STATUS_WAIT
    //         * self::STATUS_WAIT_AUDIT
    //         * self::STATUS_DELETE
    //         * self::STATUS_REFUSE_AUDIT
    //         */
    //        switch ($jobModel->status) {
    //            case BaseJob::STATUS_OFFLINE:
    //                throw new Exception('该职位已经下线,不能修改');
    //            case BaseJob::STATUS_WAIT_AUDIT:
    //                throw new Exception('该职位正在审核中,不能修改');
    //            case BaseJob::STATUS_DELETE:
    //                throw new Exception('该职位已经删除,不能修改');
    //            case BaseJob::STATUS_ONLINE:
    //                // 在线状态,允许修改,但是会去到待审核
    //                break;
    //            case BaseJob::STATUS_WAIT:
    //                // 待审核状态,允许修改,但是会去到待审核
    //                break;
    //            case BaseJob::STATUS_REFUSE_AUDIT:
    //                // 审核拒绝状态,允许修改,但是会去到待审核
    //                break;
    //            default:
    //                throw new Exception('该职位状态不正确,不能修改');
    //        }
    //
    //        $jobModel->create_id       = $this->operatorId;
    //        $jobModel->member_id       = $this->companyMemberModel->id;
    //        $jobModel->name            = $data['name'];
    //        $jobModel->code            = $data['code'];
    //        $jobModel->job_category_id = $data['jobCategoryId'];
    //        $jobModel->education_type  = $data['educationType'];
    //        $jobModel->major_id        = $data['majorId'] ?: '';
    //        $jobModel->nature_type     = $data['natureType'];
    //        $jobModel->wage_type       = $data['wageType'];
    //        $jobModel->is_negotiable   = $data['isNegotiable'];
    //        $jobModel->min_wage        = $data['minWage'];
    //        $jobModel->max_wage        = $data['maxWage'];
    //        $jobModel->experience_type = $data['experienceType'];
    //        $jobModel->age_type        = $data['ageType'];
    //        $jobModel->title_type      = $data['titleType'];
    //        $jobModel->political_type  = $data['politicalType'];
    //        $jobModel->abroad_type     = $data['abroadType'];
    //        $jobModel->amount          = $data['amount'];
    //        $jobModel->department      = $data['department'];
    //        $jobModel->province_id     = $data['provinceId'];
    //        $jobModel->city_id         = $data['cityId'];
    //        $jobModel->district_id     = '';
    //        $jobModel->address         = $data['address'];
    //        $jobModel->welfare_tag     = $data['welfareTag'];
    //        $jobModel->period_date     = $data['periodDate'];
    //        $jobModel->apply_type      = $data['applyType'] ?: '';
    //        $jobModel->apply_address   = $data['applyAddress'] ?: '';
    //        $jobModel->is_show         = $data['isShow'] ?: BaseJob::IS_SHOW_YES;
    //
    //        // oldData
    //        $oldData = $jobModel->getOldAttributes();
    //
    //        // TODO 修改了【岗位职责、任职要求、其他说明】
    //        $editList         = [
    //            'duty'        => $data['duty'],
    //            'requirement' => $data['requirement'],
    //            'remark'      => $data['remark'],
    //        ];
    //        $oldList          = [
    //            'duty'        => $oldData['duty'],
    //            'requirement' => $oldData['requirement'],
    //            'remark'      => $oldData['remark'],
    //        ];
    //        $modifyAfterList  = array_diff_assoc($editList, $oldList);
    //        $modifyBeforeList = [];
    //        foreach ($modifyAfterList as $k => $list) {
    //            $modifyBeforeList[$k] = $oldList[$k];
    //        }
    //
    //        if ($oldData['status'] == BaseJob::STATUS_ONLINE) {
    //            if (sizeof($modifyAfterList) > 0) {
    //                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
    //                    $jobModel->creator = $this->companyMemberModel->username;
    //                }
    //
    //                if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
    //                    $jobModel->creator = $this->adminModel->name;
    //                }
    //                $announcementId = $oldData['announcement_id'];
    //
    //                $editContent = json_encode($modifyAfterList);
    //                $list        = [
    //                    'job_id'          => $jobId,
    //                    'add_time'        => CUR_DATETIME,
    //                    'status'          => BaseJobEdit::STATUS_ONLINE,
    //                    'edit_content'    => $editContent,
    //                    'editor_id'       => $this->operatorId,
    //                    'editor_type'     => BaseJobEdit::EDITOR_TYPE_COMPANY,
    //                    'editor'          => $jobModel->creator,
    //                    'announcement_id' => $announcementId,
    //                ];
    //                // 检测之前是否有职位编辑内容
    //                $jobEditInfo = BaseJobEdit::selectInfo(['job_id' => $jobId], ['id']);
    //                if ($jobEditInfo['id']) {
    //                    $condition = ['id' => $jobEditInfo['id']];
    //                    BaseJobEdit::updateAll($list, $condition);
    //                } else {
    //                    BaseJobEdit::createInfo($list);
    //                }
    //                //这里存职位操作表
    //                $changeModifyBeforeList = [];
    //                $changeModifyAfterList  = [];
    //                foreach ($modifyBeforeList as $k => $item) {
    //                    switch ($k) {
    //                        case 'duty':
    //                            $changeModifyBeforeList['岗位职责'] = $item;
    //                            break;
    //                        case 'requirement':
    //                            $changeModifyBeforeList['任职要求'] = $item;
    //                            break;
    //                        case 'remark':
    //                            $changeModifyBeforeList['其他说明'] = $item;
    //                            break;
    //                    }
    //                }
    //
    //                foreach ($modifyAfterList as $k => $item) {
    //                    switch ($k) {
    //                        case 'duty':
    //                            $changeModifyAfterList['岗位职责'] = $item;
    //                            break;
    //                        case 'requirement':
    //                            $changeModifyAfterList['任职要求'] = $item;
    //                            break;
    //                        case 'remark':
    //                            $changeModifyAfterList['其他说明'] = $item;
    //                            break;
    //                    }
    //                }
    //
    //                $handleBefore = json_encode($changeModifyBeforeList);
    //                $handleAfter  = json_encode($changeModifyAfterList);
    //                $jobHandleLog = [
    //                    'add_time'      => CUR_DATETIME,
    //                    'job_id'        => $jobId,
    //                    'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_EDIT,
    //                    'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_USER,
    //                    'handler_id'    => $this->operatorId,
    //                    'handler_name'  => $jobModel->creator,
    //                    'handle_before' => $handleBefore,
    //                    'handle_after'  => $handleAfter,
    //                    'ip'            => IpHelper::getIpInt(),
    //                ];
    //                BaseJobHandleLog::createInfo($jobHandleLog);
    //
    //                //只有修改--岗位职责、任职要求、其他说明，移至待审核
    //                $jobModel->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
    //                $jobModel->apply_audit_time = CUR_DATETIME;
    //            }
    //        } else {
    //            $jobModel->duty             = $data['duty'];
    //            $jobModel->requirement      = $data['requirement'];
    //            $jobModel->remark           = $data['remark'];
    //            $jobModel->status           = BaseJob::STATUS_WAIT_AUDIT;
    //            $jobModel->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
    //            $jobModel->apply_audit_time = CUR_DATETIME;
    //        }
    //
    //        if (!$jobModel->save()) {
    //            throw new Exception($jobModel->getFirstErrorsMessage());
    //        }
    //    }

}
