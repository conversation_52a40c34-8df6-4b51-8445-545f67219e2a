<?php

namespace timer\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementClickTotalDaily;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseCompany;
use common\base\models\BaseDailyAnnouncementSummary;
use common\base\models\BaseJob;
use common\base\models\BaseNews;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\WxWork;
use common\service\announcement\OfflineService;
use common\service\boShiHouColumn\AnnouncementService;
use queue\Producer;
use yii;
use yii\base\Exception;

/**
 *
 */
class AnnouncementController extends BaseTimerController
{

    public function online()
    {
    }

    public function actionOffline()
    {
        // 首先, 找到所有应该下线的公告,有两种

        $yesterday = TimeHelper::getYesterday();

        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->select([
                'a.id',
                'a.period_date',
            ])
            ->where([
                'b.status' => BaseAnnouncement::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<',
                'period_date',
                TimeHelper::dayToEndTime($yesterday),
            ])
            ->andWhere([
                '!=',
                'period_date',
                TimeHelper::ZERO_TIME,
            ])
            ->asArray()
            ->all();

        // 找到所有非合作单位没有写下线日期,并且发布时间超过365天的在线公告
        // 365天前
        $theDayBefore365Day = date('Y-m-d', strtotime('-365 day'));
        $list2              = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id=a.company_id')
            ->select([
                'a.id',
                'refresh_date',
                'c.full_name',
            ])
            ->where([
                'b.status' => BaseAnnouncement::STATUS_ACTIVE,
                'a.status' => BaseAnnouncement::STATUS_ACTIVE,
            ])
            ->andWhere([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->andWhere([
                '<',
                'b.refresh_time',
                TimeHelper::dayToBeginTime($theDayBefore365Day),
            ])
            ->andWhere([
                'period_date' => TimeHelper::ZERO_TIME,
            ])
            ->asArray()
            ->all();

        self::log('先下线有截止时间的');
        self::log('============');
        self::log('一共' . count($list) . '条');

        $service = new OfflineService();
        foreach ($list as $k => $item) {
            $params = [
                'id'     => $item['id'],
                'reason' => '自动下线',
            ];
            // 输出百分比
            self::log($k . '/' . count($list));
            // 里面会有一些职位是不能下线的,所以就不要全部一起提交了
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $service->setOperator(0, 0)
                    ->setOffline()
                    ->setData($params)
                    ->run();
                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                self::log($item['id']);
                self::log($e->getMessage());
            }
            //            $auto = new AnnouncementAutoClassify($item['id']);
            //            $auto->updateStatAnnouncementCount();
            Producer::afterAnnouncementUpdateJob($item['id']);
        }

        self::log('============');
        self::log('再线下线没有截止时间的');
        self::log(var_export($list2, true));
        foreach ($list2 as $k => $item) {
            $params = [
                'id'     => $item['id'],
                'reason' => '自动下线',
            ];
            self::log($k . '/' . count($list2));
            // 里面会有一些职位是不能下线的,所以就不要全部一起提交了
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $service->setOperator(0, 0)
                    ->setOffline()
                    ->setData($params)
                    ->run();
                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                self::log($item['id']);
                self::log($e->getMessage());
            }
            //            $auto = new AnnouncementAutoClassify($item['id']);
            //            $auto->updateStatAnnouncementCount();
            Producer::afterAnnouncementUpdateJob($item['id']);
        }
    }

    public function actionDailySummary($date = '')
    {
        try {
            if (!$date) {
                $yesterday = TimeHelper::getYesterday();
                $summary   = BaseDailyAnnouncementSummary::findOne(['belong_date' => $yesterday]);
                if ($summary) {
                    self::log($date . '已经有数据了');

                    return true;
                }
                $date = $yesterday;
            }
            BaseDailyAnnouncementSummary::create($date);
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    public function actionRunAllAutoColumn()
    {
        $list = BaseAnnouncement::find()
            ->asArray()
            ->all();
        foreach ($list as $item) {
            $model = new AnnouncementAutoClassify($item['id']);
            $model->run();
        }
    }

    public function actionRunAutoColumn($id)
    {
        $ann = BaseAnnouncement::findOne($id);
        if (!$ann) {
            return false;
        }
        $model = new AnnouncementAutoClassify($id);
        $model->run();
    }

    /**
     * 公告真实刷新时间数据修改填充
     * @return false|void
     */
    public function actionRunAnnouncementRefreshTimeToRealRefreshTime()
    {
        $list = BaseAnnouncementHandleLog::find()
            ->where([
                'handle_type' => BaseAnnouncementHandleLog::HANDLE_TYPE_REFRESH,
            ])
            ->asArray()
            ->all();
        if (!$list) {
            return false;
        }

        try {
            $transaction = Yii::$app->db->beginTransaction();

            foreach ($list as $item) {
                $model        = BaseAnnouncement::findOne(['id' => $item['announcement_id']]);
                $articleModel = BaseArticle::findOne(['id' => $model->article_id]);
                self::log(var_export($articleModel, true));

                if ($articleModel->refresh_time != TimeHelper::ZERO_TIME && $articleModel->real_refresh_time == TimeHelper::ZERO_TIME) {
                    $articleModel->real_refresh_time = $articleModel->refresh_time;
                    $articleModel->save();
                }
                // 公告的职位真实刷新时间修改填充
                $jobModel = BaseJob::findAll(['announcement_id' => $item['announcement_id']]);
                if ($jobModel) {
                    foreach ($jobModel as $job) {
                        self::log(var_export($job, true));

                        if ($job->refresh_time != TimeHelper::ZERO_TIME && $job->real_refresh_time == TimeHelper::ZERO_TIME) {
                            $job->real_refresh_time = $job->refresh_time;
                            $job->save();
                        }
                    }
                }
            }

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            self::log($e->getMessage());
        }
    }

    /**
     * 根据属性过期时间删除属性
     * @return false|void
     */
    public function actionRunAttributeExpireDelete()
    {
        $yesterday = TimeHelper::getYesterday();
        $list      = BaseArticleAttribute::find()
            ->andWhere([
                '<',
                'expire_time',
                TimeHelper::dayToEndTime($yesterday),
            ])
            ->andWhere([
                '!=',
                'expire_time',
                TimeHelper::ZERO_TIME,
            ])
            ->asArray()
            ->all();
        if (!$list) {
            return false;
        }
        self::log(var_export($list, true));
        $messageData = [];

        foreach ($list as $item) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model     = BaseArticleAttribute::findOne($item['id']);
                $type      = $model->type;
                $articleId = $model->article_id;
                // 找到article
                $articleModel = BaseArticle::findOne($articleId);
                $title        = $articleModel->title;
                $typeName     = BaseArticleAttribute::ATTRIBUTE_LIST[$type];
                if ($articleModel->type == BaseArticle::TYPE_ANNOUNCEMENT) {
                    // 公告
                    $announcementId = BaseAnnouncement::findOneVal(['article_id' => $articleId], 'id');
                    $showId         = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $announcementId);
                    $messageData[]  = "公告:{$typeName},id:{$showId},标题:{$title}";
                } else {
                    // 资讯
                    $newsId        = BaseNews::findOneVal(['article_id' => $articleId], 'id');
                    $messageData[] = "资讯:{$typeName},id:{$newsId},标题:{$title}";
                }

                if (!$model) {
                    self::log('找不到对应的公告属性');
                }
                $model->delete();

                self::log(var_export($model->attributes, true));
                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                self::log($e->getMessage());
            }
        }

        if (!$messageData) {
            return false;
        }

        $sendMessage = $yesterday . '一共下线' . count($messageData) . '条属性,具体如下' . PHP_EOL . implode(PHP_EOL,
                $messageData);

        try {
            $wxWork = WxWork::getInstance();
            $wxWork->messageToWebsiteOperationAudit($sendMessage);
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    /**
     * 更新在线公告的下线方式
     * @return false|void
     * php timer_yii announcement/set-announcement-offline-type
     */
    public function actionSetAnnouncementOfflineType()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $announcementList = BaseAnnouncement::find()
                ->alias('a')
                ->leftJoin(['art' => BaseAnnouncement::tableName()], 'art.id = a.article_id')
                ->select([
                    'a.id',
                ])
                ->where([
                    'art.status' => BaseAnnouncement::STATUS_AUDIT_PASS,
                ])
                ->andWhere([
                    '!=',
                    'a.offline_type',
                    0,
                ])
                ->asArray()
                ->all();
            $count            = count($announcementList);
            self::log("数据共{$count}条\n");

            foreach ($announcementList as $item) {
                $model = BaseAnnouncement::findOne($item['id']);
                self::log("开始处理公告id:{$model['id']}\n");

                $model->offline_type = 0;
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新公告Id:{$model['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 更新公告是否首发
     * 规则：
     *      1、发布日为近15日
     *      2、对外发布时间=初始发布时间
     *
     * php timer_yii announcement/set-announcement-is-first
     * 每一天凌晨过1分执行一次
     */
    public function actionSetAnnouncementIsFirst()
    {
        $day = 15;
        //计算15天前的日期
        $fifteenDaysAgo = date('Y-m-d', strtotime('-' . $day . ' day'));

        //获取15天前发布的首发公告
        $announcementList = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['art' => BaseArticle::tableName()], 'art.id = a.article_id')
            ->select([
                'a.id',
                'art.refresh_time',
                'art.first_release_time',
            ])
            ->where([
                '<',
                'art.first_release_time',
                TimeHelper::dayToBeginTime($fifteenDaysAgo),
            ])
            ->where(['a.is_first_release' => BaseAnnouncement::IS_FIRST_RELEASE_YES])
            ->asArray()
            ->all();

        $count = count($announcementList);
        self::log("数据共{$count}条\n");

        foreach ($announcementList as $item) {
            $model = BaseAnnouncement::findOne($item['id']);
            self::log("开始处理公告id:{$model['id']}\n");

            $model->is_first_release = BaseAnnouncement::IS_FIRST_RELEASE_NO;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
            self::log("更新公告Id:{$model['id']}成功\n");
        }
    }

    /**
     * 博士后公告榜单
     * php timer_yii announcement/update-announcement-ranking
     */
    public function actionUpdateAnnouncementRanking()
    {
        AnnouncementService::getRankingListCache();
        self::log('博士后公告榜单更新成功');
    }

    /**
     * 统计公告日点击量
     * php timer_yii announcement/update-announcement-click-total-daily
     */
    public function actionUpdateAnnouncementClickTotalDaily($date = '')
    {
        // 昨天
        if (!$date) {
            $date = TimeHelper::getYesterday();
        }

        BaseAnnouncementClickTotalDaily::updateDate($date);

        self::log('公告日点击量统计成功' . $date);
    }

}
