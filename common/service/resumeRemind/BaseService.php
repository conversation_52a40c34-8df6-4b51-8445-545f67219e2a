<?php

namespace common\service\resumeRemind;

use common\base\models\BaseJobApply;

/**
 */
class BaseService
{
    // 操作状态
    const JOB_OPERATION_STATUS_TO_FIELD = [
        BaseJobApply::STATUS_THROUGH_FIRST   => 'job_apply_pass_count',
        BaseJobApply::STATUS_INAPPROPRIATE   => 'job_apply_no_pass_count',
        BaseJobApply::STATUS_SEND_INVITATION => 'job_apply_invite_count',
        BaseJobApply::STATUS_EMPLOYED        => 'job_apply_employed_count',
    ];

}

