<?php

namespace common\base\models;

use common\libs\Cache;
use common\models\EngineSearchType;

class BaseEngineSearchType extends EngineSearchType
{
    const IS_DELETE_NO  = 2;//正常
    const IS_DELETE_YSE = 1;//删除

    /* 定义搜索类型 */
    /* 工作地点 */
    const SEARCH_TYPE_AREA = 1;
    /* 单位类型 */
    const SEARCH_TYPE_COMPANY_TYPE = 2;
    /* 学科分类 */
    const SEARCH_TYPE_MAJOR = 3;
    /* 学历要求 */
    const SEARCH_TYPE_EDUCATION_TYPE = 4;
    /* 职位类型 */
    const SEARCH_TYPE_JOB_TYPE = 5;

    /**
     * 获取搜索配置表所有数据
     */
    public static function getTypeData()
    {
        $data = Cache::hvals(Cache::ALL_TABLE_ENGINE_SEARCH_TYPE_KEY);
        if (!$data) {
            $data = self::find()
                ->where(['is_delete' => self::IS_DELETE_NO])
                ->asArray()
                ->all();
            Cache::hMSet(Cache::ALL_TABLE_ENGINE_SEARCH_TYPE_KEY, array_column($data, null, 'id'));
        }

        return $data;
    }
}