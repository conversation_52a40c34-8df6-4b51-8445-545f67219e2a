<?php

namespace common\service\downloadTask;

use admin\models\Order;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseResumeOrder;
use common\helpers\UUIDHelper;

class OrderPersonListService
{
    /**
     * 导出数据准备
     * @param $params
     * @return array
     * @throws \yii\base\Exception
     */
    public function run($params)
    {
        $fileName = BaseAdminDownloadTask::TYPE_ORDER_PERSON_LIST_NAME . '_' . date('YmdHis');
        //构建数据DB句柄
        $query = Order::getBuilderReusmeOrderQuery($params);
        //获取分页列表数据
        ////排序-下单时间倒叙
        $query->orderBy('ro.add_time DESC');
        $list = $query->select([
            //订单ID
            'ro.id',
            //订单号
            'ro.order_no',
            //流水号
            'ro.trade_no',
            //产品ID
            'ro.equity_package_id',
            //产品名称
            'reps.name AS equity_package_name',
            //产品类型
            'reps.equity_package_category_id',
            //产品类型名称
            'repcs.name AS equity_package_category_name',
            //简历ID
            'ro.resume_id',
            //简历名称
            'r.name AS resume_name',
            //支付状态
            'ro.status',
            //支付方式
            'ro.payway',
            //支付时间
            'ro.pay_time',
            //添加时间
            'ro.add_time',
            //支付金额
            'ro.real_amount',
            //下单平台
            'ro.platform',
            //下单时间
            'ro.add_time',
            //备注
            'ro.remark',
            //快照
            'ro.snapshot_data',
        ])
            ->asArray()
            ->all();
        //导出头部文本定义
        $headers = [
            '订单号',
            '产品ID',
            '产品名称',
            '产品类型',
            '用户ID',
            '姓名',
            '支付金额',
            '优惠金额',
            '支付状态',
            '支付方式',
            '下单时间',
            '支付时间',
            '流水号',
            '下单渠道',
            '备注',
        ];
        $data    = [];
        //列表数据循环处理
        foreach ($list as &$item) {
            //订单快照数据
            $snapshot_data = json_decode($item['snapshot_data'], true);
            if (isset($snapshot_data['convert_data']) && isset($snapshot_data['vip_grade']) == true) {
                $discounts_amount = $snapshot_data['convert_data']['convert_price'];
            } else {
                $discounts_amount = 0;
            }
            $data[] = [
                //订单号
                $item['order_no'],
                //产品ID
                $item['equity_package_id'],
                //产品名称
                $item['equity_package_name'],
                //产品类型
                $item['equity_package_category_name'],
                //用户ID
                UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']),
                //姓名
                $item['resume_name'],
                //支付金额
                $item['real_amount'],
                //优惠金额
                $discounts_amount,
                //支付状态
                isset(BaseResumeOrder::STATUS_LIST[$item['status']]) ? BaseResumeOrder::STATUS_LIST[$item['status']] : '',
                //支付方式
                isset(BaseResumeOrder::PAYWAY_LIST[$item['payway']]) ? BaseResumeOrder::PAYWAY_LIST[$item['payway']] : '',
                //下单时间
                $item['add_time'] && $item['add_time'] != '0000-00-00 00:00:00' ? $item['add_time'] : '',
                //支付时间
                $item['pay_time'] && $item['pay_time'] != '0000-00-00 00:00:00' ? $item['pay_time'] : '',
                //流水号
                $item['trade_no'],
                //下单渠道
                isset(BaseResumeOrder::PLATFORM_LIST[$item['platform']]) ? BaseResumeOrder::PLATFORM_LIST[$item['platform']] : '',
                //备注
                $item['remark'],
            ];
        }
        //释放资源
        unset($list);

        return [
            'data'     => $data,
            'headers'  => $headers,
            'fileName' => $fileName,
        ];
    }
}