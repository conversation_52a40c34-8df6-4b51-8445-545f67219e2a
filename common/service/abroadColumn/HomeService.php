<?php

namespace common\service\abroadColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseJob;
use common\base\models\BaseJobColumn;
use common\base\models\BaseMajor;
use common\base\models\BaseShowcase;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use QL\Dom\Query;
use yii\helpers\BaseFileHelper;

class HomeService extends BaseService
{
    public $selectList;

    public function getPublicActivitySelect()
    {
        $this->selectList = [
            'a.id',
            'ap.img_file_id',
            'a.series_type as seriesType',
            'a.sub_type as subType',
            'a.type',
            'a.name as title',
            'a.introduce as description',
            'a.activity_start_date',
            'a.activity_end_date',
            'a.detail_url as url',
            'a.sign_up_url',
            'a.is_outside_url',
            'a.activity_child_status',
            'a.activity_link',
        ];
    }

    public function getAll($isUpdateCache = false)
    {
        if (!$isUpdateCache) {
            $data = $this->getCache(self::CACHE_KEY_HOME);

            if ($data) {
                return $data;
            }
        }

        $banner                                 = $this->getBanner();
        $homeRecommendedActivities              = $this->getRecommendedActivities();
        $homeOverseasTalentAttractionActivities = $this->getOverseasTalentAttractionActivities();
        $homeComeBackActivities                 = $this->getComeBackActivities();
        $homeOverseasExcellentYouth             = $this->getOverseagetsExcellentYouth();
        $homeRenownedEmployer                   = $this->getRenownedEmployer();
        $homeGlobalAnnouncement                 = $this->getGlobalAnnouncement();
        $homeCollaborationCases                 = $this->getCollaborationCases();
        $homeDiscoveryPosition                  = $this->getDiscoveryJob();

        $data = [
            'banner'                             => $banner,
            'recommendedActivities'              => $homeRecommendedActivities,
            'overseasTalentAttractionActivities' => $homeOverseasTalentAttractionActivities,
            'comeBackActivities'                 => $homeComeBackActivities,
            'overseasExcellentYouth'             => $homeOverseasExcellentYouth,
            'globalAnnouncement'                 => $homeGlobalAnnouncement,
            'renownedEmployer'                   => $homeRenownedEmployer,
            'collaborationCases'                 => $homeCollaborationCases,
            'discoveryPosition'                  => $homeDiscoveryPosition,
        ];

        return $this->setCache(self::CACHE_KEY_HOME, $data);
    }


    // =======首页部分开始=======
    // 首页banner(单纯就是广告位)
    public function getBanner()
    {
        $key = 'hw_shouye_HF';

        return $this->getCommonShowcaseList($key);
    }


    // 首页推荐活动
    //1、展示勾选了“首页-推荐活动”推广位、且在推广时间内的活动，最多5个；
    //    若无符合条件的活动，则整个模块隐藏；
    //    （PS：活动上下架状态不影响该位置的活动调用）
    //2、排序：
    //（1）按当前活动在“首页-推荐活动”推广位中的“推广排序”倒序排列；
    //（2）其次按活动ID倒序排；
    public function getRecommendedActivities()
    {
        //首页推荐模块比较特别，参数不同
        $select = [
            'a.id',
            'ap.img_file_id',
            'a.series_type as seriesType',
            'a.sub_type as subType',
            'a.type',
            'a.name as title',
            'a.other_description_one as otherDescriptionOne',
            'a.introduce as description',
            'a.activity_start_date',
            'a.activity_end_date',
            'a.detail_url as url',
            'a.is_outside_url',
            'a.activity_child_status',
            'a.activity_link',
        ];

        return $this->getActivityListByPromotion(BaseHwActivityPromotion::PROMOTION_POSITION_INDEX_RECOMMEND, 5,
            $select);
    }


    // 首页出海引才

    // 活动调用，通过【运营端->高才海外->活动管理->新增/编辑活动->“推广设置”】中配置：
    // 1、展示勾选了“首页-出海引才”推广位、且在推广时间内的活动，最多6个；
    //     若无符合条件的活动，则整个模块隐藏；
    //     （PS：活动上下架状态不影响该位置的活动调用）
    // 2、排序：
    // （1）按当前活动在“首页-出海引才”推广位中的“推广排序”倒序排列；
    // （2）其次按活动ID倒序排；
    // 3、字段说明：见卡片标注说明；
    // 4、移入交互&点击热区说明：
    // （1）移入交互：鼠标移入卡片时，显示移入效果（描边+投影+弹起）；
    // （2）热区①-图片+活动logo+活动名称：点击整个热区，新页面打开配置的“活动详情链接”；
    // （3）热区②-操作按钮：
    //        【立即报名】/【待举办】/【正在进行】按钮，点击，新页面打开配置的“活动报名链接”；
    //        【已结束】按钮，点击无交互；
    // （4）点击【查看更多】，跳转【出海引才】页面；
    public function getOverseasTalentAttractionActivities()
    {
        return $this->getActivityListByPromotion(BaseHwActivityPromotion::PROMOTION_POSITION_OVERSEAS_RECRUITMENT, 6);
    }

    // 首页归国活动

    /**
     * “首页-归国活动-上”推广位
     * 1、展示勾选了“首页-归国活动-上”推广位、且在推广时间内的活动，最多2个；
     * 若无符合条件的活动，则整个模块隐藏；
     * （PS：活动上下架状态不影响该位置的活动调用）
     * 2、排序：
     * （1）按当前活动在“首页-归国活动-上”推广位中的“推广排序”倒序排列；
     * （2）其次按活动ID倒序排；
     * 3、字段说明：见卡片标注说明；
     * 4、移入交互&点击热区说明：
     * （1）移入交互：鼠标移入卡片时，显示移入效果（描边+投影+弹起）；
     * （2）热区①-整张活动卡片：点击整个卡片热区，新页面打开配置的“活动详情链接”；
     * （3）热区②-操作按钮：
     * 【立即报名】/【待举办】/【正在进行】按钮，点击，新页面打开配置的“活动报名链接”；
     * 【已结束】按钮，点击无交互；
     * 4
     * 4.1
     * “首页-归国活动-下”推广位
     * 1、展示勾选了“首页-归国活动-下”推广位、且在推广时间内的活动，最多4个；
     * 若无符合条件的活动，则整个模块隐藏；
     * （PS：活动上下架状态不影响该位置的活动调用）
     * 2、排序：
     * （1）按当前活动在“首页-归国活动-下”推广位中的“推广排序”倒序排列；
     * （2）其次按活动ID倒序排；
     * 3、字段说明：见卡片标注说明；
     * 4、移入交互&点击热区说明：
     * （1）移入交互：鼠标移入卡片时，显示移入效果（描边+投影+弹起）；
     * （2）热区①-活动名称上方区域（即图片+活动日期+活动名称）：点击整个热区，新页面打开配置的“活动详情链接”；
     * （3）热区②-操作按钮：
     * 【立即报名】/【待举办】/【正在进行】按钮，点击，新页面打开配置的“活动报名链接”；
     * 【已结束】按钮，点击无交互；
     * 5、点击【查看更多】，跳转【归国活动】页面；
     */
    public function getComeBackActivities()
    {
        $topList = $this->getActivityListByPromotion(BaseHwActivityPromotion::PROMOTION_POSITION_COME_HOME_PART_TOP, 2);

        $bottomList = $this->getActivityListByPromotion(BaseHwActivityPromotion::PROMOTION_POSITION_COME_HOME_PART_BOTTOM,
            4);

        return [
            'topList'    => $topList,
            'bottomList' => $bottomList,
        ];
    }

    // 首页海外优青
    // 勾选了 首页-海优 合作单位的公告 的在线&已下线公告（不含已隐藏）展示，最多9个；
    // 若无符合条件的公告，则整个模块隐藏；
    // 三、排序
    // 1. 优先按公告状态排：在线＞已下线；
    // 2.其次，按发布时间倒序排列；
    // 需要的字段有 公告的标题、公告的连接、公告的亮点、发布时间、地区、单位、单位logo，图片的alt属性、查看更多的连接
    public function getOverseagetsExcellentYouth(): array
    {
        $att = BaseArticleAttribute::ATTRIBUTE_HOME_HAIYOU;

        $list = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'a.id = c.article_id')
            ->innerJoin(['d' => BaseCompany::tableName()], 'b.company_id = d.id')
            ->select([
                'b.title',
                'b.id',
                'a.refresh_date',
                'a.refresh_time',
                'b.highlights_describe',
                'b.company_id',
                //                'd.full_name as companyName',
                //                'd.logo_url as companyLogo',
            ])
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'a.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'a.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'c.type'      => [
                    $att,
                ],
                // 'd.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->orderBy('b.status desc,sort_time desc,a.refresh_date desc,b.id desc')
            ->limit(9)
            ->asArray()
            ->all();
        if (!$list) {
            return [];
        }
        foreach ($list as &$item) {
            $item['address']      = BaseAnnouncementAreaRelation::getCityTextByAnnouncementId($item['id']);
            $item['btnText']      = '立即查看';
            $item['url']          = UrlHelper::createPcAnnouncementDetailPath($item['id']);
            $item['refresh_date'] = TimeHelper::formatDateByYear($item['refresh_date'], '.');
            $item['address']      = BaseAnnouncementAreaRelation::getCityTextByAnnouncementId($item['id']);
            $companyInfo          = BaseCompany::findOne($item['company_id']);
            $item['companyName']  = $companyInfo->full_name;
            $item['companyLogo']  = BaseCompany::getLogoFullUrl($companyInfo->logo_url);
            $item['date']         = $item['refresh_date'];
        }

        return $list;
    }

    // 首页知名雇主 广告位
    public function getRenownedEmployer()
    {
        $key = 'hw_zhimingguzhu';

        $list = BaseShowcase::getByKey($key);
        foreach ($list as &$item) {
            $companyInfo         = BaseCompany::findOne($item['company_id']);
            $item['companyName'] = $companyInfo['full_name'];
            $item['companyLogo'] = $companyInfo['logo_url'];
            //获取单位类型
            $type = BaseDictionary::getCompanyTypeName($companyInfo['type']);
            //获取单位性质
            $nature = BaseDictionary::getCompanyNatureName($companyInfo['nature']);
            //获取单位所在地
            $area = BaseArea::getAreaName($companyInfo['city_id']);
            //所在城市/单位类型/单位性质
            $item['companyInfo'] = $area . '|' . $type . '|' . $nature;
            if (!$item['sub_title']) {
                // 拿单位简介
                $item['sub_title'] = BaseCompany::findOneVal(['id' => $item['company_id']], 'introduce');
            }
            $item['description'] = $item['sub_title'];
            $item['image']       = $item['image_link'] ?: '';
            $item['url']         = $item['url'] ?: '';
        }

        return $list;
    }

    // 首页全球求贤 栏目，
    // 一、调用规则：
    //1、“属性【焦点】+合作单位+单位类型+职位类型+学历/职称”  的在线公告（不含已下线、已隐藏）
    //二、排序：
    //1、优先排【首页公告-置顶】属性的在线公告
    //2、按发布时间倒序排列；
    //最多展示8条公告（含规则调用、【首页公告-置顶】属性调用的公告）；
    public function getGlobalAnnouncement()
    {
        $att = BaseArticleAttribute::ATTRIBUTE_HOME_ANNOUNCEMENT_TOP;

        // 按照这个属性去找8条符合的公告(考虑到下面还有可能去拿公告的相关id，所以现在只用id)
        $topIdList = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'a.id = c.article_id')
            ->select([
                'b.id',
            ])
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'a.type'      => BaseArticle::TYPE_ANNOUNCEMENT,
                'a.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'c.type'      => [
                    $att,
                ],
            ])
            ->orderBy('b.status desc,sort_time desc,a.refresh_date desc,b.id desc')
            ->limit(4)
            ->column();

        $count = 8 - count($topIdList);

        if ($count > 0) {
            // 这里需要找到手动勾选了副栏目是海外的公告
            // $notIncludeAnnouncementIds = BaseAnnouncement::find()
            //     ->select('a.id')
            //     ->alias('a')
            //     ->innerJoin(['b' => BaseArticle::tableName()], 'b.id = a.article_id')
            //     ->innerJoin(['c' => BaseArticleColumn::tableName()], 'c1.article_id=b.id')
            //     ->select([
            //         'a.id',
            //     ])
            //     ->where([
            //         'or',
            //         ['b.home_sub_column_ids' => BaseHomeColumn::ABROAD_QIUXIAN_ID],
            //         ['b.home_column_id' => BaseHomeColumn::ABROAD_QIUXIAN_ID],
            //     ])
            //     ->column();

            $columnId    = BaseHomeColumn::ABROAD_QIUXIAN_ID;
            $otherIdList = BaseAnnouncement::find()
                ->select('a.id')
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'b.id = a.article_id')
                ->innerJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=b.id')
                ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
                ->innerJoin(['e' => BaseCompany::tableName()], 'e.id = a.company_id')
                ->select([
                    'a.id',
                ])
                ->where([
                    'a.status'         => BaseAnnouncement::STATUS_RECRUIT_ONLINE,
                    'd.type'           => BaseArticleAttribute::ATTRIBUTE_FOCUS,
                    'e.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $topIdList,
                ])
                ->andWhere(['c.column_id' => $columnId])
                ->orderBy('a.status desc,refresh_date desc,a.id desc')
                ->groupBy('a.id')
                ->asArray()
                ->limit($count)
                ->column();

            $announcementIds = ArrayHelper::merge($topIdList, $otherIdList);
        }

        // 这里再循环去找合适的内容
        $query = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['an' => BaseAnnouncement::tableName()], 'a.id = an.article_id')
            ->where(['an.id' => $announcementIds])
            ->select([
                'an.title',
                'an.id',
                'a.refresh_date',
                'an.highlights_describe',
            ]);

        if ($announcementIds) {
            $query->orderBy((['FIELD(an.id,' . implode(',', $announcementIds) . ')' => SORT_ASC]));
        }

        $list = $query->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['tag'] = '';
            if (in_array($item['id'], $topIdList)) {
                $item['tag'] = '推荐';
            }
            $item['url']         = UrlHelper::createPcAnnouncementDetailPath($item['id']);
            $refreshTime         = strtotime($item['refresh_date']);
            $item['refreshDate'] = date('m.d', $refreshTime);
            $item['refreshYear'] = date('Y', $refreshTime);
            $item['address']     = BaseAnnouncement::getOneCityName($item['id']);
            //获取招聘人数
            $key                   = Cache::ALL_ANNOUNCEMENT_JOB_AMOUNT_KEY . ':' . $item['id'];
            $recruitAmount         = Cache::get($key);
            $item['recruitAmount'] = $recruitAmount ?: BaseJob::getAnnouncementJobRecruitAmount($item['id']);
            //获取公告下的职位数量
            $item['jobAmount'] = BaseJob::getAnnouncementJobAmount($item['id']);
        }

        return $list;
    }

    // 首页发现职位 拿求贤公告里面匹配的职位里面最新发布的一条，同一个单位只拿一条
    public function getDiscoveryJob()
    {
        // 找到全部求贤公告的id?
        $columnId = BaseHomeColumn::ABROAD_QIUXIAN_ID;
        $att      = BaseArticleAttribute::ATTRIBUTE_FOCUS;
        // $list = BaseAnnouncement::find()
        //     ->select('a.id')
        //     ->alias('a')
        //     ->innerJoin(['b' => BaseArticle::tableName()], 'b.id = a.article_id')
        //     ->innerJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id=b.id')
        //     ->where([
        //         'a.status' => BaseAnnouncement::STATUS_RECRUIT_ONLINE,
        //     ])
        //     ->andWhere(['c.column_id' => $columnId])
        //     ->orderBy('a.status desc,refresh_date desc,a.id desc')
        //     ->asArray()
        //     ->all();
        //
        // $baseKey    = Cache::ALL_ABROAD_QIUXIAN_ANNOUNCEMENT_MATCH_JOB_KEY;
        // $allJobList = [];
        // foreach ($list as $item) {
        //     $key         = $baseKey . ':' . $item['id'];
        //     $jobListJson = Cache::get($key);
        //
        //     if ($jobListJson) {
        //         $jobList = json_decode($jobListJson, true);
        //
        //         if ($jobList) {
        //             $allJobList = array_merge($allJobList, $jobList);
        //         }
        //     }
        // }
        // 这里还要看字段
        // 因为要单位的最新公告在前面，所以先按做一个公告子表
        $limitSql = BaseJob::find()
            ->alias('a')
            ->select([
                'a.id',
                'name as jobName',
                'a.refresh_time',
                'wage_type',
                'min_wage',
                'max_wage',
                'a.city_id',
                'amount',
                'a.company_id',
                'major_id',
            ])
            ->innerJoin(['b' => BaseJobColumn::tableName()], 'b.job_id = a.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
            ->innerJoin(['d' => BaseAnnouncement::tableName()], 'd.id = a.announcement_id')
            ->innerJoin(['e' => BaseArticleAttribute::tableName()], 'd.article_id = e.article_id')
            ->where([
                'a.status'         => BaseJob::STATUS_ONLINE,
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                'b.column_id'      => $columnId,
                'e.type'           => $att,
            ])
            ->orderBy('a.refresh_time desc,c.id asc')
            ->limit(1000);

        $list = (new \yii\db\Query())->from(['c' => $limitSql])
            ->groupBy('c.company_id')
            ->orderBy('c.refresh_time desc,c.id asc')
            ->limit(9)
            ->all();

        // 以上面为子表然后再按照单位分组拿9个

        // $list = BaseJob::find()
        //     ->alias('a')
        //     ->select([
        //         'a.id',
        //         'name as jobName',
        //         'a.refresh_time',
        //         'wage_type',
        //         'min_wage',
        //         'max_wage',
        //         'a.city_id',
        //         'amount',
        //         'a.company_id',
        //         'major_id',
        //     ])
        //     ->innerJoin(['b' => BaseJobColumn::tableName()], 'b.job_id = a.id')
        //     ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
        //     ->innerJoin(['d' => BaseAnnouncement::tableName()], 'd.id = a.announcement_id')
        //     ->innerJoin(['e' => BaseArticleAttribute::tableName()], 'd.article_id = e.article_id')
        //     ->where([
        //         'a.status'         => BaseJob::STATUS_ONLINE,
        //         'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
        //         'b.column_id'      => $columnId,
        //         'e.type'           => $att,
        //     ])
        //     ->union($limitSql)
        //     ->orderBy('a.refresh_time desc')
        //     ->limit(9)
        //     ->asArray()
        //     ->all();

        foreach ($list as &$item) {
            $item['wage']          = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            $item['address']       = BaseArea::getAreaName($item['city_id']);
            $item['recruitAmount'] = '招' . $item['amount'] . '人';
            $item['companyName']   = BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name');
            $item['companyLogo']   = BaseCompany::getLogoFullUrl(BaseCompany::findOneVal(['id' => $item['company_id']],
                'logo_url'));
            $item['refreshDate']   = TimeHelper::formatDateByYear($item['refresh_time'], '.');
            $item['url']           = UrlHelper::createPcJobDetailPath($item['id']);
            $majorList             = explode(',', $item['major_id']);
            if (count($majorList) == 1) {
                $item['major'] = BaseMajor::getMajorName($majorList[0]);
            } else {
                $item['major'] = BaseMajor::getMajorName($majorList[0]) . '等';
            }
        }

        return $list;
    }

    // 首页合作案例 广告位
    public function getCollaborationCases()
    {
        $key = 'hw_hezuoanli';

        $info = BaseShowcase::getByKey($key, 3);
        $list = [];
        foreach ($info as $item) {
            $list[] = [
                'id'          => $item['id'],
                'number'      => $item['number'],
                'logoImage'   => $item['image_url'],
                'title'       => $item['title'],
                'description' => $item['sub_title'],
                'otherImage'  => $item['other_image_url'],
                'url'         => $item['url'] ?: '',
                'rel'         => 'nofollow',
            ];
        }

        return $list;
    }

    /**
     * 根据推广位置，获取活动列表
     * @param $position
     * @param $limit
     * @param $select
     * @return array
     */
    public function getActivityListByPromotion($position, $limit, $select = []): array
    {
        $this->getPublicActivitySelect();
        $selectList = $select ?: $this->selectList;
        $list       = BaseHwActivityPromotion::find()
            ->alias('ap')
            ->innerJoin(['a' => BaseHwActivity::tableName()], 'a.id=ap.activity_id')
            ->select($selectList)
            ->where([
                'ap.status' => BaseHwActivityPromotion::STATUS_ONLINE,
                'a.status'  => BaseHwActivity::STATUS_ACTIVE,
            ])
            ->andFilterWhere(['position_type' => $position])
            ->orderBy('ap.sort desc,a.id desc')
            ->groupBy('a.id')
            ->asArray()
            ->limit($limit)
            ->all();
        if (!$list) {
            return [];
        }
        //完善参数
        foreach ($list as &$item) {
            if ($position == BaseHwActivityPromotion::PROMOTION_POSITION_INDEX_RECOMMEND && empty($item['otherDescriptionOne'])) {
                $item['otherDescriptionOne'] = $item['title'] ?? '';
            }

            $item['image'] = FileHelper::getFullPathById($item['img_file_id']);
            $item['logo']  = BaseHwActivity::getLogo($item['id']);
            //只有“首页-推荐活动”这个模块，是用的系列，其他地方用的都是类型
            if ($position == BaseHwActivityPromotion::PROMOTION_POSITION_INDEX_RECOMMEND) {
                $item['tag'] = BaseHwActivity::SERIES_TEXT_LIST[$item['seriesType']];
            } else {
                $item['tag'] = BaseHwActivity::TYPE_TEXT_LIST[$item['type']];
            }
            $item['date']    = BaseHwActivity::getActivityDate($item['id'], 4);
            $item['address'] = BaseHwActivity::getAddressBySeries($item['id']);
            if ($item['is_outside_url'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
            $item['btnDisabled'] = $item['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END;
            $item['btnText']     = BaseHwActivity::ACTIVITY_BTN_TEXT_LIST[$item['activity_child_status']];

            //////+++注意这一块有数据重置，请放到最后面+++////////////////////////////////////////////////////
            //////+++注意这一块有数据重置，请放到最后面+++////////////////////////////////////////////////////
            //////+++注意这一块有数据重置，请放到最后面+++////////////////////////////////////////////////////
            //////+++注意这一块有数据重置，请放到最后面+++////////////////////////////////////////////////////
            //////+++注意这一块有数据重置，请放到最后面+++////////////////////////////////////////////////////
            //招聘会
            if (in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_TYPE)) {
                if ($item['subType'] > 0 && $position == BaseHwActivityPromotion::PROMOTION_POSITION_INDEX_RECOMMEND) {
                    $item['tag'] = BaseHwActivity::SERIES_TEXT_LIST[BaseHwActivity::SUB_TYPE_PARENT_LIST[$item['subType']]];
                } else {
                    $item['tag'] = $item['subType'] > 0 ? BaseHwActivity::SUB_TYPE_TEXT_LIST[$item['subType']] : BaseHwActivity::TYPE_TEXT_LIST[$item['type']];
                }

                $urlInfo             = BaseHwActivity::getPersonApplyLinkUrl($item['id']);
                $item['url']         = BaseHwActivity::getActivityLinkUrl($item['seriesType'], $item['activity_link']);
                $item['sign_up_url'] = $urlInfo['link'];
                $item['btnText']     = $item['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END ? '已结束' : '立即报名';
                $item['type']        = BaseHwActivity::SUB_TYPE_PARENT_LIST[$item['subType']];
            } else {
                // 最终需要返回给前端type 是 seriesType ，因为决定了颜色
                $item['type'] = $item['seriesType'];
            }
            /////////////////////+++++++++++++++++++++++++++++++++++++++++/////////////////////////////////
            /////////////////////+++++++++++++++++++++++++++++++++++++++++/////////////////////////////////
            /////////////////////+++++++++++++++++++++++++++++++++++++++++/////////////////////////////////
            /////////////////////+++++++++++++++++++++++++++++++++++++++++/////////////////////////////////
        }

        return $list;
    }

}
