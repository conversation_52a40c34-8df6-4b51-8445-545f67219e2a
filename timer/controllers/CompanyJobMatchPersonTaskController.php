<?php
/**
 * create user：gaocai
 * create time：2024/1/16 16:10
 */
namespace timer\controllers;

use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use queue\Producer;

class CompanyJobMatchPersonTaskController extends BaseTimerController
{
    //最近三十天内登录的账号
    private $lastLoginDay = 30;

    /**
     * php timer_yii company-job-match-person-task/run
     */
    public function actionRun()
    {
        //获取$this->lastLoginDay时间内的单位ID
        //计算一下开始开始时间
        $time = time() - $this->lastLoginDay * 24 * 3600;
        //时间格式化
        $time       = date('Y-m-d H:i:s', $time);
        $companyIds = BaseCompany::find()
            ->alias('c')
            ->select('c.id')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = c.member_id')
            ->andWhere([
                '>',
                'm.last_login_time',
                $time,
            ])
            ->andWhere([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                'c.status'         => BaseCompany::STATUS_ACTIVE,
                'm.type'           => BaseMember::TYPE_COMPANY,
                'm.status'         => BaseMember::STATUS_ACTIVE,
            ])
            ->asArray()
            ->column();

        //在线的职位ID推进队列
        foreach ($companyIds as $companyId) {
            self::log('推进单位ID：' . $companyId . '到队列');
            Producer::companyJobMatchPerson($companyId);
        }

        self::log('完成今天单位职位匹配人才任务，结束执行！');
    }
}