<?php

namespace common\base;

use common\helpers\FormatConverter;
use common\helpers\IpHelper;
use common\libs\Cache;
use yii;
use yii\web\Controller;

/**
 * Site controller
 */
class BaseController extends Controller
{
    public $enableCsrfValidation = false;

    public $startTime;
    public $endTime;
    public $ip;

    public function behaviors()
    {
        $this->startTime = microtime(true);

        $ip = IpHelper::getIp();

        if (Yii::$app->params['ipBlacklist']) {
            // 黑名单机制
            if (in_array($ip, Yii::$app->params['ipBlacklist'])) {
                yii::error('ip:' . $ip . ' is in blacklist');
                exit('非法操作');
            }
        }

        // 获取停服参数
        $isStop = Yii::$app->params['isStop'];
        if ($isStop) {
            Yii::$app->response->setStatusCode(503)
                ->send();
            echo Yii::$app->view->renderFile('@frontendPc/views/home/<USER>');
            exit();
        }

        // 后置的一些设置
        $this->ip = $ip;
    }

    public function afterAction($action, $result)
    {
        if (Yii::$app->params['isLogUa']) {
            // 在这里做一个ua纪录机制,纪录每天的ua总数量
            $controllerId = \Yii::$app->controller->id;
            $actionId     = \Yii::$app->controller->action->id;
            $action       = $controllerId . '/' . $actionId;
            // 做一些不需要记录的额外api
            if (in_array($action, [
                'api/config/load-country-mobile-code',
                'api/member/get-captcha-config',
                'api/config/load-country-mobile-code',
                'api/member/check-login-qrcode',
            ])) {
                return $result;
            }
            try {
                $ua                 = Yii::$app->request->getUserAgent();
                $searchData         = \Yii::$app->request->get();
                $host               = Yii::$app->request->getHostInfo();
                $searchData['host'] = $host;

                if (!Yii::$app->user->isGuest) {
                    $userId   = Yii::$app->user->id;
                    $platform = PLATFORM;
                } else {
                    $userId   = 0;
                    $platform = '';
                }

                $data = [
                    'ua'         => $ua,
                    'ip'         => $this->ip,
                    'controller' => $action,
                    'searchData' => $searchData,
                    'time'       => CUR_DATETIME,
                    'userId'     => $userId,
                    // 保留两位小数
                    'duration'   => number_format(microtime(true) - $this->startTime, 2),
                    'platform'  => $platform,
                ];
                $json = json_encode($data);
                $key  = 'TMP:CONTROLLER_UA';
                Cache::lpush($key, $json);
            } catch (\Exception $e) {
                $ua = '';
            }
        }

        return parent::afterAction($action, $result);
    }

    /**
     * --------------------------------------------------
     * AJAX请求返回
     * --------------------------------------------------
     *
     * 场景说明：
     * - 用户预期请求得到实现，返回result=1、提示信息、跳转地址
     * - 用户预期请求得到实现，返回result=1、提示信息、不跳转但需要js操作
     * - 用户预期请求得到实现，返回result=1、不跳转但需要js操作
     * - 用户预期请求没有实现，返回result=0、错误提示（如“请先登录”）
     * - 用户预期请求没有实现，返回result=0、错误提示、跳转地址
     *
     * 1、如果只有一个string参数，则表示返回错误信息，如result('验证码错误') <br>
     * 2、如果有两个string参数，则表示返回成功信息和跳转URL，如result('操作成功', $url) <br>
     * 3、如果第二个参数是array，则表示返回自定义数据$data
     * 4、如果需要返回自定义数据$data，则必需要三个参数
     *
     * @param string $msg 返回提示信息，必填
     * @param        $result
     *
     * @return yii\console\Response|yii\web\Response
     */
    public function result($msg, $result = null, $data = null)
    {
        $ret = [
            'msg'    => $msg,
            'result' => $result,
            'data'   => $data,
        ];
        if ($result === null) {
            $ret['result'] = 0;
        } elseif (is_string($result)) {
            $ret['result'] = 1;
        } else {
            $ret['result'] = $result;
        }

        $response         = Yii::$app->response;
        $response->format = \yii\web\Response::FORMAT_JSON;
        $response->data   = $ret;

        return $response;
    }

    public function notLogin()
    {
        return json_encode([
            'code'   => 403,
            'result' => 0,
            'msg'    => '未登录用户',
        ]);
    }

    /**
     * --------------------------------------------------
     * AJAX成功返回
     * --------------------------------------------------
     *
     */
    public function success($data = null, $msg = '')
    {
        // 如果第一个参数是字符串,就只要提示信息返回
        if (is_string($data)) {
            $msg = $data;

            return $this->result($msg, 1, []);
        }

        // 什么都不传,两个都是空的
        if ($data == null) {
            $msg = '';
            if (Yii::$app->request->isPost) {
                $msg = '操作成功';
            }

            return $this->result($msg, 1, []);
        }

        // 第一个是数组或者对象的时候,这个时候是需要返回数据的

        return $this->result($msg, 1, FormatConverter::convertLine($data));
    }

    public function fail($msg = null)
    {
        $msg = $msg ?: '非法操作';

        return $this->result($msg);
    }

    /**
     * 方便与获取get和post都有的参数
     * @param      $str
     * @param null $default
     * @return array|mixed
     */
    public function request($str, $default = null)
    {
        if (Yii::$app->request->isGet) {
            return Yii::$app->request->get($str, $default);
        }

        return Yii::$app->request->post($str, $default);
    }

    // 返回500的HTTP状态码等错误状态码,这个页面
    public function error($msg, $code = 500)
    {
        $response             = Yii::$app->response;
        $response->statusCode = $code;
        $response->format     = yii\web\Response::FORMAT_JSON;
        $ret                  = [
            'msg'    => $msg,
            'result' => 0,
        ];
        $response->data       = $ret;

        return $response;
    }

    public function badPage()
    {
        $response             = Yii::$app->response;
        $response->statusCode = 503;
        $response->format     = yii\web\Response::FORMAT_JSON;

        return $response;
    }

    /**
     * 用于格式化一些分页所需的参数
     *
     * @param int $pageSize 每页多少条
     *
     * 返回的数组里面
     *  offset offset使用的
     *  limit  limit使用的
     *  total  总的页数
     */
    public static function setPage($count, $page, $pageSize)
    {
        return [
            'offset' => ($page - 1) * $pageSize,
            'limit'  => $pageSize,
            'total'  => ceil($count / $pageSize),
        ];
    }

    /**
     * 检查参数是否完整
     * @param mixed ...$params
     * @return bool
     */
    public function checkParam(...$params)
    {
        foreach ($params as $param) {
            if (!$param) {
                return false;
            }
        }

        return true;
    }
}
