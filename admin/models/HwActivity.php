<?php

namespace admin\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwActivityCompanyHot;
use common\base\models\BaseHwActivityFeatureTagRelation;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwActivitySessionArea;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseHwSpecialActivityRelation;
use common\base\models\BaseHwSpecialActivitySessionArea;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\service\hwActivity\HwService;
use common\service\zhaoPinHuiColumn\BaseService;
use common\service\zhaoPinHuiColumn\SpecialActivityService;
use queue\HwActivityCompany;
use queue\Producer;
use Yii;
use yii\db\conditions\AndCondition;
use yii\db\Exception;
use yii\db\Query;

class HwActivity extends BaseHwActivity
{
    /**
     * 查询列表
     * @param $params
     * @return array
     */
    public static function search($params)
    {
        $select = [
            'a.id as activityId',
            'a.name as activityName',
            'a.series_type as seriesType',
            'a.detail_url as detailUrl',
            'a.activity_link as activityLink',
            'a.tags',
            'a.type',
            'a.to_hold_type as toHoldType',
            'a.add_time as addTime',
            'a.activity_start_date as activityStartDate',
            'a.activity_end_date as activityEndDate',
            'a.sign_end_date as signEndDate',
            'a.sign_custom_end_date as signCustomEndDate',
            'a.grounding_status as groundingStatus',
            'a.sort',
            'a.company_id as companyId',
            'a.activity_child_status as activityChildStatus',
            'a.participation_company_amount as participationCompanyAmount',
            'a.admin_id',
        ];
        $query  = self::find()
            ->alias('a')
            ->where(['a.status' => self::STATUS_ACTIVE]);
        //活动名称、id搜索
        $query->andFilterWhere(['a.id' => $params['activityId']]);
        $query->andFilterWhere([
            'like',
            'a.name',
            $params['activityName'],
        ]);

        //单位id搜索
        $query->andFilterWhere(['a.company_id' => $params['companyId']]);
        //模糊查询单位
        if ($params['companyName']) {
            $query->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id');
            $query->andWhere([
                'like',
                'full_name',
                $params['companyName'],
            ]);
        }

        // if ($params['type']) {
        //     //活动系列&活动类型(这里有一个比较奇怪的问题，因为前端是放在一个字段里面了，但是实际上数据库是两个字段，这里会涉及到一个问题，如果是0前缀的就是type，如果不是0前缀的就是series_type，并且有可能是数组，所以得分开来处理
        //     $typeList        = explode(',', $params['type']);
        //     $typeWhere       = [];
        //     $seriesTypeWhere = [];
        //     foreach ($typeList as $type) {
        //         if (strpos($type, '0') === 0) {
        //             $typeWhere[] = $type;
        //         } else {
        //             $seriesTypeWhere[] = $type;
        //         }
        //     }
        //     if ($typeWhere) {
        //         $query->andFilterWhere(['a.type' => $typeWhere]);
        //     }
        //     if ($seriesTypeWhere) {
        //         $query->andFilterWhere(['a.series_type' => $seriesTypeWhere]);
        //     }
        // }

        if ($params['type']) {
            $typeArray = explode(',', $params['type']);
            $query->andFilterWhere(['a.type' => $typeArray]);
        }
        //举办方式
        if ($params['toHoldType']) {
            $query->andFilterWhere([
                'a.to_hold_type' => [
                    $params['toHoldType'],
                    BaseHwActivity::TO_HOLD_TYPE_ONLINE_AND_OFFLINE,
                ],
            ]);
        }

        //活动状态
        $query->andFilterWhere([
            'a.activity_status' => $params['activityStatus'],
        ]);

        //上架状态
        $query->andFilterWhere(['a.grounding_status' => $params['groundingStatus']]);

        //创建时间
        if ($params['addTimeStart'] && $params['addTimeEnd']) {
            $query->andWhere([
                'between',
                'a.add_time',
                TimeHelper::dayToBeginTime($params['addTimeStart']),
                TimeHelper::dayToEndTime($params['addTimeEnd']),
            ]);
        }
        //活动开始时间
        if ($params['activityStartTimeStart'] && $params['activityStartTimeEnd']) {
            $query->andWhere([
                'between',
                'a.activity_start_date',
                TimeHelper::dayToBeginTime($params['activityStartTimeStart']),
                TimeHelper::dayToEndTime($params['activityStartTimeEnd']),
            ]);
        }
        //活动结束时间
        if ($params['activityEndTimeStart'] && $params['activityEndTimeEnd']) {
            $query->andWhere([
                'between',
                'a.activity_end_date',
                TimeHelper::dayToBeginTime($params['activityEndTimeStart']),
                TimeHelper::dayToEndTime($params['activityEndTimeEnd']),
            ]);
        }
        //报名截止时间
        if ($params['signTimeStart'] && $params['signTimeEnd']) {
            $query->andWhere([
                'between',
                'a.sign_end_date',
                TimeHelper::dayToBeginTime($params['signTimeStart']),
                TimeHelper::dayToEndTime($params['signTimeEnd']),
            ]);
        }
        //推广位置
        if ($params['promotionPosition']) {
            $query->innerJoin(['ap' => BaseHwActivityPromotion::tableName()], 'ap.activity_id = a.id');
            $query->andWhere(['ap.position_type' => $params['promotionPosition']]);
        }
        //活动标签
        $query->andFilterCompare('a.tags', $params['tags'], 'like');
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $order    = '';
        if ($params['sortAddTime'] == 1) {
            $order = 'a.add_time asc';
        } elseif ($params['sortAddTime'] == 2) {
            $order = 'a.add_time desc';
        }
        if ($params['sortSort'] == 1) {
            $order = 'a.sort asc';
        } elseif ($params['sortSort'] == 2) {
            $order = 'a.sort desc';
        }
        if (!$order) {
            $order = 'a.add_time desc';
        }
        $list = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($order)
            ->groupBy('a.id')
            ->asArray()
            ->all();
        if (!$list) {
            $result['list'] = [];

            $result['page'] = [
                'limit' => $pages['limit'],
                'count' => 0,
                'page'  => $pages['page'],
            ];

            return $result;
        }
        foreach ($list as &$item) {
            if ($item['tags']) {
                $tagsArr     = explode(',', $item['tags']);
                $typeNameArr = [];
                foreach ($tagsArr as $tagsItem) {
                    $itemName      = self::TAGS_TEXT_LIST[$tagsItem];
                    $typeNameArr[] = [
                        'type' => $tagsItem,
                        'name' => $itemName,
                    ];
                }
                $item['tagsList'] = $typeNameArr;
            } else {
                $item['tagsList'] = [];
            }

            //获取活动时间
            $item['activityDate']    = self::getActivityDate($item['activityId'], 1);
            $item['isZhaoPinHui']    = in_array($item['seriesType'], BaseHwActivity::ZHAOPINHUI_TYPE) ? 1 : 2;
            $item['activityNameUrl'] = in_array($item['seriesType'],
                BaseHwActivity::ZHAOPINHUI_UN_TYPE) ? $item['detailUrl'] : BaseHwActivity::getActivityLinkUrl($item['seriesType'],
                $item['activityLink']);
            //活动子状态
            $item['activityStatus'] = $item['activityChildStatus'] ? self::ACTIVITY_CHILD_TEXT_LIST[$item['activityChildStatus']] : '/';
            //活动上架状态
            $item['groundingStatus'] = self::GROUNDING_STATUS_TEXT_LIST[$item['groundingStatus']];
            $item['toHoldTypeText']  = $item['toHoldType'] ? self::TO_HOLD_TYPE_TEXT[$item['toHoldType']] : '';
            //单位名称
            $item['companyName'] = $item['companyId'] ? BaseCompany::findOneVal(['id' => $item['companyId']],
                'full_name') : '/';
            //推广位
            $item['promotionList'] = BaseHwActivityPromotion::getPromotionTypeTextList($item['activityId']);
            if ($item['signEndDate'] == TimeHelper::ZERO_DATE) {
                $item['signEndDate'] = $item['signCustomEndDate'];
            }
            $item['seriesType']        = self::SERIES_TEXT_LIST[$item['seriesType']];
            $item['type']              = self::TYPE_TEXT_LIST[$item['type']];
            $item['adminName']         = BaseAdmin::findOne($item['admin_id'])->name;
            $item['adminName']         = $item['adminName'] ?: '';
            $item['combinationFields'] = $item['toHoldTypeText'] . '/' . $item['seriesType'] . '-' . $item['type'];
        }

        $result['list'] = $list;

        $result['page'] = [
            'limit' => $pages['limit'],
            'count' => (int)$count,
            'page'  => $pages['page'],
        ];

        return $result;
    }

    /**
     * 获取编辑信息
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getInfo($id)
    {
        $activityInfo                                     = BaseHwActivity::find()
            ->where(['id' => $id])
            ->select([
                'id',
                'series_type',
                'sub_type',
                'tags',
                'type',
                'name',
                'introduce',
                'detail_url',
                'to_hold_type',
                'sign_up_url',
                'is_outside_url',
                'logo_file_id',
                'other_img_one_file_id',
                'other_img_two_file_id',
                'other_img_three_file_id',
                'other_description_one',
                'other_description_two',
                'other_description_three',
                'grounding_status',
                'sort',
                'company_id',
                'main_img_file_id',
                'sign_end_date',
                'sign_custom_end_date',
                'review_img_file_ids',
                'custom_feature_tag',
                'longitude',
                'latitude',
                'activity_organization',
                'activity_detail',
                'participation_method',
                'activity_highlights_title',
                'activity_highlights',
                'activity_benefits',
                'activity_benefits_content',
                'attendance_notes',
                'image_pc_banner_id',
                'image_service_code_id',
                'image_mini_master_id',
                'image_mini_banner_id',
                'image_notice_id',
                'apply_link_person_type',
                'apply_link_person_form_id',
                'apply_link_person_form_option_id',
                'apply_link_person',
                'apply_person_time',
                'apply_link_company',
                'apply_company_time',
                'template_id',
                'activity_link',
                'wonderful_review',
                'activity_number',
            ])
            ->asArray()
            ->one();
        $activityInfo['series_type']                      = (int)$activityInfo['series_type'];
        $activityInfo['sub_type']                         = $activityInfo['sub_type'] ?: '';
        $activityInfo['apply_link_person_type']           = $activityInfo['apply_link_person_type'] ?: '';
        $activityInfo['apply_link_person_form_id']        = $activityInfo['apply_link_person_form_id'] ?: '';
        $activityInfo['apply_link_person_form_option_id'] = $activityInfo['apply_link_person_form_option_id'] ?: '';
        $activityInfo['apply_link_person_form_option_id'] = $activityInfo['apply_link_person_form_option_id'] ?: '';
        $activityInfo['apply_person_time']                = $activityInfo['apply_person_time'] != TimeHelper::ZERO_DATE ? $activityInfo['apply_person_time'] : '';
        $activityInfo['apply_company_time']               = $activityInfo['apply_company_time'] != TimeHelper::ZERO_DATE ? $activityInfo['apply_company_time'] : '';
        $activityInfo['type']                             = (int)$activityInfo['type'];
        $activityInfo['logoImgUrl']                       = FileHelper::getFullPathById($activityInfo['logo_file_id']);
        $activityInfo['otherImgOneUrl']                   = FileHelper::getFullPathById($activityInfo['other_img_one_file_id']);
        $activityInfo['otherImgTwoUrl']                   = FileHelper::getFullPathById($activityInfo['other_img_two_file_id']);
        $activityInfo['otherImgThreeUrl']                 = FileHelper::getFullPathById($activityInfo['other_img_three_file_id']);
        $activityInfo['mainImgUrl']                       = FileHelper::getFullPathById($activityInfo['main_img_file_id']);

        $activityInfo['imagePcBannerUrl']    = FileHelper::getFullPathById($activityInfo['image_pc_banner_id']);
        $activityInfo['imageServiceCodeUrl'] = FileHelper::getFullPathById($activityInfo['image_service_code_id']);
        $activityInfo['imageMiniMasterUrl']  = FileHelper::getFullPathById($activityInfo['image_mini_master_id']);
        $activityInfo['imageMiniBannerUrl']  = FileHelper::getFullPathById($activityInfo['image_mini_banner_id']);
        $activityInfo['imageNoticeUrl']      = FileHelper::getFullPathById($activityInfo['image_notice_id']);

        $activityInfo['companyName'] = BaseCompany::findOneVal(['id' => $activityInfo['company_id']], 'full_name');
        $activityInfo['tags']        = $activityInfo['tags'] ? explode(',', $activityInfo['tags']) : [];

        $reviewImgList = json_decode($activityInfo['review_img_file_ids'], true) ?: [];
        foreach ($reviewImgList as &$item) {
            $item['imgUrl'] = FileHelper::getFullPathById($item['fileId']);
        }
        $activityInfo['review_img_file_ids'] = $reviewImgList;
        //获取场次信息
        if (in_array($activityInfo['series_type'], BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
            $activityInfo['sessionList'] = HwActivitySession::getEditInfoList($activityInfo['id']);
        } else {
            $activityInfo['sessionList'] = [];
            $activitySessionInfo         = BaseHwActivitySession::findOne(['activity_id' => $activityInfo['id']]);
            if ($activitySessionInfo) {
                $areaIdList                      = BaseHwActivitySessionArea::find()
                    ->where(['session_id' => $activitySessionInfo->id])
                    ->select(['area_id'])
                    ->column();
                $activityInfo['areaIds']         = $areaIdList;
                $activityInfo['isCustomTime']    = $activitySessionInfo->custom_time ? '1' : '2';
                $activityInfo['customTime']      = $activitySessionInfo->custom_time;
                $activityInfo['startDate']       = $activitySessionInfo->start_date != TimeHelper::ZERO_DATE ? $activitySessionInfo->start_date : '';
                $activityInfo['startTime']       = $activitySessionInfo->start_time;
                $activityInfo['endDate']         = $activitySessionInfo->end_date != TimeHelper::ZERO_DATE ? $activitySessionInfo->end_date : '';
                $activityInfo['endTime']         = $activitySessionInfo->end_time;
                $activityInfo['isCustomAddress'] = $activitySessionInfo->custom_address ? '1' : '2';
                $activityInfo['customAddress']   = $activitySessionInfo->custom_address;
                $activityInfo['detailAddress']   = $activitySessionInfo->detail_address;
            }
        }
        //获取推广设置
        $activityInfo['promotionList'] = BaseHwActivityPromotion::getAllPromotionList($activityInfo['id']);
        $activityInfo['sign_end_date'] = $activityInfo['sign_end_date'] == TimeHelper::ZERO_DATE ? '' : $activityInfo['sign_end_date'];
        if (!$activityInfo['company_id']) {
            $activityInfo['company_id'] = '';
        }
        $activityInfo['to_hold_type'] = $activityInfo['to_hold_type'] == BaseHwActivity::TO_HOLD_TYPE_ONLINE_AND_OFFLINE ? ArrayHelper::intToString([
            BaseHwActivity::TO_HOLD_TYPE_ONLINE,
            BaseHwActivity::TO_HOLD_TYPE_OFFLINE,
        ]) : [$activityInfo['to_hold_type']];

        //热门单位
        $activityInfo['companyHotList'] = BaseHwActivityCompanyHot::getActivityCompanyHotList($activityInfo['id']);

        //特色标签
        $activityInfo['featureTagId'] = BaseHwActivityFeatureTagRelation::getFeatureTag($activityInfo['id']);

        return $activityInfo;
    }

    /**
     * 获取专场查询数据
     */
    public static function getSpecialActivityParams()
    {
        $toHoldTypeList = BaseHwSpecialActivity::TO_HOLD_TYPE_TEXT_LIST;
        unset($toHoldTypeList[BaseHwSpecialActivity::TO_HOLD_TYPE_ALL]);

        return [
            'toHoldTypeList' => ArrayHelper::obj2Arr($toHoldTypeList),
            'statusList'     => ArrayHelper::obj2Arr(BaseHwSpecialActivity::STATUS_TEXT_LIST),
        ];
    }

    public static function getSpecialActivityList($params)
    {
        if (!empty($params['toHoldType'])) {
            $params['toHoldType'] = [
                $params['toHoldType'],
                BaseHwSpecialActivity::TO_HOLD_TYPE_ALL,
            ];
        }
        // and条件
        $andWhere = BaseActiveRecord::getSearchCondition($params, [
            [
                'startAddTime',
                '>=',
                'add_time',
            ],
            [
                'endAddTime',
                '<=',
                'add_time',
            ],
            [
                'startStartDate',
                '>=',
                'start_date',
            ],
            [
                'endStartDate',
                '<=',
                'start_date',
            ],
            [
                'startEndDate',
                '>=',
                'end_date',
            ],
            [
                'endEndDate',
                '<=',
                'end_date',
            ],
            ['status'],
            [
                'toHoldType',
                'in',
                'to_hold_type',
            ],
        ]);

        // or条件
        $orWhere = BaseActiveRecord::getSearchCondition($params, [
            [
                'name',
                'like',
                'name',
            ],
            [
                'name',
                'like',
                'id',
            ],
        ], 'or');

        // 排序
        $order = BaseActiveRecord::getOrderByInParams($params, [
            [
                'sortAddTime',
                'add_time',
            ],
        ]);

        $orderBy = implode(', ', $order);
        $query   = BaseHwSpecialActivity::find();
        $query   = $query->select([
            'id',
            'name',
            'to_hold_type',
            'add_time',
            'status',
            'custom_time',
            'start_date',
            'end_date',
            'start_time',
            'end_time',
            'real_participation_activity_amount',
            'special_link',
            'admin_id',
        ])
            ->andWhere($andWhere)
            ->andWhere($orWhere)
            ->orderBy($orderBy);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->asArray()
            ->all();

        $list = self::formatSpecialActivityList($list);

        return [
            'list'  => $list,
            'pages' => [
                'limit' => $pages['limit'],
                'count' => $count,
                'page'  => $pages['page'],
            ],
        ];
    }

    /**
     * 整理专场内容
     * @param $list
     * @return array
     */
    public static function formatSpecialActivityList($list, $dateType = 1): array
    {
        foreach ($list as &$item) {
            $item['adminName']      = BaseAdmin::findOne($item['admin_id'])->name;
            $item['adminName']      = $item['adminName'] ?: '';
            $item['toHoldTypeText'] = BaseHwSpecialActivity::TO_HOLD_TYPE_TEXT_LIST[$item['to_hold_type']] ?? '';
            if (!empty($item['custom_time'])) {
                $activityDateText = $item['custom_time'];
            } else {
                // 处理时间格式
                $activityDateText = BaseService::formatActivityDateText($dateType, $item['start_date'],
                    $item['end_date'], $item['start_time'], $item['end_time']);
            }
            $item['activityDateText'] = $activityDateText;
            $item['statusTxt']        = BaseHwSpecialActivity::STATUS_TEXT_LIST[$item['status']] ?? '';

            // 标签配置
            $tagIds     = explode(',', $item['tag_ids']);
            $tagTextArr = [];
            foreach ($tagIds as $tagId) {
                $tagTextArr[] = BaseHwSpecialActivity::ACTIVITY_TAG_TEXT_LIST[$tagId] ?? '';
            }

            $customTag          = explode(',', $item['custom_tag']);
            $item['tagTextArr'] = array_filter(ArrayHelper::merge($tagTextArr, $customTag));

            $item['special_link'] = SpecialActivityService::getSpecialLink($item['special_link']);

            $item['realParticipationCompanyAmount'] = BaseHwSpecialActivityRelation::getRealParticipationCompanyAmount($item['id']);
        }

        return $list;
    }

    /**
     * 获取活动公告详情链接的公告ID的单位
     * @param $link
     */
    public static function getAnnouncementDetailCompanyInfo($link)
    {
        //公告链接格式：gaoxiaojob.com/announcement/detail/{公告ID}.html
        //先看link内是否含有/announcement/detail/
        if (empty($link) || strpos($link, 'gaoxiaojob.com/announcement/detail/') === false) {
            return '';
        }

        //根据格式截取获取公告ID
        $linkArr = explode('?', $link);
        if (!$linkArr[0]) {
            return '';
        }
        $linkArr = explode('detail/', $linkArr[0]);
        if (!$linkArr[0]) {
            return '';
        }
        $linkArr        = explode('.', $linkArr[1]);
        $announcementId = $linkArr[0];
        if (!$announcementId || intval($announcementId) <= 0) {
            return '';
        }
        //获取公告信息
        $announcementInfo = BaseAnnouncement::findOne($announcementId);
        if (!$announcementInfo) {
            return '';
        }
        //获取单位信息
        $companyInfo = BaseCompany::findOne($announcementInfo->company_id);
        if (!$companyInfo) {
            return '';
        }

        return [
            'id'       => $companyInfo->id,
            'fullName' => $companyInfo->full_name,
        ];
    }

    /**
     * 获取专场详情
     * @param $id
     * @return array
     */
    public static function getSpecialActivityDetail($id)
    {
        $specialActivityDetail = BaseHwSpecialActivity::findOne($id);

        if ($specialActivityDetail) {
            $specialActivityDetail = $specialActivityDetail->toArray();
            if ($specialActivityDetail['to_hold_type'] == BaseHwSpecialActivity::TO_HOLD_TYPE_ALL) {
                $specialActivityDetail['to_hold_type'] = BaseHwSpecialActivity::TO_HOLD_TYPE_ONLINE . ',' . BaseHwSpecialActivity::TO_HOLD_TYPE_OFFLINE;
            }
            $specialActivityDetail['apply_person_time']  = $specialActivityDetail['apply_person_time'] == TimeHelper::ZERO_DATE ? '' : $specialActivityDetail['apply_person_time'];
            $specialActivityDetail['apply_company_time'] = $specialActivityDetail['apply_company_time'] == TimeHelper::ZERO_DATE ? '' : $specialActivityDetail['apply_company_time'];
            $specialActivityDetail['to_hold_type']       = explode(',', $specialActivityDetail['to_hold_type']);
            // 整理图片数据
            $fileList = SpecialActivityService::getSpecialActivityFile($specialActivityDetail['id'],
                $specialActivityDetail);
            // 关联的活动
            $relationActivityList = ['relationActivityList' => self::getSpecialActivityRelationActivity($specialActivityDetail['id'])];
            // 关联的地区
            if (!$specialActivityDetail['custom_address']) {
                $sessionAreaList = BaseHwSpecialActivitySessionArea::find()
                    ->where(['special_id' => $specialActivityDetail['id']])
                    ->select(['area_id'])
                    ->column();

                $specialActivityDetail['sessionAreaList'] = $sessionAreaList;
            }

            // 追加数据给前端使用，部分下拉选项不存数据库，直接判断即可
            $specialActivityDetail['isCustomAddress'] = $specialActivityDetail['custom_address'] ? 1 : 2;
            $specialActivityDetail['isCustomTime']    = $specialActivityDetail['custom_time'] ? 1 : 2;
            $specialActivityDetail['custom_tag']      = !empty($specialActivityDetail['custom_tag']) ? explode(',',
                $specialActivityDetail['custom_tag']) : [];
            $specialActivityDetail['tag_ids']         = !empty($specialActivityDetail['tag_ids']) ? explode(',',
                $specialActivityDetail['tag_ids']) : [];
            if ($specialActivityDetail['apply_link_person_type'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_NULL) {
                $specialActivityDetail['apply_link_person_type']           = '';
                $specialActivityDetail['apply_link_person_form_id']        = '';
                $specialActivityDetail['apply_link_person_form_option_id'] = '';
            }

            // 整合数据
            $specialActivityDetail = ArrayHelper::intToString(ArrayHelper::merge($fileList, $relationActivityList,
                $specialActivityDetail));
        }

        // 地址组件
        $areaList = BaseArea::getHwAreaList();
        // 获取特色标签内容
        $activityTagList = ArrayHelper::obj2Arr(BaseHwSpecialActivity::ACTIVITY_TAG_TEXT_LIST);
        // 举办类型，需要把线上线+下去掉
        $toHoldTypeList = BaseHwSpecialActivity::TO_HOLD_TYPE_TEXT_LIST;
        unset($toHoldTypeList[BaseHwSpecialActivity::TO_HOLD_TYPE_ALL]);
        $toHoldTypeList = ArrayHelper::obj2Arr($toHoldTypeList);
        // 报名链接
        $applyLinkPersonTypeList = ArrayHelper::obj2Arr(BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_TEXT_LIST);
        // 页面模版
        $templateList = ArrayHelper::obj2Arr(BaseHwSpecialActivity::TEMPLATE_TEXT_LIST);
        // 活动类型
        $typeList = BaseHwActivity::getSeriesAdminAndTypeList();

        return [
            'info'                    => empty($specialActivityDetail) ? [] : $specialActivityDetail,
            'typeList'                => $typeList,
            'areaList'                => $areaList,
            'activityTagList'         => $activityTagList,
            'toHoldTypeList'          => $toHoldTypeList,
            'applyLinkPersonTypeList' => $applyLinkPersonTypeList,
            'templateList'            => $templateList,
            'activityDomain'          => BaseService::getDomain() . '/zhuanchang/',
        ];
    }

    /**
     * 整合专场活动关联的活动
     * @param $specialActivityId int
     * @return array
     */
    private static function getSpecialActivityRelationActivity(int $specialActivityId): array
    {
        $activityList = BaseHwSpecialActivityRelation::getRelationActivityList([
            'specialActivityId' => $specialActivityId,
        ], [
            'a.name',
            'sar.id',
            'sar.sort',
            'sar.activity_id',
            'sar.activity_short',
            'sar.is_recommend',
            'asession.start_date',
        ], 'sar.id asc');

        foreach ($activityList as &$item) {
            $item['activityDate'] = self::getActivityDate($item['activity_id']);
        }

        // 使用 usort 对活动列表进行排序
        usort($activityList, function ($a, $b) {
            // 获取开始时间
            $startTimeA = ($a['start_date'] === '0000-00-00' || empty($a['start_date'])) ? PHP_INT_MAX : strtotime($a['start_date']);
            $startTimeB = ($b['start_date'] === '0000-00-00' || empty($b['start_date'])) ? PHP_INT_MAX : strtotime($b['start_date']);

            // 先比较开始时间
            if ($startTimeA !== $startTimeB) {
                return $startTimeA <=> $startTimeB; // 按开始时间升序排列
            }

            // 如果开始时间相同，则按 activity_id 倒序排列
            return $b['activity_id'] <=> $a['activity_id'];
        });

        return $activityList;
    }

    /**
     * 获取专场可新增活动列表
     * @param $params
     * @return array
     */
    public static function getSpecialActivityCanAddActivity($params): array
    {
        $list = BaseHwActivity::getActivityList([
            'removeIds'   => $params['hasSetActivityId'] ?? '',
            'keyword'     => $params['keyword'] ?? '',
            'seriesTypes' => BaseHwActivity::ZHAOPINHUI_TYPE,
        ]);

        $arr = [];
        foreach ($list as $value) {
            $arr[] = [
                'k'            => (string)$value['id'],
                'v'            => $value['name'],
                'activityDate' => self::getActivityDate($value['id']),
            ];
        }

        // 获取可添加的活动
        return [
            'list' => $arr,
        ];
    }

    /**
     * 验证更新专场时参数
     * @param $params
     * @return void
     * @throws MessageException
     */
    public static function checkUpdateSpecialParams($params)
    {
        $checkParamsKeyArr = [
            'name',
            'toHoldType',
            'activityDetail',
            'imagePcBannerId',
            'imageMiniBannerId',
            'specialLink',
        ];

        foreach ($checkParamsKeyArr as $checkParamKey) {
            if (!isset($params[$checkParamKey]) || $params[$checkParamKey] == '') {
                throw new MessageException($checkParamKey . '参数不能为空');
            }
        }

        // 验证举办地址
        if (empty($params['customAddress']) && empty($params['sessionAreaList'])) {
            throw new MessageException('举办地址不能为空');
        }

        // 验证举办时间
        if (empty($params['customTime']) && (empty($params['startDate']) || empty($params['endDate']))) {
            throw new MessageException('举办时间不能为空');
        }

        if (empty($params['relationActivityList'])) {
            throw new MessageException('关联活动不能为空');
        }

        //        if (empty($params['applyLinkPersonType'])) {
        //            throw new MessageException('人才报名链接不能为空');
        //        }
        //
        //        if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM && empty($params['applyLinkPersonFormId'])) {
        //            throw new MessageException('人才报名链接不能为空');
        //        }
        //
        //        if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM_OPTION && empty($params['applyLinkPersonFormOptionId'])) {
        //            throw new MessageException('人才报名链接不能为空');
        //        }
        //
        //        if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER && empty($params['applyLinkPerson'])) {
        //            throw new MessageException('人才报名链接不能为空');
        //        }

        // 判断结束时间必须大于开始时间
        if ($params['customTime'] == '') {
            // 非自定义时间,结束时间必须大于等于开始时间，转成时间搓去判断
            $formatStartDate = $params['startDate'];
            $formatEndDate   = $params['endDate'];

            if ($params['startTime']) {
                $startTime = substr_replace($params['startTime'], ':', 2, 0) . ':00';
            } else {
                $startTime = '00:00:00';
            }
            if ($params['endTime']) {
                $endTime = substr_replace($params['endTime'], ':', 2, 0) . ':00';
            } else {
                $endTime = '23:59:59';
            }
            $formatStartDate = $formatStartDate . ' ' . $startTime;
            $formatEndDate   = $formatEndDate . ' ' . $endTime;

            if (strtotime($formatEndDate) < strtotime($formatStartDate)) {
                throw new MessageException('结束时间必须大于开始时间');
            }
        }
    }

    /**
     * 更新/编辑专场详情
     * @param $params
     * @return void
     */
    public static function updateSpecialActivityDetail($params)
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $isNeedAddArea = false; // 是否需要批量导入地址
            if (!empty($params['id'])) {
                $specialActivityModel = BaseHwSpecialActivity::findOne($params['id']);
            }
            if (!$specialActivityModel) {
                $specialActivityModel           = new BaseHwSpecialActivity();
                $specialActivityModel->admin_id = $params['adminId'] ?? 0;
            }

            // 信息处理
            $specialActivityModel->name = $params['name'] ?? '';
            // 举办方式
            $onlineHoldType  = 0;
            $offlineHoldType = 0;
            if (in_array(BaseHwSpecialActivity::TO_HOLD_TYPE_ONLINE, $params['toHoldType'])) {
                $onlineHoldType                     = 1;
                $specialActivityModel->to_hold_type = BaseHwSpecialActivity::TO_HOLD_TYPE_ONLINE;
            }
            if (in_array(BaseHwSpecialActivity::TO_HOLD_TYPE_OFFLINE, $params['toHoldType'])) {
                $offlineHoldType                    = 1;
                $specialActivityModel->to_hold_type = BaseHwSpecialActivity::TO_HOLD_TYPE_OFFLINE;
            }
            if ($onlineHoldType == 1 && $offlineHoldType == 1) {
                $specialActivityModel->to_hold_type = BaseHwSpecialActivity::TO_HOLD_TYPE_ALL;
            }
            // 活动时间
            if (!empty($params['customTime'])) {
                $specialActivityModel->start_date  = '';
                $specialActivityModel->start_time  = '';
                $specialActivityModel->end_date    = '';
                $specialActivityModel->end_time    = '';
                $specialActivityModel->custom_time = $params['customTime'];
            } else {
                $specialActivityModel->start_date  = $params['startDate'] ?? '';
                $specialActivityModel->start_time  = $params['startTime'] ?? '';
                $specialActivityModel->end_date    = $params['endDate'] ?? '';
                $specialActivityModel->end_time    = $params['endTime'] ?? '';
                $specialActivityModel->custom_time = '';
            }
            $specialActivityModel->event_organization = $params['eventOrganization'] ?? '';
            // 举办地址处理
            if (!empty($specialActivityModel->id)) {
                BaseHwSpecialActivitySessionArea::deleteAll(['special_id' => $specialActivityModel->id]);
            }

            if (!empty($params['customAddress'])) {
                $specialActivityModel->custom_address = $params['customAddress'];
                $specialActivityModel->detail_address = '';
            } else {
                $specialActivityModel->custom_address = '';
                $specialActivityModel->detail_address = $params['detailAddress'] ?? '';
                $isNeedAddArea                        = true;
            }

            $specialActivityModel->type                         = $params['type'] ?? '';
            $specialActivityModel->activity_detail              = $params['activityDetail'] ?? '';
            $specialActivityModel->participation_method         = $params['participationMethod'] ?? '';
            $specialActivityModel->retrospection                = $params['retrospection'] ?? '';
            $specialActivityModel->participation_benefit        = $params['participationBenefit'] ?? '';
            $specialActivityModel->participation_benefit_detail = $params['participationBenefitDetail'] ?? '';
            $specialActivityModel->image_pc_banner_id           = $params['imagePcBannerId'] ?? '';
            $specialActivityModel->image_mini_banner_id         = $params['imageMiniBannerId'] ?? '';
            $specialActivityModel->image_service_code_id        = $params['imageServiceCodeId'] ?? '';
            $specialActivityModel->custom_tag                   = $params['customTag'] ?? '';
            $specialActivityModel->tag_ids                      = $params['tagIds'] ?? '';

            // 判断人才报名链接类型
            $specialActivityModel->apply_link_person_type = intval($params['applyLinkPersonType']);
            if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER) {
                $specialActivityModel->apply_link_person = $params['applyLinkPerson'] ?? '';
            } else {
                if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM) {
                    $specialActivityModel->apply_link_person_form_id = $params['applyLinkPersonFormId'] ?? 0;
                } else {
                    if ($params['applyLinkPersonType'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_FORM_OPTION) {
                        $specialActivityModel->apply_link_person_form_id        = $params['applyLinkPersonFormId'] ?? 0;
                        $specialActivityModel->apply_link_person_form_option_id = $params['applyLinkPersonFormOptionId'] ?? 0;
                    }
                }
            }

            $specialActivityModel->apply_person_time                  = (empty($params['applyPersonTime'])) ? TimeHelper::ZERO_DATE : $params['applyPersonTime'];
            $specialActivityModel->apply_company_time                 = (empty($params['applyCompanyTime'])) ? TimeHelper::ZERO_DATE : $params['applyCompanyTime'];
            $specialActivityModel->apply_link_company                 = $params['applyLinkCompany'] ?? '';
            $specialActivityModel->template_id                        = empty($params['templateId']) ? BaseHwSpecialActivity::TEMPLATE_DEFAULT : $params['templateId'];
            $specialActivityModel->special_link                       = $params['specialLink'] ?? '';
            $specialActivityModel->real_participation_activity_amount = count($params['relationActivityList']);

            $specialActivityModel->save();

            // 判断链接是否已经存在
            $checkSpecialLinkExists = BaseHwSpecialActivity::find()
                ->andWhere(new AndCondition([
                    [
                        '<>',
                        'id',
                        $specialActivityModel->id,
                    ],
                    [
                        '=',
                        'special_link',
                        $params['specialLink'],
                    ],
                ]))
                ->exists();
            if ($checkSpecialLinkExists) {
                throw new MessageException('页面链接已存在，需要替换');
            }

            // 这个要等后续保存了主表再操作
            if ($isNeedAddArea) {
                // 将所有数据查出来
                foreach ($params['sessionAreaList'] as $areaId) {
                    $areaInfo = BaseArea::getAreaInfo($areaId); // 地址信息

                    $specialActivitySessionAreaModel             = new BaseHwSpecialActivitySessionArea();
                    $specialActivitySessionAreaModel->special_id = $specialActivityModel->id;
                    $specialActivitySessionAreaModel->area_id    = $areaId;
                    $specialActivitySessionAreaModel->level      = $areaInfo['level'] ?? 0;
                    $specialActivitySessionAreaModel->save();
                }
            }

            // 追加活动
            BaseHwSpecialActivityRelation::deleteAll(['special_id' => $specialActivityModel->id]);
            foreach ($params['relationActivityList'] as $activity) {
                $activityRelationModel                 = new BaseHwSpecialActivityRelation();
                $activityRelationModel->special_id     = $specialActivityModel->id;
                $activityRelationModel->activity_id    = $activity['activityId'];
                $activityRelationModel->sort           = $activity['sort'] ?? 0;
                $activityRelationModel->activity_short = empty($activity['activityShort']) ? '' : $activity['activityShort'];
                $activityRelationModel->is_recommend   = $activity['isRecommend'] ?? BaseHwSpecialActivityRelation::NOT_RECOMMENDED;

                if (!$activityRelationModel->save()) {
                    throw new MessageException($activityRelationModel->getFirstErrorsMessage());
                }
            }

            SpecialActivityService::syncSpecialActivity($specialActivityModel->id);

            $transaction->commit();
            if (!$specialActivityModel->id) {
                throw new MessageException('新增失败');
            }

            // 更新对应场次排序逻辑
            foreach ($params['relationActivityList'] as $activity) {
                Producer::hwActivityCompanyQueue($activity['activityId']);

                if (Yii::$app->params['environment'] == 'local') {
                    $queue = (new HwActivityCompany());
                    $queue->updateActivityCompanySortPoint($activity['activityId']);
                    $queue->updateSpecialActivityCompanySortPoint($activity['activityId']);
                }
            }

            return [
                'id' => $specialActivityModel->id,
            ];
        } catch (Exception $exception) {
            $transaction->rollBack();
            throw new MessageException($exception->getMessage());
        }
    }

    /**
     * 查询公告关联的海外活动列表
     * @param $params
     * @return array
     */
    public static function searchAnnouncementHwActivity($params)
    {
        // 排除已经选的活动
        $hasSetActivityIds = BaseHwActivityAnnouncement::find()
            ->where(['announcement_id' => $params['announcementId']])
            ->select(['activity_id'])
            ->column();

        $conditions = [
            [
                'keyword',
                'like',
                'name',
            ],
        ];
        $where      = BaseActiveRecord::getSearchCondition($params, $conditions);
        $query1     = (new Query())->select([
            '*',
            '1 as top',
        ])
            ->from(BaseHwActivity::tableName())
            ->where([
                'in',
                'id',
                $hasSetActivityIds,
            ])
            ->andWhere([
                'in',
                'series_type',
                BaseHwActivity::ZHAOPINHUI_TYPE,
            ])
            ->andWhere($where);

        // 创建第二个查询无需置顶
        $query2 = (new Query())->select([
            '*',
            '0 as top',
        ])
            ->from(BaseHwActivity::tableName())
            ->where([
                'not in',
                'id',
                $hasSetActivityIds,
            ])
            ->andWhere([
                'in',
                'series_type',
                BaseHwActivity::ZHAOPINHUI_TYPE,
            ])
            ->andWhere($where);

        // 使用 union 将两个查询结合
        $query = (new Query())->select([
            'id',
            'name',
        ])
            ->from(['combined' => $query1->union($query2)])
            ->orderBy('top asc, activity_child_status asc');

        $sql = $query->createCommand()
            ->getRawSql();
        // `1` AS `top` 和 `0` AS `top` 转为可用效果
        $sql = str_replace('`1` AS `top`', '1 as top', $sql);
        $sql = str_replace('`0` AS `top`', '0 as top', $sql);

        $list = Yii::$app->db->createCommand($sql)
            ->queryAll();

        return ArrayHelper::arr2KV($list, 'id', 'name');
    }

    /**
     * 获取未关联单位列表
     * @param $params
     */
    public static function getAssociatedCompanyList($params)
    {
        $query      = BaseCompany::find()
            ->select([
                'id',
                'full_name as name',
            ])
            ->where([
                'status' => BaseCompany::STATUS_ACTIVE,
            ]);
        $companyIds = BaseHwActivityCompany::find()
            ->select('company_id')
            ->where(['activity_id' => $params['activityId']])
            ->column();
        if ($companyIds) {
            $query->andWhere([
                'not in',
                'id',
                $companyIds,
            ]);
        }

        if ($params['companyName']) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'full_name',
                    $params['companyName'],
                ],
                [
                    'like',
                    'short_name',
                    $params['companyName'],
                ],
            ]);
        }

        $orderBy = ' add_time desc';

        return $query->limit(100)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 添加单位关联
     * @param $companyIds
     */
    public static function addAssociatedCompany($activityId, $companyIds)
    {
        $insert = [];
        foreach ($companyIds as $companyId) {
            if (!BaseHwActivityCompany::find()
                ->where([
                    'activity_id' => $activityId,
                    'company_id'  => $companyId,
                ])
                ->exists()) {
                //不存在
                $insert[] = [
                    'activity_id' => $activityId,
                    'company_id'  => $companyId,
                    'is_top'      => BaseHwActivityCompany::IS_TOP_NO,
                    'sort'        => 0,
                ];
            }
        }
        if ($insert) {
            $res = \Yii::$app->db->createCommand()
                ->batchInsert(BaseHwActivityCompany::tableName(), [
                    'activity_id',
                    'company_id',
                    'is_top',
                    'sort',
                ], $insert)
                ->execute();

            if ($res) {
                //更新活动关联单数量
                $model                               = BaseHwActivity::findOne($activityId);
                $model->participation_company_amount = BaseHwActivityCompany::find()
                    ->where(['activity_id' => $activityId])
                    ->count();
                $model->save();
                Producer::hwActivityCompanyQueue($activityId);
            }

            return $res;
        }

        return false;
    }

    /**
     * 获取未关联公告列表
     * @param $params
     */
    public static function getAssociatedAnnouncementList($params)
    {
        $query           = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title as name',
            ])
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->where([
                'art.status'  => BaseArticle::STATUS_ACTIVE,
                'art.is_show' => BaseArticle::IS_SHOW_YES,
            ]);
        $announcementIds = BaseHwActivityAnnouncement::find()
            ->select('announcement_id')
            ->where(['activity_id' => $params['activityId']])
            ->column();
        if ($announcementIds) {
            $query->andWhere([
                'not in',
                'a.id',
                $announcementIds,
            ]);
        }

        if ($params['announcementName']) {
            $query->andWhere([
                'like',
                'a.title',
                $params['announcementName'],
            ]);
        }

        $orderBy = 'a.add_time desc';

        return $query->limit(100)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 添加公告关联
     */
    public static function addAssociatedAnnouncement($activityId, $announcementIds)
    {
        $announcementInsert = [];
        $companyInsert      = [];
        $companyInsertIds   = [];
        foreach ($announcementIds as $announcementId) {
            //获取公告信息
            $announcementInfo = BaseAnnouncement::findOne($announcementId);
            if (!BaseHwActivityAnnouncement::find()
                ->where([
                    'activity_id'     => $activityId,
                    'announcement_id' => $announcementId,
                    'company_id'      => $announcementInfo->company_id,
                ])
                ->exists()) {
                //不存在
                $announcementInsert[] = [
                    'activity_id'     => $activityId,
                    'announcement_id' => $announcementId,
                    'company_id'      => $announcementInfo->company_id,
                ];
            }
            if (!BaseHwActivityCompany::find()
                    ->where([
                        'activity_id' => $activityId,
                        'company_id'  => $announcementInfo->company_id,
                    ])
                    ->exists() && !in_array($announcementInfo->company_id, $companyInsertIds)) {
                //不存在
                $companyInsert[]    = [
                    'activity_id' => $activityId,
                    'company_id'  => $announcementInfo->company_id,
                    'is_top'      => BaseHwActivityCompany::IS_TOP_NO,
                    'sort'        => 0,
                ];
                $companyInsertIds[] = $announcementInfo->company_id;
            }
        }
        if ($announcementInsert) {
            \Yii::$app->db->createCommand()
                ->batchInsert(BaseHwActivityAnnouncement::tableName(), [
                    'activity_id',
                    'announcement_id',
                    'company_id',
                ], $announcementInsert)
                ->execute();
        }
        if ($companyInsert) {
            \Yii::$app->db->createCommand()
                ->batchInsert(BaseHwActivityCompany::tableName(), [
                    'activity_id',
                    'company_id',
                    'is_top',
                    'sort',
                ], $companyInsert)
                ->execute();
            //更新活动关联单数量
            $model                               = BaseHwActivity::findOne($activityId);
            $model->participation_company_amount = BaseHwActivityCompany::find()
                ->where(['activity_id' => $activityId])
                ->count();
            if ($model->save()) {
                Producer::hwActivityCompanyQueue($activityId);
            }
        }

        return true;
    }

    /**
     * 取消关联
     * @param $activityId
     * @param $companyId
     * @return true
     */
    public static function cancelAssociatedCompany($activityId, $companyId)
    {
        //删除关联的单位数据
        BaseHwActivityCompany::deleteAll([
            'activity_id' => $activityId,
            'company_id'  => $companyId,
        ]);
        //删除关联单位的公告关联数据
        BaseHwActivityAnnouncement::deleteAll([
            'activity_id' => $activityId,
            'company_id'  => $companyId,
        ]);
        //更新活动关联单数量
        $model                               = BaseHwActivity::findOne($activityId);
        $model->participation_company_amount = BaseHwActivityCompany::find()
            ->where(['activity_id' => $activityId])
            ->count();
        $model->save();

        return true;
    }

    /**
     * 关联列表
     * @param $params
     * @return array
     */
    public static function getAssociatedList($params)
    {
        $query = BaseHwActivityCompany::find()
            ->alias('hac')
            ->select([
                'hac.id',
                'hac.activity_id as activityId',
                'hac.company_id as companyId',
                'hac.is_top as isTop',
                'hac.sort',
                'c.full_name as companyName',
                'GROUP_CONCAT(a.title SEPARATOR "、") as announcementName',
            ])
            ->leftJoin(['haa' => BaseHwActivityAnnouncement::tableName()],
                'haa.activity_id=hac.activity_id and haa.company_id=hac.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'haa.announcement_id=a.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'hac.company_id=c.id')
            ->where(['hac.activity_id' => $params['activityId']])
            ->groupBy('hac.activity_id,hac.company_id');

        if ($params['keyword']) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'a.title',
                    $params['keyword'],
                ],
                [
                    'like',
                    'c.full_name',
                    $params['keyword'],
                ],
            ]);
        }

        // sortOrder  asc desc
        // sortField  announcementName isTop
        $order = 'hac.is_top asc,hac.sort desc,hac.id desc';
        if ($params['sortField'] && $params['sortOrder']) {
            if ($params['sortField'] === 'announcementName') {
                $order = 'announcementName ' . $params['sortOrder'] . ',' . $order;
            } elseif ($params['sortField'] === 'isTop') {
                $order = 'isTop ' . $params['sortOrder'] . ',' . $order;
            }
        }

        $count = $query->count();
        $pages = BaseActiveRecord::setPage($count, $params['page'] ?: 1, 20);
        $list  = $query->orderBy($order)
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        return [
            'list' => $list,
            'page' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $pages['page'],
            ],
        ];
    }
}