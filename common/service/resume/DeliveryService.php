<?php

namespace common\service\resume;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyLimitCompany;
use common\base\models\BaseJobApplyLimitCompanyTag;
use common\base\models\BaseJobApplyLimitConfig;
use common\base\models\BaseJobApplyLimitResume;
use common\base\models\BaseJobApplyLimitResumeTag;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobApplyTopEquityRecord;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageDetail;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeTag;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\MaskHelper;
use common\helpers\UrlHelper;
use common\libs\EmailQueue;
use common\libs\WxPublic;
use common\libs\WxWork;
use common\service\CommonService;
use common\service\specialNeedService\JobApplyService;
use frontendPc\models\ResumeEducation;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 处理职位投递流程
 * 1、使用当前服务类应当阅读类前面定义的变量常量
 * 2、其次阅读调用的操作方法头部介绍
 */
class DeliveryService extends CommonService
{
    //简历模块大模块数量
    const  RESUME_MODULE_AMOUNT = 7;
    /** start 操作类型 */
    /** 检测公告检测 */
    const OPERATION_TYPE_CHECK_ANNOUNCEMENT_APPLY = 'check_announcement_apply';
    /** 检测申请 申请弹窗 */
    const OPERATION_TYPE_CHECK_APPLY = 'check_apply';
    /** 申请 */
    const OPERATION_TYPE_APPLY = 'apply';
    /** 系统投递 赋值时默认 */
    const TYPE_APPLY_SYSTEM = 1;
    /** 线下招聘会投递*/
    const TYPE_APPLY_FAIR = 2;
    /** RPO招聘会投递 */
    const TYPE_APPLY_RPO = 3;
    /** end 操作类型 */

    const JOB_DAY_LIMIT       = 30;
    const JOB_SEVEN_DAY_LIMIT = 100;

    /** 传递参数 */
    private array $requestData = [];
    /** 求职者的基本信息与一些相关信息 */
    private $memberResumeInfo;
    /** 职位与单位信息 */
    private $jobCompanyInfo;
    /** 公告信息 */
    private $announcementInfo;
    /** 职位者最高学历 */
    private $memberMaxEducationId;
    /** 满多少完整度才能投递的配置 */
    private $configComplete;
    /** 求职者简历完整度 */
    private $complete;
    /** 用户member_id */
    private $memberId;
    /** 投递弹窗类型 */
    private $applyToastType;
    /** 单位ID */
    private $companyId;
    /** 投递类型 */
    private $deliveryType;
    /** 投递方式 */
    private $deliveryWay;
    /** 是否是合作单位 */
    private $isCooperation;

    // 被限制的时候出现的提示
    private $jobLimitMessageType = 0;
    // 0代表没有命中限制，1代表命中了ban的限制，2代表命中了lower的限制
    private $jobLimitType = 0;

    private $domainStr;
    //    private $domainStr = 'gaoxiaojob.com';
    // private $domainStr = 'jugaocai.com';

    /** start 文本检测与申请时候的文本身 */
    /**
     * 文本定义介绍：
     *  解释变量名：
     *  功能:check(检测界面)
     *  弹窗类型：1站内 2站外
     */
    /** 文本定义 */
    private string $system_tips                       = '系统校验到该职位最低学历要求：{p1}，建议您确认学历要求后再投递';
    private string $resume_tips                       = '简历完成度{p1}%';
    private string $resume_file_tips_1                = '<span style="color: #FA635C;">*</span>公告正文提示报名需提交附件材料，请确认是否已上传。';
    private string $resume_file_tips_2                = '<span style="color: #FA635C;">*</span>需要上传职位所要求的应聘材料';
    private string $resume_file_tips_3                = '请按招聘要求上传材料';
    private string $un_cooperate_email_tips           = '该职位为我平台整理后发布，<span class="color-primary">我平台将帮您代投简历至用人部门邮箱，同时抄送至您 {p1} 的邮箱，</span>请注意查收邮件以获得后续反馈进度。投递成功后，可至“投递”-“站外投递”查看您的投递记录。';
    private string $link_tips                         = '应要求，本职位须通过{p1}申请。选择“投递”，将打开单位方招聘页面，并自动帮您添加该职位至“投递”-“站外投递”菜单。';
    private string $link_double_tips                  = '本场活动需通过指定报名通道进行报名。请点击“去报名”查看更多活动信息，立即报名！';
    private string $apply_education_limit_tips        = '您的学历不符合，职位最低学历要求：{p1}';
    private string $apply_file_limit_tips             = '投递须上传职位所要求的应聘材料，请先上传文件';
    private string $apply_double_education_limit_tips = '您的学历不符合，场次最低学历要求：{p1}';
    private string $apply_double_success_tips         = '您已成功报名{p1}！会前将有工作人员联系您，请保持电话畅通并按时参会。';
    private string $apply_double_success_qrcode_tips  = '扫下方二维码添加高才助手，加入本场微信群了解招聘会最新动态。';

    /** 简历前三步未完成文案  */
    private string $resume_step_unfinished_tips = '当前简历信息不完善，请先完善简历';
    private string $resume_complete_tips        = '您的在线简历完善度{p1}%，简历完善度达{p2}%方可投递。请先完善简历';

    /** 投递权益文案 */
    private string $equity_remaining_n_number_tips = '使用后您的简历在单位端将置顶展示（剩余<span class="color-primary">{p1}</span>次）';
    private string $equity_remaining_0_number_tips = '使用后您的简历在单位端将置顶展示（剩余<span class="color-primary">0</span>次）';
    private string $equity_used_30_days_tips       = '该职位30天内已使用过投递置顶权益（剩余<span class="color-primary">{p1}</span>次）';

    /** 限制投递的用户 */
    const LIMIT_APPLY = [
        //companyId1=>[resumeId1,resumeId2,resumeId3,.....]
        //companyId2=>[resumeId1,resumeId2,resumeId3,.....]
        212 => [122824],
    ];

    /**
     * 设置用户
     */
    public function setUser()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
            case self::PLATFORM_WEB_PERSON:
            case self::PLATFORM_H5:
                //检测是否有用户登录
                if (!Yii::$app->user->isGuest) {
                    //获取登录用户ID
                    $this->memberId = Yii::$app->user->id;
                    if ($this->memberId <= 0) {
                        $this->redirectLogin();
                    }
                } else {
                    $this->redirectLogin();
                }
                break;
            case self::PLATFORM_MINI:
                //小程序获取member_id
                $this->memberId = BaseMember::getMiniMemberId();
                if ($this->memberId <= 0) {
                    $this->redirectLogin();
                }
                break;
            default:
                throw new Exception('您没有权限执行此操作！');
        }
    }

    /**
     * 初始化数据
     * @param array $init
     * @return $this
     * @throws Exception
     */
    public function init($init = [])
    {
        if (empty($this->operationPlatform)) {
            throw new Exception('请设置操作平台');
        }
        if (empty($this->operationType)) {
            throw new Exception('请设置操作类型');
        }
        $this->domainStr = Yii::$app->params['environment'] == 'prod' ? 'gaoxiaojob.com' : 'jugaocai.com';
        //获取完整度配置
        $this->configComplete = Yii::$app->params['completeResumePercent'];
        $this->requestData    = array_merge(Yii::$app->request->post(), Yii::$app->request->get());
        //兼容处理
        $this->requestData = array_merge($this->requestData, $init);
        //设置用户member_id
        $this->setUser();
        //初始化设置求职者用户信息与简历信息
        $this->memberResumeInfo = BaseResume::find()
            ->where([
                'member_id' => $this->memberId,
                //'status'    => BaseResume::STATUS_ACTIVE,
            ])
            ->with([
                'member',
            ])
            ->asArray()
            ->one();
        if (empty($this->memberResumeInfo) || (!isset($this->memberResumeInfo['id']) && $this->memberResumeInfo['id'] <= 0) || (!isset($this->memberResumeInfo['member']['id']) && $this->memberResumeInfo['member']['id'] <= 0)) {
            throw new Exception('用户信息或者简历信息不存在');
        }
        //获取当前求职者的最高学历
        $this->memberMaxEducationId = ResumeEducation::findOneVal(['id' => $this->memberResumeInfo['last_education_id']],
            'education_id');
        //特殊处理一下求职者最高学历为5的将它值修改一下为0
        if ($this->memberMaxEducationId == 5) {
            $this->memberMaxEducationId = 0;
        }
        //求职者简历完整度
        $this->complete = $this->memberResumeInfo['complete'];
        //参数含有职位ID则初始化职位信息与单位信息
        if (isset($this->requestData['jobId']) && $this->requestData['jobId'] > 0) {
            //初始化申请职位信息与单位信息
            $this->jobCompanyInfo = BaseJob::getJobCompanyInfo($this->requestData['jobId']);
            if (empty($this->jobCompanyInfo)) {
                throw new Exception('职位信息已下线或不存在');
            }
            //特殊处理一下职位学历要求为5的将它值修改一下为0
            if ($this->jobCompanyInfo['education_type'] == 5) {
                $this->jobCompanyInfo['education_type'] = 0;
            }
            $this->companyId = $this->jobCompanyInfo['company_id'];
        }
        //根据公告ID获取公告信息
        $announcement_id = 0;
        if ($this->jobCompanyInfo['announcement_id'] > 0) {
            $announcement_id = $this->jobCompanyInfo['announcement_id'];
        } elseif (isset($this->requestData['announcementId']) && $this->requestData['announcementId'] > 0) {
            $announcement_id = $this->requestData['announcementId'];
        }
        //新增双选会逻辑判断 type 1系统投递 2线下招聘会投递流程 3rpo招聘会投递流程
        $this->applyToastType = self::TYPE_APPLY_SYSTEM;
        if ($announcement_id > 0) {
            $this->announcementInfo = BaseAnnouncement::findOne($announcement_id);
            if ($this->announcementInfo['template_id'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                $this->applyToastType = self::TYPE_APPLY_FAIR;
            }
            $this->companyId = $this->announcementInfo->company_id;
        }

        return $this;
    }

    /**
     * 执行操作
     * @return false|mixed|string
     * @throws Exception
     */
    public function run()
    {
        switch ($this->operationType) {
            //检测公告职位列表
            case self::OPERATION_TYPE_CHECK_ANNOUNCEMENT_APPLY:
                $result = $this->checkAnnouncementApply();
                break;
            //检测申请操作
            case self::OPERATION_TYPE_CHECK_APPLY:
                $result = $this->checkApply();
                break;
            //申请职位操作
            case self::OPERATION_TYPE_APPLY:
                $result = $this->apply();

                break;
            default:
                throw new Exception('当前操作目前不支持');
        }
        $this->afterExec();

        $this->afterUpdate();

        return $result;
    }

    /**
     * 执行后的更新操作
     * @return void
     */
    public function afterUpdate()
    {
        if ($this->operationType == self::OPERATION_TYPE_APPLY) {
            $jobId = $this->requestData['jobId'];

            //如果有职位id了，计算职位的投递热度，保存到表里
            $jobApplyHeatType          = BaseJob::getApplyHeatType($jobId);
            $jobModel                  = BaseJob::findOne($jobId);
            $jobModel->apply_heat_type = $jobApplyHeatType;
            $jobModel->save();

            $this->checkWarring();
        }
    }

    // 在这里做一些检测，看看是否有人恶意投递，通知相关人员
    private function checkWarring()
    {
        if ($this->deliveryType != BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE) {
            return;
        }

        $resumeId  = $this->memberResumeInfo['id'];
        $companyId = $this->companyId;
        $jobId     = $this->requestData['jobId'];

        // 获取今天的时间戳
        $today       = CUR_DATE;
        $isNotice    = false;
        $contentList = [];

        // $count = BaseJobApplyRecord::find()
        //     ->where([
        //         'resume_id'     => $resumeId,
        //         'company_id'    => $companyId,
        //         'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
        //     ])
        //     ->andWhere([
        //         '>=',
        //         'add_time',
        //         $today,
        //     ])
        //     ->count();
        //
        // if ($count == 5) {
        //     // 人才当日对XX单位站内投递次数=5次,通知文案：
        //     // 人才姓名：{人才姓名}，简历ID：{简历ID}，今日对{单位名称}（{单位所属群组}）站内投递次数已达5次，请相关同事留意！
        //     $isNotice      = true;
        //     $contentList[] = "人才姓名：{人才姓名}，简历ID：{简历ID}，今日对{单位名称}（{单位所属群组}）站内投递次数已达{$count}次，请相关同事留意！";
        // }

        // 人才近30天（算今天）对XX单位累计站内投递次数＝10次
        $day = date('Y-m-d', strtotime('-29 day'));

        $count = BaseJobApplyRecord::find()
            ->where([
                'resume_id'     => $resumeId,
                'company_id'    => $companyId,
                'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $day,
            ])
            ->count();

        if ($count == 15) {
            // 人才近30天（算今天）对XX单位累计站内投递次数＝10次,通知文���：
            // 人才姓名：{人才姓名}，简历ID：{简历ID}，近30天对{单位名称}（{单位所属群组}）累计站内投递次数已达10次，请相关同事留意！
            $isNotice      = true;
            $contentList[] = "人才姓名：{人才姓名}，简历ID：{简历ID}，近30天对{单位名称}（{单位所属群组}）累计站内投递次数已达{$count}次，请相关同事留意！";
        }

        // 人才近7天累计站内投递次数＝80次
        $day   = date('Y-m-d', strtotime('-6 day'));
        $count = BaseJobApplyRecord::find()
            ->where([
                'resume_id'     => $resumeId,
                'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $day,
            ])
            ->count();

        if ($count == 100) {
            // 人才近7天累计站内投递次数＝80次,通知文案：
            // 人才姓名：{人才姓名}，简历ID：{简历ID}，近7天累计站内投递次数已达80次，请相关同事留意！
            $isNotice      = true;
            $contentList[] = "人才姓名：{人才姓名}，简历ID：{简历ID}，近7天累计站内投递次数已达{$count}次，请相关同事留意！";
        }

        // 找出这个人对这个单位的某个职位投递超过两次的数量
        // $list = BaseJobApplyRecord::find()
        //     ->select('count(*) as total,job_id')
        //     ->where([
        //         'resume_id'     => $resumeId,
        //         'company_id'    => $companyId,
        //         'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
        //     ])
        //     ->groupBy('job_id')
        //     ->having('total>=2')
        //     ->asArray()
        //     ->all();
        //
        // $count = count($list);
        // if ($count >= 3) {
        //     foreach ($list as $item) {
        //         if ($jobId == $item['job_id'] && $item['total'] == 2) {
        //             $isNotice = true;
        //             // 人才姓名：{人才姓名}，简历ID：{简历ID}，对{单位名称}（{单位所属群组}）重复投递站内职位已达{重复投递的职位数量}个，请相关同事留意！
        //             $contentList[] = "人才姓名：{人才姓名}，简历ID：{简历ID}，对{单位名称}（{单位所属群组}）重复投递站内职位已达{$count}个，请相关同事留意！";
        //         }
        //     }
        // }

        if ($isNotice) {
            $resumeName        = $this->memberResumeInfo['name'];
            $companyName       = $this->jobCompanyInfo['company']['full_name'];
            $resumeUUid        = $this->memberResumeInfo['uuid'];
            $companyGroupNames = BaseCompany::getGroupName($this->companyId);

            $wxWork = WxWork::getInstance();
            foreach ($contentList as $content) {
                $content = str_replace('{人才姓名}', $resumeName, $content);
                $content = str_replace('{简历ID}', $resumeUUid, $content);
                $content = str_replace('{单位名称}', $companyName, $content);
                $content = str_replace('{单位所属群组}', $companyGroupNames, $content);
                $wxWork->robotJobApplyWarring($content);
            }
        }
    }

    /**
     * check之前执行的公共检测
     */
    private function beforeCheck($isAnnouncementCheck = false)
    {
        $resumeStep = BaseResumeComplete::getResumeStep($this->memberResumeInfo['id']);
        //简历未完善前三步
        if ($resumeStep < 4) {
            if ($this->applyToastType == self::TYPE_APPLY_SYSTEM) {
                $result['toastDialogData']['title']             = '提示';
                $result['toastDialogData']['content']           = $this->textReplace($this->resume_complete_tips, [
                    $this->memberResumeInfo['complete'],
                    $this->configComplete,
                ]);
                $result['toastDialogData']['confirmButtonText'] = '去完善';
                $result['toastDialogData']['cancelButtonText']  = '放弃完善';
                $result['toastDialogData']['link']              = $this->getResumeStepUrl($resumeStep);
                $result['toastDialogData']['isSendRequest']     = false;
                $result['toastDialogData']['isAutoJump']        = false;
                $result['applyDialogData']                      = [];

                return $result;
            } elseif ($this->applyToastType == self::TYPE_APPLY_FAIR && $this->deliveryWay != BaseJob::DELIVERY_WAY_LINK && !$isAnnouncementCheck) {
                $result['toastDialogData']['title']             = '温馨提示';
                $result['toastDialogData']['content']           = $this->resume_step_unfinished_tips;
                $result['toastDialogData']['confirmButtonText'] = '去完善';
                $result['toastDialogData']['cancelButtonText']  = '稍后操作';
                $result['toastDialogData']['isSendRequest']     = false;
                $result['toastDialogData']['isAutoJump']        = false;
                $result['toastDialogData']['link']              = $this->getResumeStepUrl($resumeStep);
                $result['applyDialogData']                      = [];

                return $result;
            }
        }
        //完善度小于configComplete=75
        if ($this->applyToastType == self::TYPE_APPLY_SYSTEM && $this->memberResumeInfo['complete'] < $this->configComplete) {
            $result['toastDialogData']['title']             = '提示';
            $result['toastDialogData']['content']           = $this->textReplace($this->resume_complete_tips, [
                $this->memberResumeInfo['complete'],
                $this->configComplete,
            ]);
            $result['toastDialogData']['confirmButtonText'] = '去完善';
            $result['toastDialogData']['cancelButtonText']  = '放弃完善';
            $result['toastDialogData']['link']              = $this->getResumecompleteUrl();
            $result['toastDialogData']['isSendRequest']     = false;
            $result['toastDialogData']['isAutoJump']        = false;
            $result['applyDialogData']                      = [];

            return $result;
        }

        return [];
    }

    /**
     * 获取公告下的在线职位列表
     * ```EOT
     * 参数说明
     *    announcementId int 公告ID
     * ```EOT
     * @return array
     * @throws Exception
     */
    private function checkAnnouncementApply()
    {
        //获取传递的参数 --公告ID
        $id = $this->requestData['announcementId'];
        if ($id <= 0) {
            throw new Exception('参数错误');
        }
        // 参数 true 属于公告的check false 非公告check
        $result = $this->beforeCheck(true);
        if (!empty($result)) {
            return $result;
        }
        //检测当前公告是否在线
        $info = BaseAnnouncement::find()
            ->with([
                'article' => function ($query) {
                    $query->where([
                        'status'    => BaseArticle::STATUS_ONLINE,
                        'is_show'   => BaseArticle::IS_SHOW_YES,
                        'is_delete' => BaseArticle::IS_DELETE_NO,
                    ]);
                },
            ])
            ->where(['id' => $id])
            ->asArray()
            ->one();
        if (empty($info['article'])) {
            throw new Exception('公告已下线');
        }
        // 特殊处理一下禅道需求923--限制一些求职者对某些单位的恶意投递
        $this->checkLimitApplySpecial();
        //获取有效职位列表
        $job_list = BaseJob::find()
            ->select([
                'id',
                'name',
                'delivery_way',
            ])
            ->andWhere([
                'announcement_id' => $id,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();

        //增加禅道912需求判断
        $count = count($job_list);
        foreach ($job_list as &$item) {
            $jobStatus = BaseJobApplyRecord::checkJobApplyStatus($this->memberResumeInfo['id'], $item['id']);
            if ($jobStatus == BaseJob::JOB_APPLY_STATUS_YES) {
                $item['type'] = true;
            } else {
                $item['type'] = false;
            }
            $deliveryWay = $item['delivery_way'];
            if ($deliveryWay == 0) {
                $deliveryWay = $info['delivery_way'];
            }
            // 默认可选中逻辑
            if ($jobStatus == BaseJob::JOB_APPLY_STATUS_NO && $count == 1 && (($this->operationPlatform != CommonService::PLATFORM_H5 && $deliveryWay != BaseJob::DELIVERY_WAY_LINK) || ($this->operationPlatform == CommonService::PLATFORM_H5 || $this->operationPlatform == CommonService::PLATFORM_MINI))) {
                $item['active'] = true;
            } else {
                $item['active'] = false;
            }
            unset($item['delivery_way']);
        }

        return [
            'toastDialogData' => [],
            'jobList'         => $job_list,
            'resumeInfo'      => [
                'complete'     => $this->memberResumeInfo['complete'],
                'completeText' => $this->textReplace($this->resume_tips, $this->memberResumeInfo['complete']),
            ],
        ];
    }

    /**
     * 检测职位是否可以投递
     * ```EOT
     * 参数说明
     *    jobId int 职位ID
     * 返回参数说明
     * [
     *     "toastDialogData":[
     *          "title":"",
     *          "content":"",
     *          "confirmButtonText":"",
     *          "cancelButtonText":"",
     *          "link":""
     *      ],
     *      "applyDialogData":[
     *          "systemTips":"",
     *          "resumeTips":"",
     *          "resumeFileTips":"",
     *          "deliveryTopTips":"",
     *          "unCooperateEmailTips":""
     *      ]
     * ]
     * ```EOT
     * @return array
     * @throws Exception
     */
    private function checkApply()
    {
        //验证一下必选参数
        if ($this->requestData['jobId'] <= 0) {
            throw new Exception('参数错误');
        }

        // 从这里开始是直聊加入的判断,暂时多加一层,看看是否有影响
        //单位合作合作类型
        $isCooperation = $this->jobCompanyInfo['company']['is_cooperation'];
        //职位学历ID
        $jobEducationId = $this->jobCompanyInfo['education_type'];
        //职位要求学历
        $jobEducationName          = BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION, $jobEducationId);
        $userEmail                 = '';//用户邮箱
        $onlineApply               = '';//保存链接跳转
        $otherTxt                  = '';//其他投递方式文本
        $isEmailApply              = false;//邮箱投递
        $isOnlineApply             = false;//链接投递
        $isOtherApply              = false;//其他投递
        $applyTypeTxt              = '';
        $applyTypeArr              = [];
        $result                    = [];
        $result['toastDialogData'] = [];
        $result['applyDialogData'] = [];
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            if ($this->jobCompanyInfo['apply_type']) {
                //$applyTypeArr = explode(',', $this->jobCompanyInfo['apply_type']);
                $applyTypeTxt = BaseJob::getApplyTypeName($this->jobCompanyInfo['apply_type'], '/');
            }
            $applyAddress  = $this->jobCompanyInfo['apply_address'];
            $delivery_type = $this->jobCompanyInfo['delivery_type'];
            $deliveryWay   = $this->jobCompanyInfo['delivery_way'];
        } else {
            if ($this->jobCompanyInfo['announcement']['apply_type']) {
                //$applyTypeArr = explode(',', $this->jobCompanyInfo['announcement']['apply_type']);
                $applyTypeTxt = BaseJob::getApplyTypeName($this->jobCompanyInfo['announcement']['apply_type'], '/');
            }
            $delivery_type = $this->jobCompanyInfo['announcement']['delivery_type'] > 0 ? $this->jobCompanyInfo['announcement']['delivery_type'] : BaseJob::DELIVERY_TYPE_OUTSIDE;
            $applyAddress  = $this->jobCompanyInfo['announcement']['apply_address'];
            $deliveryWay   = $this->jobCompanyInfo['announcement']['delivery_type'] > 0 ? $this->jobCompanyInfo['announcement']['delivery_way'] : BaseJob::DELIVERY_WAY_PLATFORM;
        }

        $this->deliveryType = $delivery_type;
        $this->deliveryWay  = $deliveryWay;
        // 从这里开始是直聊加入的判断,暂时多加一层,看看是否有影响
        //        if ($this->jobCompanyInfo['delivery_way'] > 0) {
        //            $delivery_way = $this->jobCompanyInfo['delivery_way'];
        //        } elseif ($this->jobCompanyInfo['announcement']['delivery_way'] > 0) {
        //            $delivery_way = $this->jobCompanyInfo['announcement']['delivery_way'];
        //        } else {
        //            throw new Exception('职位申请失败');
        //        }

        if ($deliveryWay == BaseJob::DELIVERY_WAY_LINK) {
            $isOnlineApply = true;
            $onlineApply   = $applyAddress;
        } elseif ($deliveryWay == BaseJob::DELIVERY_WAY_EMAIL) {
            $isEmailApply = true;
            $userEmail    = MaskHelper::getEmail($this->memberResumeInfo['member']['email']);
        }
        if (!($this->deliveryWay == BaseJob::DELIVERY_WAY_LINK && strpos($applyAddress, $this->domainStr) !== false)) {
            $checkResult = $this->beforeCheck();
            if (!empty($checkResult)) {
                return $checkResult;
            }
        }

        $jobId          = $this->requestData['jobId'];
        $companyId      = $this->jobCompanyInfo['company_id'];
        $resumeId       = $this->memberResumeInfo['id'];
        $announcementId = $this->jobCompanyInfo['announcement_id'];

        // ======特殊逻辑开始======
        // 特殊处理一下禅道需求923--限制一些求职者对某些单位的恶意投递
        $this->checkLimitApplySpecial();
        // 这里做一些专门的限制，害怕某些人或者某些单位职位公告有特殊的要求，但是实际上逻辑在服务里面处理
        (new JobApplyService())->check($jobId, $companyId, $announcementId, $resumeId);
        // ======特殊逻辑结束======

        // 新增投递限制，读取配置等等逻辑会在这里处理
        $this->checkLimitApplyJob();

        //        if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
        //            $isEmailApply = true;
        //            $userEmail    = MaskHelper::getEmail($this->memberResumeInfo['member']['email']);
        //        } elseif (in_array(BaseJob::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
        //            $isOnlineApply = true;
        //            $onlineApply   = $applyAddress;
        //        } elseif (!empty($applyTypeArr)) {
        //            $isOnlineApply = true;
        //            $onlineApply   = $applyAddress;
        //        }

        //弹窗类型
        if ($isOnlineApply) {
            $result['toastDialogData']['isAutoJump'] = false;
            //判断是否是内部网址直接跳转且分端口处理 链接是否含有gaoxiaojob.com
            if (strpos($onlineApply, $this->domainStr) !== false) {
                //此时是内部链接-直接跳转
                $result['toastDialogData']['isAutoJump'] = true;
                if ($this->operationPlatform == self::PLATFORM_MINI) {
                    //http://替换成https
                    $onlineApply = str_replace('http://bm.', 'https://bm.', $onlineApply);
                }
                if (strpos($onlineApply, 'bm.' . $this->domainStr) === false) {
                    //不是问卷星
                    //用gaoxiaojob.com炸成数组
                    $onlineApplyArr = explode($this->domainStr, $onlineApply);
                    $onlineApply    = ($this->operationPlatform == self::PLATFORM_WEB ? UrlHelper::createPcHomePath() : UrlHelper::createH5HomePath()) . $onlineApplyArr[1];
                }
            }

            //链接投递
            //$toastType = 2;
            if ($this->applyToastType == self::TYPE_APPLY_SYSTEM) {
                $result['toastDialogData']['title']             = '提示';
                $result['toastDialogData']['content']           = $this->textReplace($this->link_tips, $applyTypeTxt);
                $result['toastDialogData']['confirmButtonText'] = '去投递';
                $result['toastDialogData']['cancelButtonText']  = '取消';

                // 这里做一个特殊逻辑（特殊单位的特殊逻辑） https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1000
                if ($this->companyId == 486) {
                    $result['toastDialogData']['content'] = '本次招聘所有内容以官网公告为准，点击“去投递”，可打开单位官网招聘链接参与报名。';
                }
            } else {
                //链接弹窗
                //                $result['toastDialogData']['content']           = $this->link_double_tips;
                //                $result['toastDialogData']['confirmButtonText'] = '去报名';
                //                $result['toastDialogData']['cancelButtonText']  = '稍后操作';
                //双会的链接直接跳转pc、h5 直接走；小程序逻辑不变
                if ($this->operationPlatform == self::PLATFORM_MINI) {
                    $result['toastDialogData']['content']           = $this->link_double_tips;
                    $result['toastDialogData']['confirmButtonText'] = '去报名';
                    $result['toastDialogData']['cancelButtonText']  = '稍后操作';
                } else {
                    $result['toastDialogData']['isAutoJump'] = true;
                }
            }
            $result['toastDialogData']['isSendRequest'] = true;
            $result['toastDialogData']['link']          = $onlineApply;
        } else {
            //$toastType = 1;
            if ($this->applyToastType == self::TYPE_APPLY_SYSTEM || ($this->applyToastType == self::TYPE_APPLY_FAIR && $this->operationPlatform == self::PLATFORM_MINI)) {
                $result['applyDialogData']['deliveryTopIsShow']             = false;//true显示false不显示
                $result['applyDialogData']['deliveryTopIsCheckBox']         = 2;//1选中2不选中
                $result['applyDialogData']['deliveryTopIsCheckBoxDisabled'] = true;
                $result['applyDialogData']['deliveryTopSourceAmount']       = 0;
                $result['applyDialogData']['deliveryTopTips']               = '';
                $result['applyDialogData']['deliveryTopButton']             = false;//true显示false不显示
                if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                    //权益验证
                    $equity_result                                              = $this->equityVerify();
                    $result['applyDialogData']['deliveryTopIsShow']             = $equity_result['deliveryTopIsShow'];
                    $result['applyDialogData']['deliveryTopIsCheckBox']         = $equity_result['deliveryTopIsCheckBox'];
                    $result['applyDialogData']['deliveryTopIsCheckBoxDisabled'] = $equity_result['deliveryTopIsCheckBoxDisabled'];
                    $result['applyDialogData']['deliveryTopSourceAmount']       = $equity_result['deliveryTopSourceAmount'];
                    $result['applyDialogData']['deliveryTopTips']               = $equity_result['deliveryTopTips'];
                    $result['applyDialogData']['deliveryTopButton']             = $equity_result['deliveryTopButton'];
                }
                if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $this->memberMaxEducationId < $jobEducationId) {
                    ///学历校验
                    $systemTips = $this->textReplace($this->system_tips, $jobEducationName);
                }
                $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr)) {
                    $resumeFileTips = $this->resume_file_tips_2;
                } elseif (!empty($this->announcementInfo) && $this->announcementInfo['is_attachment_notice'] == BaseAnnouncement::IS_ATTACHMENT_NOTICE_YES) {
                    $resumeFileTips = $this->resume_file_tips_1;
                } else {
                    $resumeFileTips = $this->resume_file_tips_3;
                }
                $resumeTips = $this->textReplace($this->resume_tips, $this->complete);
                if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO && $isEmailApply) {
                    $unCooperateEmailTips = $this->textReplace($this->un_cooperate_email_tips, $userEmail);
                    //如果是小程序就把html标签去掉
                    if ($this->operationPlatform == self::PLATFORM_MINI) {
                        $unCooperateEmailTips = strip_tags($unCooperateEmailTips);
                    }
                }
                $result['applyDialogData']['systemTips']           = $systemTips ?? '';
                $result['applyDialogData']['resumeTips']           = $resumeTips ?? '';
                $result['applyDialogData']['resumeFileTips']       = $resumeFileTips ?? '';
                $result['applyDialogData']['unCooperateEmailTips'] = $unCooperateEmailTips ?? '';
                //获取附件列表
                $resumeAttachmentList = BaseResumeAttachment::getList($this->memberResumeInfo['member']['id']);
                //获取最近一次站内投递的附件简历ID
                $lastResumeAttachmentId    = BaseJobApply::find()
                    ->select('resume_attachment_id')
                    ->andWhere(['resume_id' => $this->memberResumeInfo['id']])
                    ->andWhere([
                        '>',
                        'resume_attachment_id',
                        0,
                    ])
                    ->orderBy('id desc')
                    ->scalar();
                $defaultResumeAttachment   = '';
                $resumeAttachmentLastToken = '';
                $newList                   = [];
                foreach ($resumeAttachmentList as $item) {
                    $itemList          = [];
                    $itemList['label'] = $item['fileName'];
                    $itemList['value'] = $item['token'];
                    array_push($newList, $itemList);
                    if ($item['is_default'] == BaseResumeAttachment::IS_DEFAULT_YES) {
                        $defaultResumeAttachment = $item['token'];
                    }
                    if ($lastResumeAttachmentId == $item['id']) {
                        $resumeAttachmentLastToken = $item['token'];
                    }
                }
                $result['applyDialogData']['resumeAttachmentList']      = $newList;
                $result['applyDialogData']['resumeAttachmentLastToken'] = $resumeAttachmentLastToken;
                $result['applyDialogData']['defaultResumeAttachment']   = $defaultResumeAttachment;
                $result['applyDialogData']['complete']                  = intval($this->complete);
                //处理各端口不一致的文本
                if ($this->operationPlatform == self::PLATFORM_MINI) {
                    //返回一些应聘材料页面自定义文本
                    $result['applyDialogData']['apply_format_text'] = 'pdf/doc/docx/xls/xlsx/rar/zip/jpg/png';
                    $result['applyDialogData']['apply_size_text']   = 15;
                    $result['applyDialogData']['apply_number_text'] = 5;

                    //返回一些附件简历页面自定义文本
                    $result['applyDialogData']['attachment_format_text'] = 'doc/docx/pdf';
                    $result['applyDialogData']['attachment_size_text']   = 15;
                    $result['applyDialogData']['attachment_number_text'] = 8;
                }
            }
        }

        return $result;
    }

    /**
     * 职位申请
     * ```EOT
     * 参数说明
     *    jobId int 职位ID
     *    token string 附件简历标识
     *    isDefault int 附件简历是否设置默认
     *    stuffFileId string 应聘材料ID逗号拼接
     * 返回参数说明
     *    返回跳转链接地址
     * ```EOT
     * @return mixed|string
     * @throws Exception
     */
    private function apply()
    {
        //验证一下必选参数
        if ($this->requestData['jobId'] <= 0) {
            throw new Exception('参数错误');
        }
        //获取参数
        $token       = $this->requestData['token'] ?? '';
        $isDefault   = $this->requestData['isDefault'] ?? '';
        $stuffFileId = $this->filtrationStuffFileId($this->requestData['stuffFileId']);
        //判断请求平台来源返回对应文本
        $jobEducationName = BaseDictionary::getDataName(BaseDictionary::TYPE_EDUCATION,
            $this->jobCompanyInfo['education_type']);
        // 检查一下这个材料是否是这个求职者的,把不是的给过滤了
        // $stuffFileId = $this->requestData['stuffFileId'] ?? '';
        $result       = [];//定义返回结构
        $applyTypeArr = [];//应聘方式
        $email        = '';//邮箱
        $companyUrl   = '';//链接

        //单位合作合作类型
        $isCooperation = $this->jobCompanyInfo['company']['is_cooperation'];
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            if ($this->jobCompanyInfo['delivery_type'] > 0) {//职位属性
                $delivery_type = $this->jobCompanyInfo['delivery_type'];
            } else {//公告属性
                //这里特殊处理  公告也没有时候把他当前成职位是站内投递
                $delivery_type = $this->jobCompanyInfo['announcement']['delivery_type'] > 0 ? $this->jobCompanyInfo['announcement']['delivery_type'] : BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        } else {
            $delivery_type = BaseJobApplyRecord::DELIVERY_TYPE_OUTER;
        }
        //确认投递方式
        if ($this->jobCompanyInfo['delivery_way'] > 0) {
            $delivery_way  = $this->jobCompanyInfo['delivery_way'];
            $apply_address = $this->jobCompanyInfo['apply_address'];
            $apply_type    = $this->jobCompanyInfo['apply_type'];
        } elseif ($this->jobCompanyInfo['announcement']['delivery_way'] > 0) {
            $delivery_way  = $this->jobCompanyInfo['announcement']['delivery_way'];
            $apply_address = $this->jobCompanyInfo['announcement']['apply_address'];
            $apply_type    = $this->jobCompanyInfo['announcement']['apply_type'];
        } else {
            throw new Exception('职位申请失败');
        }
        if (!empty($apply_type)) {
            //应聘方式
            $applyTypeArr = explode(',', $apply_type);
        }

        // 投递方式
        $this->deliveryType = $delivery_type;
        $this->deliveryWay  = $delivery_way;
        //区分线下招聘会和系统投递
        if ($this->applyToastType == self::TYPE_APPLY_SYSTEM) {
            if ($delivery_way == BaseJob::DELIVERY_WAY_LINK) {
                if (strpos($apply_address, $this->domainStr) === false && $this->complete < $this->configComplete) {
                    throw new Exception('完成度不足,请先去完善简历！');
                }
            } else {
                if ($this->complete < $this->configComplete) {
                    throw new Exception('完成度不足,请先去完善简历！');
                }
            }

            // 找到今天总投递次数
            // $todayCount = BaseJobApply::find()
            //     ->where(['resume_member_id' => $this->memberResumeInfo['member']['id']])
            //     ->andWhere([
            //         '>=',
            //         'add_time',
            //         CUR_DATE,
            //     ])
            //     ->count();
            //
            // if ($todayCount >= self::JOB_DAY_LIMIT) {
            //     throw new Exception('您今日已达投递次数上限（每天限投30次）');
            // }
            $apply_education_limit_tips = $this->textReplace($this->apply_education_limit_tips, $jobEducationName);
            $apply_file_limit_tips      = $this->apply_file_limit_tips;
            $apply_success_tips         = '';
            $apply_success_qrcode_tips  = '';
            $check_job_apply_status     = '您已投递过该职位，请勿重复投递！';
        } elseif ($this->applyToastType == self::TYPE_APPLY_FAIR) {
            //当前用户简历完成到第几步
            $resume_step = BaseResumeComplete::getResumeStep($this->memberResumeInfo['id']);
            if ($resume_step < 4 && $delivery_way != BaseJob::DELIVERY_WAY_LINK) {
                throw new Exception('当前简历信息不完善，请先完善简历！');
            }
            //判断用户是否已经投递了该职位
            if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                    $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES) {
                //用户已经投递，暂时不可投递
                throw new Exception('您已报名该场次，请勿重复报名！');
            }
            $apply_education_limit_tips = $this->textReplace($this->apply_double_education_limit_tips,
                $jobEducationName);
            $apply_file_limit_tips      = '';
            $apply_success_tips         = $this->textReplace($this->apply_double_success_tips,
                $this->jobCompanyInfo['name']);
            $apply_success_qrcode_tips  = $this->apply_double_success_qrcode_tips;
            $check_job_apply_status     = '您已报名该场次，请勿重复报名！';
        } else {
            throw new Exception('职位申请失败');
        }

        // 是否合作单位
        $this->isCooperation = $isCooperation;

        $jobId          = $this->requestData['jobId'];
        $companyId      = $this->jobCompanyInfo['company_id'];
        $resumeId       = $this->memberResumeInfo['id'];
        $announcementId = $this->jobCompanyInfo['announcement_id'];

        // 这里做一些专门的限制，害怕某些人或者某些单位职位公告有特殊的要求，但是实际上逻辑在服务里面处理
        (new JobApplyService())->check($jobId, $companyId, $announcementId, $resumeId);

        $this->checkLimitApplyJob();

        //合作单位站内投递+非合作单位邮箱投递
        if (($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) || ($isCooperation == BaseCompany::COOPERATIVE_UNIT_NO && in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL,
                    $applyTypeArr))) {
            //判断职位是否有限制学历+应聘材料
            if (!empty($this->jobCompanyInfo['delivery_limit_type']) && $this->applyToastType == self::TYPE_APPLY_SYSTEM) {
                $deliveryLimitTypeArr = explode(',', $this->jobCompanyInfo['delivery_limit_type']);
                if (in_array(BaseJob::DELIVERY_LIMIT_TYPE_EDUCATION,
                        $deliveryLimitTypeArr) && $this->memberMaxEducationId < $this->jobCompanyInfo['education_type']) {
                    throw new Exception($apply_education_limit_tips);
                } elseif (in_array(BaseJob::DELIVERY_LIMIT_TYPE_FILE, $deliveryLimitTypeArr) && empty($stuffFileId)) {
                    throw new Exception($apply_file_limit_tips);
                }
            }
        }
        //判断用户是否已经投递了该职位
        if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES && $delivery_way != BaseJob::DELIVERY_WAY_LINK) {
            throw new Exception($check_job_apply_status);
        }
        //判断附件简历信息
        $resumeAttachmentId = 0;
        if (!empty($token)) {
            $resumeAttachmentInfo = BaseResumeAttachment::find()
                ->where([
                    'token'     => $token,
                    'status'    => BaseResumeAttachment::STATUS_ACTIVE,
                    'member_id' => $this->memberResumeInfo['member']['id'],
                ])
                ->asArray()
                ->one();
            if (empty($resumeAttachmentInfo)) {
                throw new Exception('附件简历不存在');
            }
            $resumeAttachmentId = $resumeAttachmentInfo['id'];
        }
        //判断应聘材料是否超出限制
        if (!empty($stuffFileId)) {
            $stuffFileIdArr = explode(',', $stuffFileId);
            if (count($stuffFileIdArr) > BaseJobApply::STUFF_FILE_LIMIT_NUM) {
                throw new Exception('应聘材料不得超过' . BaseJobApply::STUFF_FILE_LIMIT_NUM . '个');
            }
        }
        $platform = $this->getPlatform();

        //投递记录确认
        // 站内投递
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            //前提：对该次投递使用投递置顶权益
            //1、站内投递需要检验资源够不够
            //2、站内投递需要检验30天内对该职位是否使用过
            if ($this->requestData['deliveryTopIsCheckBox'] == 1) {
                $resumeEquityPackageData = BaseResumeEquityPackage::getEquityList($this->memberResumeInfo['id'],
                    BaseResumeEquitySetting::ID_DELIVERY_TOP, BaseResumeEquityPackage::STATUS_EXPIRE);
                if (count($resumeEquityPackageData) > 0) {
                    $source_amount = array_sum(array_column($resumeEquityPackageData, 'amount'));
                    if ($source_amount <= 0) {
                        throw new Exception('投递置顶资源不足');
                    }
                } else {
                    throw new Exception('您的没有投递置顶权益');
                }
                if (BaseJobApplyTopEquityRecord::isUseEquity($this->jobCompanyInfo['id'],
                    $this->memberResumeInfo['id'])) {
                    throw new Exception('该职位30天内已使用过投递置顶权益');
                }
            }
            //做一个投递统计
            $data = [
                'job_id'               => $this->jobCompanyInfo['id'],
                'member_id'            => $this->memberResumeInfo['member']['id'],
                'resume_id'            => $this->memberResumeInfo['id'],
                'resume_attachment_id' => $resumeAttachmentId,
                'stuff_file_id'        => $stuffFileId,
                'delivery_way'         => $delivery_way,
                'platform'             => $platform,
                'operation_platform'   => $this->operationPlatform,
                'equity_status'        => $this->requestData['deliveryTopIsCheckBox'] == 1 ? 1 : 0,
            ];
            //继续执行申请职位操作
            $applyId = BaseJob::apply($data);
            //投递完毕做一个消耗权益的处理
            if ($this->requestData['deliveryTopIsCheckBox'] == 1) {
                //权益扣除
                $this->equityDeduct($applyId);
            }
            //做一个投递统计
            BaseJobApplyRecordExtra::JobApplyCount($this->jobCompanyInfo['id'], $this->memberResumeInfo['id'],
                $platform);
        } else {// 站外投递
            $wage = BaseJob::formatWage($this->jobCompanyInfo['min_wage'], $this->jobCompanyInfo['max_wage'],
                $this->jobCompanyInfo['wage_type']);
            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $applyStatus = BaseOffSiteJobApply::APPLY_STATUS_DELIVERY;
                $email       = $apply_address;
            } else {
                $companyUrl = $apply_address;
            }
            $offSiteJobApplyData = [
                //公告标题
                'applyStatus'        => $applyStatus ?: BaseOffSiteJobApply::APPLY_STATUS_EMPLOYED_WAIT,
                'title'              => BaseAnnouncement::findOneVal(['id' => $this->jobCompanyInfo['announcement_id']],
                    'title'),
                'job_name'           => $this->jobCompanyInfo['name'],
                'applyDate'          => CUR_DATE,
                'company_id'         => $this->jobCompanyInfo['company_id'],
                'delivery_way'       => $delivery_way,
                'salary'             => $wage,
                'link'               => $companyUrl,
                'source'             => BaseOffSiteJobApply::SOURCE_WEBSITE,
                'jobId'              => $this->jobCompanyInfo['id'],
                'announcementId'     => $this->jobCompanyInfo['announcement_id'],
                'resumeAttachmentId' => $resumeAttachmentId,
                'stuffFileId'        => $stuffFileId,
                'email'              => $email,
                'platform'           => $platform,
                'operation_platform' => $this->operationPlatform,
            ];
            //新增站外投递记录
            //看看是不是链接投递
            $record_bool = true;
            //是否有投递记录
            $job_apply_info = BaseJobApplyRecord::find()
                ->where([
                    'job_id'        => $this->jobCompanyInfo['id'],
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTER,
                    'delivery_way'  => BaseJobApplyRecord::DELIVERY_WAY_LINK,
                ])
                ->asArray()
                ->one();
            if ($delivery_way == BaseJob::DELIVERY_WAY_LINK && !empty($job_apply_info)) {
                $record_bool = false;
            }
            if ($record_bool) {
                //站外投递
                $offSiteJobApplyId = BaseOffSiteJobApply::saveInfo($offSiteJobApplyData,
                    $this->memberResumeInfo['member']['id']);
                //做一个投递统计
                BaseJobApplyRecordExtra::JobApplyCount($this->jobCompanyInfo['id'], $this->memberResumeInfo['id'],
                    $platform);
            }
            //判断是发送电子邮件，还是条跳转网页
            if (!empty($email)) {
                //创建邮件发送记录
                $offData = [
                    'jobId'             => $this->jobCompanyInfo['id'],
                    'resumeId'          => $this->memberResumeInfo['id'],
                    'offSiteJobApplyId' => $offSiteJobApplyId ?: '',
                ];
                if ($resumeAttachmentId > 0) {
                    $offData['resumeAttachmentId'] = $resumeAttachmentId;
                }
                if ($stuffFileId > 0) {
                    $offData['stuffFileId'] = $stuffFileId;
                }
                //站外投递
                $emailLogId                         = Producer::email($email, 1, EmailQueue::EMAIL_NOT_WILL_DELIVERY,
                    $offData);
                $offSiteJobApplyModel               = BaseOffSiteJobApply::findOne($offSiteJobApplyId);
                $offSiteJobApplyModel->email_log_id = $emailLogId;
                $offSiteJobApplyModel->save();
            }
        }

        //判断是否设置默认简
        if ($isDefault == BaseResumeAttachment::IS_DEFAULT_YES && $resumeAttachmentId > 0) {
            BaseResumeAttachment::setDefaultResume($resumeAttachmentId,
                $this->memberResumeInfo['member']['id']);//设置默认简历
        }

        $wxBindQrCodeImageUrl = $applySuccessMsg = '';
        if ($this->operationPlatform == CommonService::PLATFORM_WEB) {
            try {
                $data                 = WxPublic::getInstance($this->memberResumeInfo['member']['type'])
                    ->createBindQrCode($this->memberResumeInfo['id']);
                $wxBindQrCodeImageUrl = $data['url'];
                $applySuccessMsg      = '微信扫码绑定，实时接收投递反馈。';
            } catch (\Exception $e) {
                $wxBindQrCodeImageUrl = '';
                $applySuccessMsg      = '请及时关注邮箱、服务号等渠道发送的投递反馈';
            }
        }

        return [
            'toast_type'                => $this->applyToastType,
            'link'                      => $companyUrl,
            'qrcode_link'               => Yii::$app->params['doubleMeetingActivity2022']['deliverySuccessQrcode'],
            'apply_success_tips'        => $apply_success_tips,
            'apply_success_qrcode_tips' => $apply_success_qrcode_tips,
            'wxBindQrCodeImageUrl'      => $wxBindQrCodeImageUrl,
            'applySuccessMsg'           => $applySuccessMsg,
            'apply_status'              => $delivery_way == BaseJob::DELIVERY_WAY_LINK ? BaseJob::JOB_APPLY_STATUS_NO : BaseJob::JOB_APPLY_STATUS_YES,
            'applyId'                   => $applyId,
        ];
    }

    /**
     * 检查限制投递的用户
     * @return
     */
    private function checkLimitApplySpecial()
    {
        if (isset(self::LIMIT_APPLY[$this->companyId]) && !empty(self::LIMIT_APPLY[$this->companyId]) && in_array($this->memberResumeInfo['id'],
                self::LIMIT_APPLY[$this->companyId])) {
            throw new Exception('你已多次投递该单位,请勿重复投递！');
        }

        return true;
    }

    // 一连串的限制
    // 是否被禁止投递该单位
    // 是否已经达到了职位维度的投递次数上限
    // 是否已经达到了单位维度的投递次数上限
    private function checkLimitApplyJob()
    {
        $this->checkResumeConfigLimit();

        if ($this->applyToastType == self::TYPE_APPLY_SYSTEM) {
            if (BaseJobApply::checkJobApplyStatus($this->memberResumeInfo['member']['id'],
                    $this->jobCompanyInfo['id']) == BaseJob::JOB_APPLY_STATUS_YES && $this->deliveryWay != BaseJob::DELIVERY_WAY_LINK) {
                throw new Exception('您已投递，请勿重复投递！');
            }
        }

        // 用户被ban的情况，就直接抛出异常
        if ($this->jobLimitType == BaseJobApplyLimitConfig::TYPE_BAN) {
            $message = BaseJobApplyLimitConfig::MESSAGE_TYPE_LIST[$this->jobLimitMessageType];

            throw new MessageException($message);
        }

        // 非网址投递
        if ($this->deliveryType == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            // 看职位维度(非网址投递）
            $todayJobCount = BaseJobApplyRecord::find()
                ->where([
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    CUR_DATE,
                ])
                ->count();

            $day7JobCount = BaseJobApplyRecord::find()
                ->where([
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    date('Y-m-d', strtotime('-6 day')),
                ])
                ->count();

            if ($todayJobCount >= 30) {
                throw new MessageException('您今日已达投递次数上限（每天限投30次）');
            }
            // 7天内站内投递已达100次
            if ($day7JobCount >= 100) {
                throw new MessageException('您当前已达投递次数上限（7天内限投100次）');
            }

            // 3、已达职位维度降权阈值（读取 运营端-降权投递 配置）———提示3
            if ($this->jobLimitType == BaseJobApplyLimitConfig::TYPE_LOWER) {
                // 每天累积限投10次(职位维度有两个限制）第一个是每天累积10次
                if ($todayJobCount >= 10) {
                    throw new MessageException('您当前已达投递次数上限！请耐心等待');
                }

                // 7天内累积限投50次
                if ($day7JobCount >= 50) {
                    throw new MessageException('您当前已达投递次数上限！请耐心等待');
                }
            }

            // 当日已对该单位站内投递次数已达5次或30天已达15次；
            $todayCompanyCount = BaseJobApplyRecord::find()
                ->where([
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'company_id'    => $this->jobCompanyInfo['company_id'],
                    'delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    CUR_DATE,
                ])
                ->count();

            $day30CompanyCount = BaseJobApplyRecord::find()
                ->where([
                    'resume_id'     => $this->memberResumeInfo['id'],
                    'company_id'    => $this->jobCompanyInfo['company_id'],
                    'delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    date('Y-m-d', strtotime('-29 day')),
                ])
                ->count();

            if ($todayCompanyCount >= 5) {
                throw new MessageException(BaseJobApplyLimitConfig::MESSGAE_LOWER);
            }

            if ($day30CompanyCount >= 15) {
                throw new MessageException(BaseJobApplyLimitConfig::MESSGAE_LOWER);
            }

            if ($this->jobLimitType == BaseJobApplyLimitConfig::TYPE_LOWER) {
                // 2、已达单位维度降权阈值（读取 运营端-降权投递 配置）。
                // 对同一单位每天限投1次
                if ($todayCompanyCount >= 1) {
                    throw new MessageException(BaseJobApplyLimitConfig::MESSGAE_LOWER);
                }

                // 30天内限投3次
                if ($day30CompanyCount >= 3) {
                    throw new MessageException(BaseJobApplyLimitConfig::MESSGAE_LOWER);
                }
            }
        }
    }

    // 找这个用户有没有被限制，限制分两种，一种是禁止，一种是降权
    public function checkResumeConfigLimit()
    {
        $resumeId = $this->memberResumeInfo['id'];

        // 找到这个用户被限制的配置
        $resumeConfigList = BaseJobApplyLimitResume::find()
            ->select('a.job_apply_limit_config_id')
            ->alias('a')
            ->innerJoin(['b' => BaseJobApplyLimitConfig::tableName()], 'a.job_apply_limit_config_id = b.id')
            ->where(['resume_id' => $resumeId])
            ->column();

        // 获取用户标签
        $resumeTagIds       = BaseResumeTag::getTagList($resumeId);
        $resumeTagLimitList = BaseJobApplyLimitResumeTag::find()
            ->select('a.job_apply_limit_config_id')
            ->alias('a')
            ->innerJoin(['b' => BaseJobApplyLimitConfig::tableName()], 'a.job_apply_limit_config_id = b.id')
            ->where(['resume_tag_id' => $resumeTagIds])
            ->column();

        // 合并去重
        $configList = array_merge($resumeConfigList, $resumeTagLimitList);

        // 这个人才没有被配置限制
        if (count($configList) <= 0) {
            return true;
        }

        $isCooperation = $this->jobCompanyInfo['company']['is_cooperation'];
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            // 先去找禁止投递的（禁止投递全部合作单位）
            $cooperationLimitConfig = BaseJobApplyLimitCompanyTag::find()
                ->alias('a')
                ->innerJoin(['b' => BaseJobApplyLimitConfig::tableName()], 'a.job_apply_limit_config_id = b.id')
                ->select('b.id,b.type,message_type')
                ->where([
                    'job_apply_limit_config_id' => $configList,
                    'company_feature_tag_id'    => 0,
                    'b.type'                    => BaseJobApplyLimitConfig::TYPE_BAN,
                ])
                ->orderBy('type,message_type desc')
                ->asArray()
                ->one();

            if ($cooperationLimitConfig) {
                $this->setResumeConfigLimit($cooperationLimitConfig['type'], $cooperationLimitConfig['message_type']);

                // 如果这个提示已经是2了(那就是最高级别，不用往下走了
                if ($this->jobLimitMessageType == BaseJobApplyLimitConfig::MESSAGE_TYPE_TWO) {
                    return true;
                }
            }
        }

        $companyId = $this->jobCompanyInfo['company_id'];

        // 找配置里面是否有这个单位被ban的
        $companyIdLimitConfig = BaseJobApplyLimitCompany::find()
            ->alias('a')
            ->innerJoin(['b' => BaseJobApplyLimitConfig::tableName()], 'a.job_apply_limit_config_id = b.id')
            ->select('b.type,message_type')
            ->where([
                'job_apply_limit_config_id' => $configList,
                'company_id'                => $companyId,
                'type'                      => BaseJobApplyLimitConfig::TYPE_BAN,
            ])
            ->orderBy('type,message_type desc')
            ->asArray()
            ->one();

        if ($companyIdLimitConfig) {
            $this->setResumeConfigLimit($companyIdLimitConfig['type'], $companyIdLimitConfig['message_type']);
        }

        // 有ban了，就不考虑降权了
        if ($this->jobLimitType) {
            return true;
        }

        // 找配置里面是否有降权，降权和单位没有关系，只是和用户有关系
        $limitConfig = BaseJobApplyLimitConfig::find()
            ->select('type,message_type')
            ->where([
                'id'   => $configList,
                'type' => BaseJobApplyLimitConfig::TYPE_LOWER,
            ])
            ->orderBy('type,message_type desc')
            ->asArray()
            ->one();

        if ($limitConfig) {
            $this->setResumeConfigLimit($limitConfig['type'], $limitConfig['message_type']);

            return true;
        }

        return true;
    }

    public function setResumeConfigLimit($type, $message)
    {
        $this->jobLimitType        = $type;
        $this->jobLimitMessageType = $message;
    }

    private function filtrationStuffFileId($stuffFileId)
    {
        if (!$stuffFileId) {
            return '';
        }
        $memberId       = $this->memberResumeInfo['member']['id'];
        $stuffFileArray = explode(',', $stuffFileId);
        $idArray        = BaseFile::find()
            ->select('id')
            ->where([
                'id'         => $stuffFileArray,
                'creator_id' => $memberId,
            ])
            ->asArray()
            ->column();

        return implode(',', $idArray) ?: '';
    }

    /**
     * 换取平台类型
     */
    private function getPlatform()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                $platform = BaseJobApplyRecord::PLATFORM_PC;
                break;
            case self::PLATFORM_H5:
                $platform = BaseJobApplyRecord::PLATFORM_H5;
                break;
            case self::PLATFORM_MINI:
                $platform = BaseJobApplyRecord::PLATFORM_MINI;
                break;
            default:
                $platform = 0;
                break;
        }

        return $platform;
    }

    /**
     * 获取简历步数的链接
     */
    private function getResumeStepUrl($step)
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                if ($step == 1) {
                    $url = '/member/person/required/basic';
                } elseif ($step == 2) {
                    $url = '/member/person/required/education';
                } elseif ($step == 3) {
                    $url = '/member/person/required/intention';
                } else {
                    $url = '';
                }
                break;
            case self::PLATFORM_H5:
                $url = '/resume/index';
                break;
            case self::PLATFORM_MINI:
                $url = '/packages/resume/required';
                break;
            default:
                $url = '';
                break;
        }

        return $url;
    }

    /**
     * 获取简历完善度url
     */
    private function getResumeCompleteUrl()
    {
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
                $url = '/member/person/resume';
                break;
            case self::PLATFORM_H5:
                $url = '/resume/edit';
                break;
            case self::PLATFORM_MINI:
                $url = '/packages/resume/index';
                break;
            default:
                $url = '';
                break;
        }

        return $url;
    }

    /**
     * 权益验证
     */
    private function equityVerify()
    {
        //这里校验一下是否有投递置顶权益
        $resumeEquityPackageData = BaseResumeEquityPackage::getEquityList($this->memberResumeInfo['id'],
            BaseResumeEquitySetting::ID_DELIVERY_TOP, BaseResumeEquityPackage::STATUS_EXPIRE);
        $result                  = [
            'deliveryTopIsShow'             => false,
            'deliveryTopIsCheckBox'         => 2,
            'deliveryTopIsCheckBoxDisabled' => true,
            'deliveryTopSourceAmount'       => 0,
            'deliveryTopTips'               => '',
            'deliveryTopButton'             => false,
        ];
        if (count($resumeEquityPackageData) > 0) {
            $result['deliveryTopIsShow']       = true;
            $source_amount                     = array_sum(array_column($resumeEquityPackageData, 'amount'));
            $result['deliveryTopSourceAmount'] = $source_amount;
            if ($source_amount >= 1) {
                //检验一下该职位30天内是否有投递置顶权益
                $isEquity = BaseJobApplyTopEquityRecord::isUseEquity($this->jobCompanyInfo['id'],
                    $this->memberResumeInfo['id']);
                if ($isEquity) {
                    //30天内使用过
                    $result['deliveryTopTips'] = $this->textReplace($this->equity_used_30_days_tips, $source_amount);
                } else {
                    $result['deliveryTopIsCheckBox']         = 1;
                    $result['deliveryTopIsCheckBoxDisabled'] = false;
                    $result['deliveryTopTips']               = $this->textReplace($this->equity_remaining_n_number_tips,
                        $source_amount);
                }
            } else {
                $result['deliveryTopTips']   = $this->equity_remaining_0_number_tips;
                $result['deliveryTopButton'] = true;
            }
        }

        return $result;
    }

    /**
     * 权益扣除
     */
    private function equityDeduct($applyId)
    {
        //扣除权益资源
        $resume_equity_package_result = BaseResumeEquityPackage::deductEquityAmount($this->memberResumeInfo['id'],
            BaseResumeEquitySetting::ID_DELIVERY_TOP);
        if (isset($resume_equity_package_result['resume_equity_package_id']) && $resume_equity_package_result['resume_equity_package_id'] > 0) {
            $resume_equity_package_detail_result = BaseResumeEquityPackageDetail::deductEquityAmount($this->memberResumeInfo['id'],
                $resume_equity_package_result['package_category_id'], BaseResumeEquitySetting::ID_DELIVERY_TOP);
            //做一个投递置顶权益使用记录
            $model = new BaseJobApplyTopEquityRecord();
            $model->setAttributes([
                'job_id'                   => $this->jobCompanyInfo['id'],
                'resume_id'                => $this->memberResumeInfo['id'],
                'apply_id'                 => $applyId,
                'equity_package_detail_id' => $resume_equity_package_detail_result['resume_equity_package_detail_id'],
                'equity_status'            => BaseJobApplyTopEquityRecord::EQUITY_STATUS_EFFECT,
                'expire_type'              => BaseJobApplyTopEquityRecord::EXPIRE_TYPE_EFFECT,
                'add_time'                 => date('Y-m-d H:i:s'),
                'update_time'              => date('Y-m-d H:i:s'),
            ]);
            $model->save();
            //流水
            $equityIds     = [BaseResumeEquitySetting::ID_DELIVERY_TOP];
            $before_amount = [BaseResumeEquitySetting::ID_DELIVERY_TOP => $resume_equity_package_result['before_amount']];
            $after_amount  = [BaseResumeEquitySetting::ID_DELIVERY_TOP => $resume_equity_package_result['after_amount']];
            BaseResumeEquityActionRecord::saveActionRecord(date('Y-m-d H:i:s'), $this->memberResumeInfo['id'],
                $equityIds, BaseResumeEquityActionRecord::EQUITY_TYPE_USED,
                BaseResumeEquityActionRecord::ACTION_USED_DELIVERY_TOP, $applyId,
                BaseResumeEquityActionRecord::TYPE_RELATION_REMARK_DELIVERY_TOP,
                BaseResumeEquityActionRecord::TYPE_OPERATION_RESUME, $this->memberResumeInfo['id'], 0, $before_amount,
                $after_amount);
        } else {
        }

        return true;
    }
}