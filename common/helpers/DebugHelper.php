<?php

namespace common\helpers;

use Yii;

class DebugHelper
{

    public static function log($message, $path = '')
    {
        if (empty($path)) {
            $path = Yii::getAlias('@log/debug/1.log');
        }

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        $time = date('Y-m-d H:i:s');

        file_put_contents($path, $time . PHP_EOL, FILE_APPEND);
        // 追加写入执行这个方法的行数信息
        $backtrace = debug_backtrace();
        $line     = $backtrace[0]['line'];
        $file     = $backtrace[0]['file'];
        $message  = "Line: {$line} File: {$file} " . var_export($message, true);
        file_put_contents($path, $message . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function payPointLog($message, $path = '')
    {
        if (empty($path)) {
            $path = Yii::getAlias('@log/debug/payPointLog.log');
        }

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function wxLog($message)
    {
        $path = Yii::getAlias('@log/debug/wxWorkServiceCallback.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function wxMessageLog($message)
    {
        $path = Yii::getAlias('@log/debug/wxMessageLog.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function emailLog($message)
    {
        $path = Yii::getAlias('@log/debug/emailError.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function meilisearchIndexLog($message)
    {
        $path = Yii::getAlias('@log/debug/meilisearch/index/' . date('Ymd') . '.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function meilisearchSearchLog($message)
    {
        $path = Yii::getAlias('@log/debug/meilisearch/search/' . date('Ymd') . '.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function searchLog($message)
    {
        $path = Yii::getAlias('@log/debug/memberSearch.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    // 支付回调日志
    public static function payNotifyLog($message)
    {
        $path = Yii::getAlias('@log/debug/payNotify.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    // 支付拉起异常日志
    public static function payFailLog($message)
    {
        $path = Yii::getAlias('@log/debug/payFail.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    /**
     * 小程序快捷登录日志
     * @param $message
     * @return void
     */
    public static function miniAppLoginLog($message)
    {
        $path = Yii::getAlias('@log/debug/miniAppLogin.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function errorMobileSign($message)
    {
        $path = Yii::getAlias('@log/debug/errorMobileSign.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, CUR_DATETIME . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function chat($message)
    {
        $path = Yii::getAlias('@log/debug/chat.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function adminController($message)
    {
        $path = Yii::getAlias('@log/debug/adminController.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function companyController($message)
    {
        $path = Yii::getAlias('@log/debug/companyController.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function companyLogin($message)
    {
        $path = Yii::getAlias('@log/debug/companyLogin.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function companyApplyList($message)
    {
        $path = Yii::getAlias('@log/debug/companyApplyList.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function registrationForm($message)
    {
        $path = Yii::getAlias('@log/debug/registrationForm.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function afterAnnouncementUpdateJob($message)
    {
        $path = Yii::getAlias('@log/debug/afterAnnouncementUpdateJob.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function jobRecommended($message)
    {
        $path = Yii::getAlias('@log/debug/jobRecommended.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function emailCallback($message)
    {
        $path = Yii::getAlias('@log/debug/emailCallback.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function resumeLibrary($message)
    {
        $path = Yii::getAlias('@log/debug/resumeLibrary.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function sms($message)
    {
        $path = Yii::getAlias('@log/debug/sms.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function wxWorkCallback($message)
    {
        $path = Yii::getAlias('@log/debug/wxWorkCallback.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function wxWorkServiceCallback($message)
    {
        $path = Yii::getAlias('@log/debug/wxWorkServiceCallback.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function miniCheck($message)
    {
        $path = Yii::getAlias('@log/debug/miniCheck.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function adminDownload($message)
    {
        $path = Yii::getAlias('@log/debug/adminDownload.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function resumeUpload($message)
    {
        $path = Yii::getAlias('@log/debug/resumeUpload.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function cookies($message)
    {
        $path = Yii::getAlias('@log/debug/cookies.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        file_put_contents($path, date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function announcementAfter($message, $path = '')
    {
        $path = Yii::getAlias('@log/debug/announcementAfter.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        $time = date('Y-m-d H:i:s');

        file_put_contents($path, $time . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

    public static function click($message)
    {
        $path = Yii::getAlias('@log/debug/click.log');

        // 递归创建文件
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);
        }

        $time = date('Y-m-d H:i:s');

        file_put_contents($path, $time . PHP_EOL, FILE_APPEND);

        file_put_contents($path, var_export($message, true) . PHP_EOL, FILE_APPEND);
    }

}
