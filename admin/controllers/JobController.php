<?php

namespace admin\controllers;

use admin\models\Job;
use admin\models\JobHandleLog;
use admin\models\UploadForm;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\service\CommonService;
use common\helpers\ValidateHelper;
use common\service\downloadTask\DownLoadTaskApplication;
use common\service\job\AuditService;
use common\service\job\BaseService;
use common\service\job\JobService;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class JobController extends BaseAdminController
{
    /**
     * 根据条件获取职位列表、职位查询
     * @return Response|\yii\web\Response
     */
    public function actionJobList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            if ($request['export'] == 1) {
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_JOB_LIST, $request);

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }
            //$list = Job::getJobList(FormatConverter::convertHump($request));
            $list = Job::getJobListNew(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据条件获取职位列表、职位查询
     * @return Response|\yii\web\Response
     */
    public function actionSimpleJobList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            if ($request['export'] == 1) {
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_JOB_LIST, $request);

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }
            //$list = Job::getSimpleJobList(FormatConverter::convertHump($request));
            $list = Job::getJobListNew(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据条件获取职位列表、职位查询--b
     * @return Response|\yii\web\Response
     */
    public function actionSliceJobList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            if ($request['export'] == 1) {
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_SLICE_JOB_LIST,
                    FormatConverter::convertHump($request));

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }
            $list = Job::getSliceJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位详情
     * @return Response|\yii\web\Response
     */
    public function actionGetDetails()
    {
        $request = Yii::$app->request->get();
        try {
            if (!$request['id']) {
                return '职位Id不能为空';
            }

            return $this->success(Job::getDetails(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑职位
     * @return Response|\yii\web\Response
     */
    public function actionJobEdit()
    {
        $request = Yii::$app->request->post();
        if ($request['jobId'] < 0) {
            return $this->fail('职位id不能为空,参数错误');
        }
        try {
            //            $checkData = [
            //                'natureType',
            //                'wageType',
            //                'minWage',
            //                'maxWage',
            //                'isNegotiable',
            //                'amount',
            //                'provinceId',
            //                'cityId',
            //                'address',
            //                'periodDate',
            //                'duty',
            //                'requirement',
            //                'jobId',
            //                'experienceType',
            //                'ageType',
            //                'titleType',
            //                'politicalType',
            //                'abroadType',
            //                'department',
            //                'abroadType',
            //            ];
            //            foreach ($checkData as $list) {
            //                if (!isset($request[$list])) {
            //                    throw new Exception('参数' . $list . '不能为空');
            //                }
            //            }
            //Job::jobEdit(FormatConverter::convertHump($request));
            $service = new JobService();
            //判断是编辑还是新增
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setOparetion(JobService::JOB_EDIT)
                ->setData($request)
                ->run();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位刷新
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobRefresh()
    {
        $request     = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$request['id']) {
                throw new Exception('职位Id不能为空');
            }
            Job::jobRefresh($request['id']);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位刷新
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionBatchJobRefresh()
    {
        $ids = Yii::$app->request->post('ids');
        if (!$ids) {
            return $this->fail('职位Id不能为空');
        }
        $idsArr       = explode(',', $ids);
        $errorContent = '';
        foreach ($idsArr as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                Job::jobRefresh($id);
                $transaction->commit();
            } catch (\Exception $e) {
                $errorContent .= '职位id：' . $id . ' 执行刷新失败，原因：' . $e->getMessage() . "<br/>";
                $transaction->rollBack();
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 职位审核
     * @return Response|\yii\web\Response
     */
    public function actionJobExamine()
    {
        $request     = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //Job::JobExamine(FormatConverter::convertHump($request));
            $adminId      = Yii::$app->user->id;
            $auditService = new AuditService();
            $params       = [
                'jobId'       => $request['id'],
                'auditStatus' => $request['auditStatus'],
                'opinion'     => $request['opinion'],
            ];

            $auditService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setData($params)
                ->run();
            $transaction->commit();

            return $this->result('', 1, []);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位下线
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobOffline()
    {
        $request     = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$request['id']) {
                throw new Exception('职位Id不能为空');
            }
            Job::JobOffline(FormatConverter::convertHump($request));
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量处理职位下线
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionBatchJobOffline()
    {
        $request = Yii::$app->request->post();
        if (!$request['ids']) {
            return $this->fail('职位Id不能为空');
        }
        if (!$request['reason']) {
            return $this->fail('下线原因不能为空');
        }
        $ids          = explode(',', $request['ids']);
        $errorContent = '';
        foreach ($ids as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $data = [
                    'id'     => $id,
                    'reason' => $request['reason'],
                ];
                Job::JobOffline($data);
                $transaction->commit();
            } catch (\Exception $e) {
                $errorContent .= '职位id：' . $id . ' 执行下线失败，原因：' . $e->getMessage() . "<br/>";
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 职位再发布
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobReleaseAgain()
    {
        $request     = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$request['id']) {
                throw new Exception('职位Id不能为空');
            }
            Job::jobReleaseAgain($request['id']);

            $transaction->commit();
            $res = Job::majorMessageNotice($request['id']);
            if ($res['res'] == false) {
                return $this->success($res['msg']);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量再发布
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionBatchJobReleaseAgain()
    {
        $ids = Yii::$app->request->post('ids');
        if (!$ids) {
            return $this->fail('职位Id不能为空');
        }
        $ids          = explode(',', $ids);
        $errorContent = '';
        $msg          = '';
        foreach ($ids as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                Job::jobReleaseAgain($id);
                $transaction->commit();
                $res = Job::majorMessageNotice($id);
                if ($res['res'] == false) {
                    $msg .= $res['msg'] . '。';
                }
            } catch (\Exception $e) {
                $errorContent .= '职位id：' . $id . ' 执行再发布失败，原因：' . $e->getMessage() . "<br/>";
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent]);
        } else {
            return $this->success($msg);
        }
    }

    /**
     * 获取职位操作列表
     * @return Response|\yii\web\Response
     */
    public function actionGetJobHandleList()
    {
        $request = Yii::$app->request->get();
        try {
            return $this->success(JobHandleLog::getJobHandleList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务收到简历列表
     * @return Response|\yii\web\Response
     */
    public function actionJobApplyList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;

        try {
            if (!$adminId || !$request['jobId']) {
                throw new Exception('非法访问,未登录或未传参数');
            }

            return $this->success(Job::jobApplyList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务面试邀约列表
     * @return Response|\yii\web\Response
     */
    public function actionJobInvitationList()
    {
        $request = Yii::$app->request->get();
        try {
            return $this->success(Job::jobInvitationList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务下载简历列表
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobResumeDownloadList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        try {
            if (!$adminId || !$request['jobId']) {
                throw new Exception('非法访问,未登录或未传参数');
            }

            return $this->success(Job::jobResumeDownloadList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核处理历史
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobHandleLog()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        try {
            if (!$adminId || !$request['jobId']) {
                throw new Exception('非法访问,未登录或未传参数');
            }

            return $this->success(Job::jobHandleLog(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核列表
     * @return Response|\yii\web\Response
     */
    public function actionJobAuditList()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            $list = Job::getJobAuditList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核详情
     * @return Response|\yii\web\Response
     */
    public function actionJobAuditInfo()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::jobAuditInfo(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位地址列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyAddressList()
    {
        $request = Yii::$app->request->get();

        try {
            if (!$request['memberId']) {
                throw new Exception('参数不能为空');
            }

            return $this->success(Job::getCompanyAddressList(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 显示/隐藏职位
     * @return Response|\yii\web\Response
     */
    public function actionChangeJobShow()
    {
        $request = Yii::$app->request->post();
        try {
            if (!$request['id']) {
                throw new Exception('职位Id不能为空');
            }
            Job::changeJobShow(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量显示/隐藏职位
     * @return Response|\yii\web\Response
     */
    public function actionBatchChangeJobShow()
    {
        $request = Yii::$app->request->post();
        if (!$request['ids']) {
            return $this->fail('职位Id不能为空');
        }
        if (!$request['isShow']) {
            return $this->fail('显示/隐藏状态不能为空');
        }
        $idsArr       = explode(',', $request['ids']);
        $errorContent = '';
        foreach ($idsArr as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                //拼装下结构
                $data = [
                    'id'      => $id,
                    'is_show' => $request['isShow'],
                ];
                Job::changeJobShow($data);
                $transaction->commit();
            } catch (\Exception $e) {
                $errorContent .= '职位id：' . $id . ' 执行隐藏/展示失败，原因：' . $e->getMessage() . "<br/>";
                $transaction->rollBack();
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 内容管理下职位列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAgentJobList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::getAgentJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位检索
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::getCompanyList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发布/保存新职位
     * @return Response|\yii\web\Response
     */
    public function actionCreate()
    {
        $request = Yii::$app->request->post();
        $tran    = Yii::$app->db->beginTransaction();
        try {
            //            Job::create($request);
            $service = new JobService();
            //判断是编辑还是新增
            if (isset($request['jobId']) && $request['jobId'] > 0) {
                //有职位ID的那就是编辑
                $service->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->setOparetion(JobService::JOB_EDIT)
                    ->setData($request)
                    ->run();
            } else {
                //职位ID不存在或者小于0的那就是新增
                $service->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->setOparetion(JobService::JOB_ADD)
                    ->setData($request)
                    ->run();
            }
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            $path = 'job_temp_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobBatchImport()
    {
        $request = Yii::$app->request->post();
        // todo 这里校验一下数据
        $filePath  = $request['filePath'];
        $companyId = $request['companyId'];
        if (strlen($filePath) < 1 || strlen($companyId) < 1) {
            return $this->fail();
        }

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $data = Job::jobBatchImport($request);
            $transaction->commit();

            return $this->success($data);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取报名方式
     * @return Response|\yii\web\Response
     */
    public function actionGetSignUpList()
    {
        try {
            $data = Job::getSignUpList();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除职位
     * @return Response|\yii\web\Response
     */
    public function actionDeleteAgentJob()
    {
        $request = Yii::$app->request->post();
        try {
            Job::deleteAgentJob($request);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 模糊查找职位列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchJobList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::searchJobList($request);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 模糊查找职位列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchJobInfoList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::searchJobInfoList($request);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核状态
     * @return Response|\yii\web\Response
     */
    public function actionJobAuditStatusList()
    {
        try {
            $list = ArrayHelper::obj2Arr(BaseJob::AUDIT_STATUS_CHECK_NAME);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 招聘状态
     * @return Response|\yii\web\Response
     */
    public function actionJobStatusList()
    {
        try {
            $list = ArrayHelper::obj2Arr(BaseJob::STATUS_NAME);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改投递学历限制
     * @return Response|\yii\web\Response
     */
    public function actionChangeDeliveryEducationLimit()
    {
        $request = Yii::$app->request->post();
        if (!$request['id']) {
            return $this->fail('职位id不能为空');
        }
        if (!$request['isLimit']) {
            return $this->fail('限制状态不能为空');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = [
                'id'      => $request['id'],
                'isLimit' => $request['isLimit'],
                'type'    => $request['isLimit'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_LIMIT_EDUCATION : BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_EDUCATION,
            ];
            Job::changeEducationLimit($data);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改编制
     */
    public function actionBatchJobEstablishment()
    {
        $request = Yii::$app->request->post();
        if (!$request['id']) {
            return $this->fail('职位id不能为空');
        }
        if ($request['type'] == 1 && empty($request['set'])) {
            return $this->fail('请选择编制');
        }

        //循环职位id修改编制
        $ids = explode(',', $request['id']);
        foreach ($ids as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $item = BaseJob::findOne($id);
                if (!$item) {
                    continue;
                }
                $item->is_establishment = $request['type'];
                if ($request['type'] == 1) {
                    $item->establishment_type = $request['set'];
                } else {
                    $item->establishment_type = '';
                }
                $item->save();
                $transaction->commit();

                // 处理公告缓存
                // 刷公告
                if ($item->announcement_id) {
                    Producer::afterAnnouncementUpdateJob($item->announcement_id);
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
            }
        }

        return $this->success();
    }

    /**
     * 批量修改投递学历限制
     * @return Response|\yii\web\Response
     */
    public function actionBatchChangeDeliveryEducationLimit()
    {
        $request = Yii::$app->request->post();
        if (!$request['ids']) {
            return $this->fail('职位id不能为空');
        }
        if (!$request['isLimit']) {
            return $this->fail('限制状态不能为空');
        }
        $idsArr       = explode(',', $request['ids']);
        $errorContent = '';
        foreach ($idsArr as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $data = [
                    'id'      => $id,
                    'isLimit' => $request['isLimit'],
                    'type'    => $request['isLimit'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_LIMIT_EDUCATION : BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_EDUCATION,
                ];
                Job::changeEducationLimit($data);
                $transaction->commit();
            } catch (\Exception $e) {
                if ($request['isLimit'] = 1) {
                    $action = '添加学历限制';
                } else {
                    $action = '取消学历限制';
                }
                $errorContent .= '职位id：' . $id . ' 执行' . $action . '失败，原因：' . $e->getMessage() . "<br/>";

                $transaction->rollBack();
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 修改投递应聘材料限制
     * @return Response|\yii\web\Response
     */
    public function actionChangeDeliveryFileLimit()
    {
        $request = Yii::$app->request->post();
        if (!$request['id']) {
            return $this->fail('职位id不能为空');
        }
        if (!$request['isLimit']) {
            return $this->fail('限制状态不能为空');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = [
                'id'      => $request['id'],
                'isLimit' => $request['isLimit'],
                'type'    => $request['isLimit'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_LIMIT_FILE : BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_FILE,
            ];
            Job::changeEducationLimit($data);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改投递学历限制
     * @return Response|\yii\web\Response
     */
    public function actionBatchChangeDeliveryFileLimit()
    {
        $request = Yii::$app->request->post();
        if (!$request['ids']) {
            return $this->fail('职位id不能为空');
        }
        if (!$request['isLimit']) {
            return $this->fail('限制状态不能为空');
        }
        $idsArr       = explode(',', $request['ids']);
        $errorContent = '';
        foreach ($idsArr as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $data = [
                    'id'      => $id,
                    'isLimit' => $request['isLimit'],
                    'type'    => $request['isLimit'] == 1 ? BaseJobHandleLog::HANDLE_TYPE_LIMIT_FILE : BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_FILE,
                ];
                Job::changeEducationLimit($data);
                $transaction->commit();
            } catch (\Exception $e) {
                if ($request['isLimit'] = 1) {
                    $action = '添加附件限制';
                } else {
                    $action = '取消附件限制';
                }
                $errorContent .= '职位id：' . $id . ' 执行' . $action . '失败，原因：' . $e->getMessage() . "<br/>";

                $transaction->rollBack();
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 获取筛选投递类型配置项
     * @return Response|\yii\web\Response
     */
    public function actionDeliveryTypeSearch()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::DELIVERY_TYPE_NAME));
    }

    /**
     * 账号筛选项
     */
    public function actionMemberAccountFilter()
    {
        //获取单位ID
        $companyId = Yii::$app->request->get('companyId');
        $contact   = Yii::$app->request->get('contact');
        $limit     = Yii::$app->request->get('limit');
        if ($companyId <= 0) {
            return $this->fail('参数错误');
        }
        //获取单位下的所有账号
        $query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id=cmi.member_id')
            ->andWhere(['cmi.company_id' => $companyId]);
        if ($contact) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'm.email',
                    $contact,
                ],
                [
                    'like',
                    'cmi.contact',
                    $contact,
                ],
                [
                    'like',
                    'cmi.department',
                    $contact,
                ],
            ]);
        }
        if ($limit) {
            $query->limit($limit);
        }

        $list = $query->select([
            'cmi.id',
            'm.email',
            'm.mobile',
            'cmi.contact',
            'cmi.department',
            'cmi.company_member_type',
        ])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['contact_text'] = $item['contact'];
            if ($item['department']) {
                $item['contact_text'] .= ' /' . $item['department'];
            }
        }

        return $this->success($list);
    }

    /**
     * 识别职位协同子账号
     */
    public function actionMemberAccountIdentify()
    {
        try {
            //获取单位ID
            $companyId = Yii::$app->request->get('companyId');
            if ($companyId <= 0) {
                throw new Exception('参数错误');
            }
            //获取填写的投递地址
            $apply_address = Yii::$app->request->get('applyAddress');
            if (empty($apply_address)) {
                throw new Exception('识别失败，请手动选择协同子账号');
            }
            //将投递地址转换为数组
            $apply_address_arr = explode(',', $apply_address);
            if (count($apply_address_arr) > 3) {
                throw new Exception('识别失败，邮箱地址不能超过3个');
            }
            //去重
            $apply_address_arr = array_unique($apply_address_arr);
            //循环验证邮箱地址
            $identify = [];
            foreach ($apply_address_arr as $item) {
                //对邮箱进行格式验证
                if (!ValidateHelper::isEmail($item)) {
                    //格式错误
                    throw new Exception('识别失败，邮箱格式错误');
                }
                $record_id = BaseCompanyMemberInfo::validateEmailMember($companyId, $item);
                if ($record_id) {
                    array_push($identify, $record_id);
                }
            }
            if (empty($identify)) {
                throw new Exception('邮箱地址不匹配，请手动选择协同子账号');
            } else {
                return $this->success([
                    'contact_identify' => $identify[0],
                    'identify'         => $identify,
                    'msg'              => '识别成功',
                ]);
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改职位联系人与协同子账号
     * @return Response|\yii\web\Response
     */
    public function actionMemberAccountEditBatch()
    {
        try {
            $request = Yii::$app->request->post();
            if (empty($request['jobIds']) || empty($request['companyId'])) {
                throw new Exception('参数错误');
            }
            if (empty($request['jobContactId'])) {
                throw new Exception('联系人必需选择');
            }
            //判断单位是否是合作单位
            $company_info = BaseCompany::findOne($request['companyId']);
            if ($company_info->is_cooperation != BaseJob::IS_CONSUME_RELEASE_YES) {
                throw new Exception('该单位不是合作单位');
            }
            //先验证一下jobIds是不是同一家单位的职位
            $jobIds = explode(',', $request['jobIds']);
            $job    = BaseJob::find()
                ->where(['id' => $jobIds])
                ->select(['company_id'])
                ->asArray()
                ->all();
            if (count(array_unique(array_column($job, 'company_id'))) != 1) {
                throw new Exception('职位不属于同一家单位');
            }
            //获取单位下的所有账号
            $account_list = BaseCompanyMemberInfo::find()
                ->where(['company_id' => $request['companyId']])
                ->asArray()
                ->all();
            $account_ids  = array_column($account_list, 'id');
            //联系人判断
            if ($request['jobContactId'] && !in_array($request['jobContactId'], $account_ids)) {
                throw new Exception('联系人不属于该单位');
            }
            //协同账号判断
            $synergyIds = [];
            if (count($request['jobContactSynergyIds']) > 0) {
                $synergyIds = $request['jobContactSynergyIds'];
                if (count(array_intersect($synergyIds, $account_ids)) != count($synergyIds)) {
                    throw new Exception('协同账号不属于该单位');
                }
            }
            //获取单位主账号信息
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'company_id'          => $company_info['id'],
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
            if (!in_array($request['jobContactId'],
                    array_unique($request['jobContactSynergyIds'])) && $request['jobContactId'] != $companyMemberInfo->id) {
                throw new Exception('职位联系人必须是协同账号或者是单位主账号');
            }
            //更新职位的联系人和协同账号
            foreach ($jobIds as $jobId) {
                $announcement_id = BaseJob::findOneVal(['id' => $jobId], 'announcement_id');
                $insert          = [
                    'job_id'                 => $jobId,
                    'company_id'             => $request['companyId'],
                    'company_member_info_id' => $request['jobContactId'],
                    'announcement_id'        => $announcement_id,
                ];
                //联系人更新
                BaseJobContact::add($insert);
                //协同账号更新
                $insert_synergy = [
                    'job_id'                 => $jobId,
                    'company_id'             => $request['companyId'],
                    'company_member_info_id' => $synergyIds,
                    'announcement_id'        => $announcement_id,
                ];
                BaseJobContactSynergy::addBatch($insert_synergy);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改职位的福利前置检校验
     */
    public function actionBatchEditWelfareCheck()
    {
        try {
            $jobIds = Yii::$app->request->post('jobIds');
            if (!$jobIds) {
                throw new Exception('参数错误');
            }
            //检查一下jobids 是否同属一个公告
            $jobIdsArr      = explode(',', $jobIds);
            $jobIdOne       = $jobIdsArr[0];
            $jobInfo        = BaseJob::findOne($jobIdOne);
            $announcementId = $jobInfo->announcement_id;
            if (!$announcementId) {
                throw new Exception('含有纯职位，请勾选同一公告下后再修改福利待遇！');
            }
            $count = BaseJob::find()
                ->where([
                    'id'              => $jobIdsArr,
                    'announcement_id' => $announcementId,
                ])
                ->count();
            if (count($jobIdsArr) != $count) {
                throw new Exception('勾选的职位不是同属一个公告');
            }

            return $this->success(['companyMemberId' => BaseCompany::findOne($jobInfo->company_id)->member_id]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量修改职位的福利待遇
     */
    public function actionBatchEditWelfare()
    {
        try {
            $jobIds     = Yii::$app->request->post('jobIds');
            $welfareIds = Yii::$app->request->post('welfareIds', '');
            if (!$jobIds) {
                throw new Exception('参数错误');
            }
            //检查一下jobids 是否同属一个公告
            $jobIdsArr      = explode(',', $jobIds);
            $jobIdOne       = $jobIdsArr[0];
            $announcementId = BaseJob::findOne($jobIdOne)->announcement_id;
            if (!$announcementId) {
                throw new Exception('含有纯职位，请勾选同一公告下后再修改福利待遇！');
            }
            $count = BaseJob::find()
                ->where([
                    'id'              => $jobIdsArr,
                    'announcement_id' => $announcementId,
                ])
                ->count();
            if (count($jobIdsArr) != $count) {
                throw new Exception('勾选的职位不是同属一个公告');
            }
            $res = BaseJob::updateAll(['welfare_tag' => $welfareIds], ['id' => $jobIdsArr]);
            if ($res) {
                Producer::afterAnnouncementUpdateJob($announcementId);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}