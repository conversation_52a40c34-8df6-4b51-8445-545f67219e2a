<?php

namespace common\helpers;

use phpDocumentor\Reflection\DocBlock\Tags\PropertyRead;
use yii\helpers\BaseStringHelper;

class StringHelper extends BaseStringHelper
{

    /**
     * 生成指定长度的随机数字
     *
     * @param int $length 随机数字的长度，默认为4
     * @return string 生成的随机数字字符串
     */
    public static function randNumber($length = 4)
    {
        $number = '';

        for ($i = 1; $i <= $length; $i++) {
            $number .= rand(0, 9);
        }

        return $number;
    }

    /**
     * 生成指定长度的随机文本
     *
     * @param int $length 随机文本的长度，默认为4
     * @return string 生成的随机文本字符串
     */
    public static function randText($length = 4)
    {
        $chars     = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $str       = '';
        $chars_len = strlen($chars) - 1;

        for ($i = 0; $i < $length; $i++) {
            $str .= $chars[mt_rand(0, $chars_len)];
        }

        return $str;
    }

    /**
     * 生成指定长度的随机文本和数字组合
     *
     * @param int $tetLength 随机文本的长度，默认为4
     * @param int $numLength 随机数字的长度，默认为6
     * @return string 生成的随机文本和数字组合字符串
     */
    public static function randTextAndNumber($tetLength = 4, $numLength = 6)
    {
        return strtolower(substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0,
                $tetLength) . substr(str_shuffle('0123456789'), 0, $numLength));
    }

    /**
     * 将数字转换为百分比格式
     *
     * @param float $num    要转换的数字
     * @param int   $digits 保留的小数位数，默认为2
     * @return string 转换后的百分比字符串
     */
    public static function percent($num, $digits = 2)
    {
        if (!is_numeric($num)) {
            return '';
        }

        return round($num * 100, $digits) . "%";
    }

    /**
     * 从文本中提取手机号码
     *
     * @param string $text 包含手机号码的文本
     * @return string 提取到的手机号码，如果没有找到则返回空字符串
     */
    public static function getMobile($text)
    {
        preg_match_all('/1[3456789][0-9]{8,10}/', $text, $match);
        foreach ($match[0] as $mobile) {
            return $mobile;
        }

        return '';
    }

    /**
     * 格式化金融数字，保留两位小数
     *
     * @param float $number 要格式化的数字
     * @return string 格式化后的金融数字字符串
     */
    public static function formateFinance($number)
    {
        return number_format(sprintf("%.2f", $number), 2);
    }

    /**
     * 对数组中的所有字符串进行trim操作
     *
     * @param array $array 要处理的数组
     * @return array 处理后的数组
     */
    public static function arrayTrim($array)
    {
        $result = [];
        foreach ($array as $key => $item) {
            if (is_array($item) || is_object($item)) {
                $result[trim($key)] = self::arrayTrim((array)$item);
            } else {
                $result[trim($key)] = $item;
            }
        }

        return $result;
    }

    /**
     * 判断字符串是否为空，为空则返回'-'，否则返回原字符串
     *
     * @param string $str 要判断的字符串
     * @return string 判断结果
     */
    public static function isEmpty($str)
    {
        if (empty($str)) {
            return '-';
        } else {
            return $str;
        }
    }

    /**
     * 截取字符串并添加省略号
     *
     * @param string $str    要截取的字符串
     * @param int    $length 截取的长度
     * @return string 截取后的字符串，如果字符串长度超过指定长度则添加省略号
     */
    public static function subStr($str, $length)
    {
        $newStr = mb_substr($str, 0, $length, 'utf-8');
        if ($str == $newStr) {
            return $str;
        }

        return $newStr . '...';
    }

    /**
     * 删除字符串前后指定的特殊字符
     *
     * @param string $text      要处理的字符串
     * @param string $character 要删除的特殊字符
     * @return string 处理后的字符串
     */
    public static function subtractString($text, $character)
    {
        if (substr($text, 0, 1) == $character || substr($text, -1, 1) == $character) {
            return str_replace($character, '', $text);
        } else {
            return $text;
        }
    }

    /**
     * 将文本中的换行符替换为HTML的<br/>标签
     *
     * @param string $text 要处理的文本
     * @return string 处理后的文本
     */
    public static function changeLineFeed($text)
    {
        return str_replace("\n", "<br/>", $text);
    }

    /**
     * 清除数据中的换行符
     *
     * @param mixed $data          要处理的数据
     * @param array $noChangeField 不需要清除换行符的字段
     * @param bool  $multiple      是否处理多维数组
     * @return mixed 处理后的数据
     */
    public static function cleanLineFeed($data, $noChangeField = [], $multiple = true)
    {
        if ($noChangeField) {
            foreach ($data as $k => &$v) {
                if (!in_array($k, $noChangeField)) {
                    $data[$k] = str_replace(PHP_EOL, '', $v);
                }
            }
        } else {
            if ($multiple) {
                foreach ($data as $k => &$v) {
                    $data[$k] = str_replace(PHP_EOL, '', $v);
                }
            } else {
                $data = str_replace(PHP_EOL, '', $data);
            }
        }

        return $data;
    }

    /**
     * 将字符串转换为布尔类型
     *
     * @param mixed $boolStr 要转换的字符串或布尔值
     * @return bool 转换后的布尔值
     */
    public static function strToBool($boolStr)
    {
        if (is_bool($boolStr)) {
            return $boolStr;
        }

        if (is_string($boolStr)) {
            $boolStr = strtolower($boolStr);
            if ($boolStr === 'true') {
                return true;
            } elseif ($boolStr === 'false') {
                return false;
            }
        }
    }

    /**
     * 邮箱字符串脱敏
     *
     * @param string $email 要脱敏的邮箱字符串
     * @return string 脱敏后的邮箱字符串
     */
    public static function strEmailDes($email)
    {
        if (empty($email)) {
            return $email;
        }
        $emailHead       = strstr($email, '@', true);
        $emailEnd        = strstr($email, '@');
        $emailHeadLength = strlen($emailHead);
        if ($emailHeadLength > 4) {
            $email = substr($emailHead, 0, $emailHeadLength - 4) . str_repeat('*', 4) . $emailEnd;
        } else {
            $email = substr($emailHead, 0, 1) . str_repeat('*', $emailHeadLength - 1) . $emailEnd;
        }

        return $email;
    }

    /**
     * 手机号码脱敏
     * @param $email
     * @return string
     */
    public static function strMobileDes($mobile)
    {
        if (empty($mobile)) {
            return $mobile;
        }
        $mobileLength = strlen($mobile);

        //手机号最后四位替换成*
        return substr($mobile, 0, $mobileLength - 4) . str_repeat('*', 4);
    }

    /**
     * 生成指定长度的随机字符串
     * @param int $n 随机字符串的长度，默认为10
     * @return string 生成的随机字符串
     */
    public static function getRandString($n = 10): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $randomString = '';

        for ($i = 0; $i < $n; $i++) {
            $index = rand(0, strlen($characters) - 1);

            $randomString .= $characters[$index];
        }

        return $randomString;
    }

    /**
     * 替换英文引号
     *
     * @param string $string 要处理的字符串
     * @return string 处理后的字符串
     */
    public static function changeQuotationMark($string)
    {
        $string = 'a' . $string . 'a';
        $flag   = false;
        while (false !== ($i = strpos($string, '"'))) {
            $begin_string = substr($string, 0, $i);
            $end_string   = substr($string, $i + 1 - strlen($string));
            $quot         = $flag ? '”' : '“';
            $flag         = !$flag;
            $string       = $begin_string . $quot . $end_string;
        }

        return trim($string, 'a');
    }

    /**
     * 获取手机区号的数字段，去掉前方的+号
     *
     * @param string $mobileCode 手机区号
     * @return string 处理后的手机区号
     */
    public static function getMobileCodeNumber($mobileCode)
    {
        $firstString = substr($mobileCode, 0, 1);
        if ($firstString == '+') {
            $mobileCode = substr($mobileCode, 1);
        }

        return $mobileCode;
    }

    /**
     * 获取完整手机区号，前面加上+号
     *
     * @param string $mobileCode 手机区号
     * @return string 处理后的手机区号
     */
    public static function getFullMobileCode($mobileCode)
    {
        if (!empty($mobileCode)) {
            $firstString = substr($mobileCode, 0, 1);
            if ($firstString != '+') {
                $mobileCode = '+' . $mobileCode;
            }

            return $mobileCode;
        } else {
            return '+86';
        }
    }

    /**
     * 格式化薪资，如果薪资大于等于10000，则单位为万
     *
     * @param float $heat 要格式化的薪资
     * @return string 格式化后的薪资字符串
     */
    public static function formatHeat($heat)
    {
        if ($heat >= 10000) {
            $heat = round($heat / 10000, 1);
            $unit = 'w';

            return $heat . $unit;
        } else {
            return '';
        }
    }

    /**
     * 将前端文本转换为数组，文本中的换行符作为分隔符
     *
     * @param string $text 前端文本
     * @return array 转换后的数组
     */
    public static function textToList($text)
    {
        $keyword = trim($text);
        $textArr = explode("\n", $text);
        $textArr = array_filter($textArr);
        $textArr = array_unique($textArr);

        return $textArr;
    }

    /**
     * 将输入的字符串转换为可查询条件的数组
     *
     * @param string $input 输入的字符串
     * @return array 转换后的数组
     */
    public static function changeStrToFilterArr($input)
    {
        $normalized  = str_replace('_', ',', $input);
        $resultArray = explode(',', $normalized);
        $resultArray = array_filter($resultArray);

        return array_values($resultArray);
    }
}