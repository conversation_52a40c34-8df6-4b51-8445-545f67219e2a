<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态（1在线，0下线，3等待发布、编辑、保存，9删除）
 * @property int $member_id 会员id
 * @property int $company_id 企业id
 * @property int $is_article 是否公告模式，1是，2否
 * @property string $name 职位名称
 * @property string $period_date 职位有效期
 * @property int $is_stick 是否长期
 * @property string $code 职位代码
 * @property int $job_category_id 职位类别id
 * @property int $education_type 学历要求类型
 * @property string $major_id 专业id
 * @property int $nature_type 性质类型
 * @property int $is_negotiable 是否自定义：1是，2否
 * @property int $wage_type 薪资类型(1月，2年，3日）
 * @property int $min_wage 薪资最低
 * @property int $max_wage 薪资最高
 * @property int $experience_type 经验要求类型
 * @property string $age_type 年龄要求类型
 * @property int $min_age 年龄要求最低
 * @property int $max_age 年龄要求最高
 * @property int $title_type 职称类型
 * @property int $political_type 政治面貌类型
 * @property int $abroad_type 海外经历类型
 * @property string $amount 招聘人数
 * @property string $department 用户部门
 * @property int $district_id 工作地点id
 * @property int $province_id 省Id
 * @property int $city_id 市Id
 * @property string $address 工作地点详细地址
 * @property string $welfare_tag 福利标签
 * @property string $duty 岗位职责
 * @property string $requirement 任职要求
 * @property string $remark 其他说明
 * @property string $refresh_time 发布时间
 * @property int $audit_status 审核状态（1审核通过，0已下线，-1审核拒绝，3编辑中，7等待审核）
 * @property int $is_show 是否显示
 * @property int $click 点击次数
 * @property string $lat 维度
 * @property string $lng 经度
 * @property string $release_time 改字段已停用
 * @property int $offline_type 下线方式 0:无；1:自动下线；2：手动下线，3:违规下线
 * @property string $offline_time 操作下线时间
 * @property int $announcement_id 公告id
 * @property int $gender_type 性别要求类型 0:不限；1:男；2:女
 * @property int $create_type 创建类型，1:自建；2:代建
 * @property int $create_id 创建人Id
 * @property string $creator 创建人名称
 * @property int $download_amount 简历下载次数
 * @property string $apply_audit_time 申请审核时间
 * @property string $delete_time 删除时间
 * @property string $apply_type 应聘方式(1电子邮件xxxx
 * @property string $apply_address 投递地址
 * @property string $first_release_time 首次发布时间
 * @property string $offline_reason 违规下线原因
 * @property int $is_consume_release 是否消费过发布，1:是，2:否
 * @property string $real_refresh_time 真实刷新时间
 * @property string $file_ids 应聘材料附件id对应file表id
 * @property string $delivery_limit_type 投递限制 1学历 2应聘材料
 * @property string $refresh_date 发布日期(格式Ymd)
 * @property int $delivery_type 投递类型1=站外投递,2=站内投递
 * @property int $delivery_way 投递方式 1平台投递 2邮箱投递 3网址投递
 * @property string $extra_notify_address 投递通知地址
 * @property string $establishment_type 职位编制类型 对应字典表类型31
 * @property int $is_establishment 是否有编制 1有编制 2无编制
 * @property int $is_miniapp 是否被小程序调用1调用 2没调用
 * @property int $is_manual_tag 是否运营手动标记 0没标记 1标记 2不标记
 * @property string $uuid uuid
 * @property int $company_sort 公司排序
 * @property int $apply_heat_type 该职位近90天的投递热度（V1.9需求，投递触发更新）
 * @property int $is_first_release 首发:1=首发,2=非首发
 * @property string $search_name
 */
class Job extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'period_date', 'refresh_time', 'release_time', 'offline_time', 'apply_audit_time', 'delete_time', 'first_release_time', 'real_refresh_time', 'refresh_date'], 'safe'],
            [['status', 'member_id', 'company_id', 'is_article', 'is_stick', 'job_category_id', 'education_type', 'nature_type', 'is_negotiable', 'wage_type', 'min_wage', 'max_wage', 'experience_type', 'min_age', 'max_age', 'title_type', 'political_type', 'abroad_type', 'district_id', 'province_id', 'city_id', 'audit_status', 'is_show', 'click', 'offline_type', 'announcement_id', 'gender_type', 'create_type', 'create_id', 'download_amount', 'is_consume_release', 'delivery_type', 'delivery_way', 'is_establishment', 'is_miniapp', 'is_manual_tag', 'company_sort', 'apply_heat_type', 'is_first_release'], 'integer'],
            [['lat', 'lng'], 'number'],
            [['name', 'code', 'department'], 'string', 'max' => 256],
            [['major_id'], 'string', 'max' => 4096],
            [['age_type', 'amount', 'address'], 'string', 'max' => 128],
            [['welfare_tag'], 'string', 'max' => 512],
            [['duty', 'requirement', 'remark'], 'string', 'max' => 2048],
            [['creator'], 'string', 'max' => 90],
            [['apply_type'], 'string', 'max' => 32],
            [['apply_address'], 'string', 'max' => 600],
            [['offline_reason', 'extra_notify_address'], 'string', 'max' => 255],
            [['file_ids'], 'string', 'max' => 100],
            [['delivery_limit_type', 'establishment_type'], 'string', 'max' => 60],
            [['uuid'], 'string', 'max' => 64],
            [['search_name'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'is_article' => 'Is Article',
            'name' => 'Name',
            'period_date' => 'Period Date',
            'is_stick' => 'Is Stick',
            'code' => 'Code',
            'job_category_id' => 'Job Category ID',
            'education_type' => 'Education Type',
            'major_id' => 'Major ID',
            'nature_type' => 'Nature Type',
            'is_negotiable' => 'Is Negotiable',
            'wage_type' => 'Wage Type',
            'min_wage' => 'Min Wage',
            'max_wage' => 'Max Wage',
            'experience_type' => 'Experience Type',
            'age_type' => 'Age Type',
            'min_age' => 'Min Age',
            'max_age' => 'Max Age',
            'title_type' => 'Title Type',
            'political_type' => 'Political Type',
            'abroad_type' => 'Abroad Type',
            'amount' => 'Amount',
            'department' => 'Department',
            'district_id' => 'District ID',
            'province_id' => 'Province ID',
            'city_id' => 'City ID',
            'address' => 'Address',
            'welfare_tag' => 'Welfare Tag',
            'duty' => 'Duty',
            'requirement' => 'Requirement',
            'remark' => 'Remark',
            'refresh_time' => 'Refresh Time',
            'audit_status' => 'Audit Status',
            'is_show' => 'Is Show',
            'click' => 'Click',
            'lat' => 'Lat',
            'lng' => 'Lng',
            'release_time' => 'Release Time',
            'offline_type' => 'Offline Type',
            'offline_time' => 'Offline Time',
            'announcement_id' => 'Announcement ID',
            'gender_type' => 'Gender Type',
            'create_type' => 'Create Type',
            'create_id' => 'Create ID',
            'creator' => 'Creator',
            'download_amount' => 'Download Amount',
            'apply_audit_time' => 'Apply Audit Time',
            'delete_time' => 'Delete Time',
            'apply_type' => 'Apply Type',
            'apply_address' => 'Apply Address',
            'first_release_time' => 'First Release Time',
            'offline_reason' => 'Offline Reason',
            'is_consume_release' => 'Is Consume Release',
            'real_refresh_time' => 'Real Refresh Time',
            'file_ids' => 'File Ids',
            'delivery_limit_type' => 'Delivery Limit Type',
            'refresh_date' => 'Refresh Date',
            'delivery_type' => 'Delivery Type',
            'delivery_way' => 'Delivery Way',
            'extra_notify_address' => 'Extra Notify Address',
            'establishment_type' => 'Establishment Type',
            'is_establishment' => 'Is Establishment',
            'is_miniapp' => 'Is Miniapp',
            'is_manual_tag' => 'Is Manual Tag',
            'uuid' => 'Uuid',
            'company_sort' => 'Company Sort',
            'apply_heat_type' => 'Apply Heat Type',
            'is_first_release' => 'Is First Release',
            'search_name' => 'Search Name',
        ];
    }
}
