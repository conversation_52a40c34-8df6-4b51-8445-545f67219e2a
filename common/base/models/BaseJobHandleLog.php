<?php

namespace common\base\models;

use common\helpers\FormatConverter;
use common\libs\Cache;
use common\models\JobHandleLog;
use frontendPc\models\Dictionary;
use frontendPc\models\Major;
use Mpdf\Tag\B;
use yii\base\Exception;
use yii\db\ActiveQuery;
use function GuzzleHttp\Psr7\str;

class BaseJobHandleLog extends JobHandleLog
{
    const HANDLE_TYPE_EDIT                   = 1;
    const HANDLE_TYPE_REFRESH                = 2;
    const HANDLE_TYPE_RELEASE                = 3;
    const HANDLE_TYPE_RELEASE_AGAIN          = 4;
    const HANDLE_TYPE_OFFLINE                = 5;
    const HANDLE_TYPE_AUDIT                  = 6;
    const HANDLE_TYPE_HIDDEN                 = 7;  //隐藏
    const HANDLE_TYPE_SHOW                   = 8;  //显示
    const HANDLE_TYPE_DELETE                 = 9;  //删除
    const HANDLE_TYPE_COPY                   = 10; //复制
    const HANDLE_TYPE_LIMIT_EDUCATION        = 11; //增加学历限制
    const HANDLE_TYPE_CANCEL_LIMIT_EDUCATION = 12; //取消学历限制
    const HANDLE_TYPE_LIMIT_FILE             = 13; //增加应聘材料限制
    const HANDLE_TYPE_CANCEL_LIMIT_FILE      = 14; //取消应聘材料限制

    const HANDLER_TYPE_PLAT = 1;
    const HANDLER_TYPE_USER = 2;

    //文本替换字典
    const LOG_TEXT_LIST_DUTY        = 1;
    const LOG_TEXT_LIST_REQUIREMENT = 2;
    const LOG_TEXT_LIST_REMARK      = 3;
    const LOG_TEXT_LIST_FILE_IDS    = 4;
    const LOG_TEXT_LIST             = [
        self::LOG_TEXT_LIST_DUTY        => '岗位职责',
        self::LOG_TEXT_LIST_REQUIREMENT => '任职要求',
        self::LOG_TEXT_LIST_REMARK      => '其他说明',
        self::LOG_TEXT_LIST_FILE_IDS    => '职位附件',
    ];

    //是否显示记录内容
    const LOG_TYPE_SHOW = 1;//显示
    const LOG_TYPE_HIDE = 2;//不显示

    /**
     * 职位编辑修改信息
     * @param $data
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function editInformation($data)
    {
        $list     = FormatConverter::convertHump($data);
        $infoList = [];
        if ($list['education_type']) {
            $infoList[$list['educationTypeTitle']] = $list['education_type'];
        }
        if ($list['major_id']) {
            $infoList['需要专业'] = $list['majorTitle'];
        }
        if ($list['nature_type']) {
            $infoList['工作性质'] = $list['majorTitle'];
        }
        if ($list['major_id']) {
            $infoList['需要专业'] = $list['majorTitle'];
        }
        if ($list['wage_type']) {
            $infoList['薪资类型'] = $list['wageTypeTitle'];
        }
        if ($list['is_negotiable']) {
            if ($list['is_negotiable'] == 1) {
                $infoList['是否面议'] = "是";
            } else {
                $infoList['是否面议'] = "否";
            }
        }
        if ($list['amount']) {
            $infoList['招聘人数'] = $list['amount'];
        }
        if ($list['province_id'] || $list['city_id'] || $list['district_id']) {
            $infoList['省市区'] = $list['provinceTitle'] . $list['cityTitle'] . $list['districtTitle'];
        }
        if ($list['major_id']) {
            $infoList[$list['majorTitle']] = $list['majorId'];
        }
        if ($list['address']) {
            $infoList['地址'] = $list['address'];
        }
        if ($list['period_date']) {
            $infoList['职位有效期'] = $list['period_date'];
        }
        if ($list['majorId']) {
            $infoList[$list['majorTitle']] = $list['majorId'];
        }
        if ($list['majorId']) {
            $infoList[$list['majorTitle']] = $list['majorId'];
        }
        if ($list['duty']) {
            $infoList['岗位职责'] = $list['duty'];
        }
        if ($list['requirement']) {
            $infoList['任职要求'] = $list['requirement'];
        }
        if ($list['min_wage']) {
            $infoList['最低薪资'] = $list['minWage'];
        }
        if ($list['max_wage']) {
            $infoList['最高薪资'] = $list['maxWage'];
        }
        if ($list['experience_type']) {
            $infoList['经验要求类型'] = $list['educationTypeTitle'];
        }
        if ($list['age_type']) {
            $infoList['年龄要求类型'] = $list['ageTypeTitle'];
        }
        if ($list['title_type']) {
            $infoList['职称类型'] = $list['titleTypeTitle'];
        }
        if ($list['political_type']) {
            $infoList['政治面貌类型'] = $list['politicalTypeTitle'];
        }
        if ($list['abroad_type']) {
            $infoList['海外经历'] = $list['abroad_type'] == 1 ? "要求" : "不要求";
        }
        if ($list['department']) {
            $infoList['用人部分'] = $list['department'];
        }
        if ($list['welfare_tag']) {
            $infoList['福利标签'] = $list['welfare_tag'];
        }
        if ($list['remark']) {
            $infoList['其他说明'] = $list['remark'];
        }

        return $infoList;
    }

    /**
     * 创建职位操作日志
     * @throws Exception
     */
    public static function createInfo($data)
    {
        $baseJobHandleLog                  = new self();
        $baseJobHandleLog->add_time        = $data['add_time'];
        $baseJobHandleLog->announcement_id = $data['announcement_id'];
        $baseJobHandleLog->job_id          = $data['job_id'];
        $baseJobHandleLog->handle_type     = $data['handle_type'];
        $baseJobHandleLog->handler_type    = $data['handler_type'];
        $baseJobHandleLog->handler_id      = $data['handler_id'];
        $baseJobHandleLog->handler_name    = $data['handler_name'];
        $baseJobHandleLog->handle_before   = $data['handle_before'];
        $baseJobHandleLog->handle_after    = $data['handle_after'];
        $baseJobHandleLog->ip              = $data['ip'];
        if (!$baseJobHandleLog->save()) {
            throw new Exception($baseJobHandleLog->getFirstErrorsMessage());
        }
    }

}