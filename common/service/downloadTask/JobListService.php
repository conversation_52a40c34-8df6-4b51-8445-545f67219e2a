<?php

namespace common\service\downloadTask;

use admin\models\Job;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\helpers\UUIDHelper;
use Yii;
use yii\base\Exception;

class JobListService
{
    /**
     * 导出合作单位的职位列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public function run($keywords)
    {
        //获取query
        $query   = Job::jobListBuilderQuery($keywords);
        $list    = $query->select([
            'j.id',
            'j.name',
            'j.refresh_time',
            'j.first_release_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.province_id',
            'j.experience_type',
            'j.offline_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.click',
            'j.amount',
            'j.political_type',
            'j.abroad_type',
            'j.download_amount',
            'j.gender_type',
            'j.creator',
            'j.delete_time',
            'j.is_miniapp',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.real_refresh_time',
            'j.delivery_limit_type',
            'c.full_name as company',
            'art.status as announcementStatus',
            'an.title',
            'an.audit_status as announcementAuditStatus',
            'art.status as articleStatus',
            'j.click as allClick',
            'jare.total as jobApplyNum',
            'jare.interview as jobInterviewNum',
        ])
            ->asArray()
            ->all();
        $headers = [
            '职位ID',
            '职位名称',
            '基本信息',
            '关联公告',
            '所属单位',
            '招聘状态',
            '投递次数',
            '面试邀约',
            '发布时间',
            '刷新时间',
            '招聘人数',
            '点击次数',
            '下载简历',
            '用人部门',
            '审核状态',
            '发布模式',
            '创建人',
            '创建时间',
            '下线时间',
            '删除时间',
            '学科专业',
            '是否小程序',
            '职位联系人',
            '职位协同子账号',
        ];
        $data    = [];
        foreach ($list as &$job) {
            $experienceTypeTitle = BaseDictionary::getExperienceName($job['experience_type']);
            $educationTypeTitle  = BaseDictionary::getEducationName($job['education_type']);
            $city                = BaseArea::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            //处理职位联系人与协同账号
            $job_contact          = Job::getJobContact($job['id']);
            $job_contact_synergy  = Job::getJobContactSynergy($job['id']);
            $job_contact_item_arr = [];
            if ($job_contact) {
                if ($job_contact['email']) {
                    $job_contact_item_arr[] = $job_contact['email'];
                }
                if ($job_contact['mobile']) {
                    $job_contact_item_arr[] = $job_contact['mobile'];
                }
            }

            $item = [
                UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']),
                $job['name'],
                $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage,
                $job['title'],
                $job['company'],
                BaseJob::JOB_STATUS_NAME[$job['status']],
                $job['jobApplyNum'],
                $job['jobInterviewNum'],
                ($job['release_time'] == '0000-00-00 00:00:00' || empty($job['release_time'])) ? '-' : date('Y/m/d',
                    strtotime($job['release_time'])),
                ($job['refresh_time'] == '0000-00-00 00:00:00' || empty($job['refresh_time'])) ? '-' : date('Y/m/d',
                    strtotime($job['refresh_time'])),
                $job['amount'],
                $job['click'],
                $job['downloadAmount'],
                $job['department'],
                BaseJob::JOB_AUDIT_STATUS_NAME[$job['audit_status']],
                $job['is_article'] == BaseJOb::IS_ARTICLE_YES ? '公告+职位' : '纯职位',
                $job['creator'] ?: '',
                ($job['add_time'] == '0000-00-00 00:00:00' || empty($job['add_time'])) ? '-' : date('Y/m/d',
                    strtotime($job['add_time'])),
                ($job['period_date'] == '0000-00-00 00:00:00' || empty($job['period_date'])) ? '-' : date('Y/m/d',
                    strtotime($job['period_date'])),
                ($job['delete_time'] == '0000-00-00 00:00:00' || empty($job['delete_time'])) ? '-' : date('Y/m/d',
                    strtotime($job['delete_time'])),
                BaseMajor::getAllMajorName(explode(',', $job['major_id'])),
                BaseJob::IS_MINIAPP_LIST[$job['is_miniapp']],
                count($job_contact_item_arr) > 0 ? implode(',', $job_contact_item_arr) : '',
                count($job_contact_synergy) > 0 ? implode(',', array_column($job_contact_synergy, 'email')) : '',
            ];
            array_push($data, $item);
        }
        $fileName = BaseAdminDownloadTask::TYPE_JOB_LIST . '_' . date('YmdHis');

        return [
            'data'     => $data,
            'headers'  => $headers,
            'fileName' => $fileName,
        ];
    }

    /**
     * 导出合作单位的职位列表（部分）
     * @param $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function runSlice($keywords): array
    {
        $query    = Job::sliceJobListBuilderQuery($keywords);
        $list     = $query->asArray()
            ->all();
        $headers  = [
            '单位所属城市',
            '单位所属省份',
            '所属单位',
            '用人部门',
            '职位名称',
            '学科专业',
            '招聘状态',
            '发布时间',
            '职位详情',
            '关联公告',
            '单位类型',
            '单位性质',
            '学历要求',
            '报名方式',
            '招聘人数',
            '岗位职责',
            '任职要求',
            '其它说明',
            '初始发布时间',
        ];
        $data     = [];
        $visitUrl = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        foreach ($list as &$job) {
            $city             = BaseArea::getAreaName($job['city_id']);
            $province         = BaseArea::getAreaName($job['province_id']);
            $major            = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
            $status           = BaseJob::JOB_STATUS_NAME[$job['status']];
            $jobLink          = $visitUrl . '/job/detail/' . $job['id'] . '.html';
            $announcementLink = $visitUrl . '/announcement/detail/' . $job['announcement_id'] . '.html';
            $applyText        = '';
            if ($job['delivery_type'] > 0) {
                if ($job['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTER) {
                    $applyText = BaseJob::getApplyTypeName($job['apply_type']);
                } else {
                    $applyText = '站内投递';
                }
            } else {
                if ($job['announcement_id'] > 0) {
                    $announcementInfo = BaseAnnouncement::findOne($job['announcement_id']);
                    if ($announcementInfo->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                        $applyText = BaseJob::getApplyTypeName($announcementInfo->apply_type);
                    } else {
                        $applyText = '站内投递';
                    }
                }
            }
            $item = [
                $city,
                $province,
                $job['company'],
                $job['department'],
                $job['name'],
                $major,
                $status,
                ($job['refresh_date'] == '0000-00-00' || empty($job['refresh_date'])) ? '-' : date('Y/m/d',
                    strtotime($job['refresh_date'])),
                $jobLink,
                $announcementLink,
                BaseDictionary::getCompanyTypeName($job['company_type']),
                BaseDictionary::getCompanyNatureName($job['company_nature']),
                BaseDictionary::getEducationName($job['education_type']),
                $applyText,
                $job['amount'],
                $job['duty'],
                $job['requirement'],
                $job['remark'],
                ($job['first_release_time'] == '0000-00-00 00:00:00' || empty($job['first_release_time'])) ? '-' : date('Y/m/d',
                    strtotime($job['first_release_time'])),
            ];
            array_push($data, $item);
        }
        $fileName = BaseAdminDownloadTask::TYPE_SLICE_JOB_LIST . '_' . date('YmdHis');

        return [
            'data'     => $data,
            'headers'  => $headers,
            'fileName' => $fileName,
        ];
    }
}
