<?php

namespace timer\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMajor;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\libs\ColumnAuto\JobAutoClassify;
use common\service\boShiHouColumn\JobService;
use common\service\column\RecommendJobService;
use common\service\job\BaseService;
use common\service\job\OnlineOfflineService;
use common\service\search\CommonSearchApplication;
use common\service\search\PcJobListService;
use frontendPc\models\Job;
use Yii;
use yii\base\Exception;

/**
 *
 */
class JobController extends BaseTimerController
{
    /**
     * 首页热门职位数据
     * 一、”热门职位“：
     *
     * ①展示近15天（不包含当前日）发布的、关注度排行前16的在线职位信息；关注度=职位投递量*40%+职位页面详情浏览量*60%；按关注度倒序展示；
     *
     * ② 按岗位类型展示；
     *
     * ③支持设置广告位
     *
     * ④点击”更多“，新页面打开”职位列表“对应岗位类型的检索页面。
     */
    public function actionHomeSelectJob()
    {
        // 首先找到最近15天发布的职位
        //        $data     = BaseHomePosition::getPcSelectJob();

        $service = new RecommendJobService();
        $data    = $service->getPcSelectJob();

        $cacheKey = Cache::PC_HOME_POSITION_SELECT_JOB_KEY;
        Cache::set($cacheKey, json_encode($data));
    }

    /**
     * 政府事业单位栏目页的热门职位数据
     * @return void
     */
    public function actionGovernmentColumnSelectJob()
    {
        // 首先找到最近15天发布的职位
        $service  = new RecommendJobService();
        $data     = $service->getGovernmentJobList();
        $cacheKey = Cache::PC_COLUMN_GOVERNMENT_SELECT_JOB_KEY;
        Cache::set($cacheKey, json_encode($data));
    }

    /**
     * 省区栏目页的热门职位数据
     * @return void
     */
    public function actionAreaColumnSelectJob()
    {
        // 首先找到最近15天发布的职位
        $service        = new RecommendJobService();
        $areaColumnList = $service::AREA_COLUMN_LIST;
        foreach ($areaColumnList as $item) {
            self::log('开始处理地区栏目：' . $item['name'] . ',栏目id:' . $item['id']);
            $service->setColumnId($item['id']);
            $data     = $service->getAreaJobList();
            $cacheKey = Cache::PC_COLUMN_AREA_SELECT_JOB_KEY . '_' . $item['id'];
            Cache::set($cacheKey, json_encode($data));
            self::log('地区栏目：' . $item['name'] . '处理完毕');
        }
    }

    /**
     * 一级栏目页的热门职位数据
     * @return void
     */
    public function actionFirstColumnSelectJob()
    {
        $service         = new RecommendJobService();
        $firstColumnList = $service::COLUMN_CATEGORY_JOB_LIST;
        foreach ($firstColumnList as $item) {
            self::log('开始处理一级栏目：' . $item['name'] . ',栏目id:' . $item['id']);
            $service->setColumnId($item['id']);
            $data     = $service->getOtherColumnJobList();
            $cacheKey = Cache::PC_FIRST_COLUMN_SELECT_JOB_KEY . '_' . $item['id'];
            Cache::set($cacheKey, json_encode($data));
            self::log('一级栏目：' . $item['name'] . '处理完毕');
        }
    }

    public function online()
    {
    }

    /**
     * 职位自动下线
     *
     * 这里有一个逻辑,就是下线的时间是日期,也就是譬如说,我设定了2022-01-01,那么就是2022-01-01 23:59:59
     */
    public function actionOffline()
    {
        // 找到所有应该下线的职位(现在是在线并且下线时间小于当前时间,必须是0点去执行)

        $transaction = Yii::$app->db->beginTransaction();
        // 昨天
        $yesterday = TimeHelper::getYesterday();
        try {
            $list = BaseJob::find()
                ->select([
                    'id',
                    'period_date',
                ])
                ->where([
                    'status' => BaseJob::STATUS_ONLINE,
                ])
                ->andWhere([
                    '<',
                    'period_date',
                    TimeHelper::dayToEndTime($yesterday),
                ])
                ->andWhere([
                    '!=',
                    'period_date',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            $service = new OnlineOfflineService();
            $adminId = 0;

            self::log(var_export($list, true));
            foreach ($list as $item) {
                $jobData = [
                    'id'            => $item['id'],
                    'status'        => BaseJob::STATUS_OFFLINE,
                    'auditStatus'   => BaseJob::AUDIT_STATUS_OFFLINE,
                    'offlineType'   => BaseJob::OFFLINE_TYPE_AUTO,
                    'periodDate'    => CUR_DATETIME,
                    'offlineReason' => '自动下线',
                ];

                $handleBefore = [
                    '职位状态' => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_ONLINE],
                ];
                $handleAfter  = [
                    '职位状态' => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_OFFLINE],
                    '下线方式' => '自动下线',
                    '下线时间' => CUR_DATETIME,
                ];

                $jobHandleLog = [
                    'job_id'        => $item['id'],
                    'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_OFFLINE,
                    'handler_type'  => 0,
                    'handler_id'    => 0,
                    'handler_name'  => '系统',
                    'handle_before' => json_encode($handleBefore),
                    'handle_after'  => json_encode($handleAfter),
                    'ip'            => 0,
                ];

                $service->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setOffline()
                    ->setData($jobData, $jobHandleLog)
                    ->run();
                $auto = new JobAutoClassify($item['id']);
                $auto->updateStatInfo();
            }

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            self::log($e->getMessage());
        }
    }

    /**
     * 职位真实刷新时间数据修改填充
     * @return false|void
     */
    public function actionRunJobRefreshTimeToRealRefreshTime()
    {
        $list = BaseJobHandleLog::find()
            ->where([
                'handle_type' => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
            ])
            ->asArray()
            ->all();
        if (!$list) {
            return false;
        }

        try {
            $transaction = Yii::$app->db->beginTransaction();

            foreach ($list as $item) {
                $model = BaseJob::findOne(['id' => $item['job_id']]);
                self::log(var_export($model, true));

                if ($model->refresh_time != TimeHelper::ZERO_TIME && $model->real_refresh_time == TimeHelper::ZERO_TIME) {
                    $model->real_refresh_time = $model->refresh_time;
                    $model->save();
                }
            }

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            self::log($e->getMessage());
        }
    }

    /**
     * 职位旧数据刷新时间为空处理
     * @return false|void
     */
    public function actionRunJobRefreshTimeDataHandle()
    {
        // 获取有审核通过历史并且没有刷新时间的数据
        $list = BaseJob::find()
            ->select('id')
            ->where([
                'refresh_time' => TimeHelper::ZERO_TIME,
            ])
            ->andWhere([
                '!=',
                'first_release_time',
                TimeHelper::ZERO_TIME,
            ])
            ->asArray()
            ->limit(100000)
            ->all();
        if (!$list) {
            return false;
        }

        self::log('开始处理,共' . count($list) . '条数据');
        $successCount = 0;
        $failCount    = 0;
        foreach ($list as $k => $item) {
            try {
                $transaction = Yii::$app->db->beginTransaction();
                $model       = BaseJob::findOne($item['id']);
                self::log('开始处理第' . ($k + 1) . '条数据,id=' . $item['id']);
                // 操作刷新记录
                $jobRefreshTime = BaseJobHandleLog::find()
                    ->select('add_time')
                    ->where([
                        'job_id'      => $item['id'],
                        'handle_type' => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
                    ])
                    ->orderBy('add_time desc')
                    ->one();
                // 如果操作过刷新，获取最新的创建时间为刷新时间，否则发布时间为刷新时间
                if (!empty($jobRefreshTime)) {
                    if (strtotime($jobRefreshTime['add_time']) > strtotime($model->release_time)) {
                        $model->refresh_time = $jobRefreshTime['add_time'];
                    } else {
                        $model->refresh_time = $model->release_time;
                    }
                } else {
                    $model->refresh_time = $model->release_time;
                }
                $model->refresh_date = date('Y-m-d', strtotime($model->refresh_time));
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                $transaction->commit();
                self::log('成功更新刷新时间=' . $model->refresh_time);
                $successCount++;
            } catch (\Exception $e) {
                $transaction->rollBack();

                self::log('失败更新' . $e->getMessage());
                $failCount++;
            }
        }

        self::log('处理完成,成功' . $successCount . '条,失败' . $failCount . '条');
    }

    /**
     * 职位列表不带条件的分页缓存
     * @return false|void
     * php timer_yii job/job-list-cache
     */
    public function actionJobListCache()
    {
        try {
            $app = CommonSearchApplication::getInstance();
            $app->setNoParamsListCache();
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    /**
     * 小程序首页缓存
     * @return false|void
     * php timer_yii job/set-mini-app-recommend-job-list-cache
     */
    public function actionSetMiniAppRecommendJobListCache()
    {
        try {
            BaseJob::setMiniAppRecommendJobList();
        } catch (\Exception $e) {
            self::log($e->getMessage());
        }
    }

    /**
     * 更新在线职位的下线方式
     * @return false|void
     * php timer_yii job/set-job-offline-type
     */
    public function actionSetJobOfflineType()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $jobList = BaseJob::find()
                ->select([
                    'id',
                ])
                ->where([
                    'status' => BaseJob::STATUS_ONLINE,
                ])
                ->andWhere([
                    '!=',
                    'offline_type',
                    0,
                ])
                ->asArray()
                ->all();
            $count   = count($jobList);
            self::log("数据共{$count}条\n");

            foreach ($jobList as $item) {
                $model = BaseJob::findOne($item['id']);
                self::log("开始处理职位id:{$model['id']}\n");

                $model->offline_type = 0;
                $model->offline_time = '0000-00-00 00:00:00';
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新职位Id:{$model['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 更新职位是否首发
     * 规则：
     *      1、发布日为近15日
     *      2、对外发布时间=初始发布时间
     *
     * php timer_yii job/set-job-is-first
     * 每一天凌晨过1分执行一次
     */
    public function actionSetJobIsFirst()
    {
        $day = 15;
        //计算15天前的日期
        $fifteenDaysAgo = date('Y-m-d', strtotime('-' . $day . ' day'));

        //获取15天前发布的首发公告
        $jobList = BaseJob::find()
            ->select([
                'id',
                'refresh_time',
                'first_release_time',
            ])
            ->where([
                '<',
                'first_release_time',
                TimeHelper::dayToBeginTime($fifteenDaysAgo),
            ])
            ->where(['is_first_release' => BaseJob::IS_FIRST_RELEASE_YES])
            ->asArray()
            ->all();

        $count = count($jobList);
        self::log("数据共{$count}条\n");

        foreach ($jobList as $item) {
            $model = BaseJob::findOne($item['id']);
            self::log("开始处理职位id:{$model['id']}\n");

            $model->is_first_release = BaseJob::IS_FIRST_RELEASE_NO;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
            self::log("更新职位Id:{$model['id']}成功\n");
        }
    }

    /**
     * 博士后职位的榜单
     * php timer_yii job/update-job-ranking
     */
    public function actionUpdateJobRanking()
    {
        JobService::getRankingListCache();
        self::log('博士后职位的榜单更新成功');
    }

    /**
     * 选定预热关键字
     * php timer_yii job/preprocess-job-keywords
     */
    public function actionPreprocessJobKeywords()
    {
        \common\service\job\JobService::preprocessJobKeywords();
    }

    /**
     * 根据关键字-预热查询职位
     * php timer_yii job/preprocess-job-ids-by-keywords
     */
    public function actionPreprocessJobIdsByKeywords($num = '')
    {
        try {
            \common\service\job\JobService::preprocessJobIdsByKeywords(intval($num));
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }
}
