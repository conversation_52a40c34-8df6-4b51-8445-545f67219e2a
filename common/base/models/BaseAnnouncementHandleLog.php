<?php

namespace common\base\models;

use common\helpers\IpHelper;
use common\models\AnnouncementHandleLog;
use yii\base\Exception;

class BaseAnnouncementHandleLog extends AnnouncementHandleLog
{
    // 公告操作类型
    const HANDLE_TYPE_EDIT          = 1;  //编辑
    const HANDLE_TYPE_REFRESH       = 2;  //刷新
    const HANDLE_TYPE_RELEASE       = 3;  //发布
    const HANDLE_TYPE_RELEASE_AGAIN = 4;  ///再发布
    const HANDLE_TYPE_OFFLINE       = 5;  //下线
    const HANDLE_TYPE_AUDIT         = 6;  //审核
    const HANDLE_TYPE_HIDDEN        = 7;  //隐藏
    const HANDLE_TYPE_SHOW          = 8;  //显示
    const HANDLE_TYPE_DELETE        = 9;  //删除
    const HANDLE_TYPE_COPY          = 10; //复制

    const HANDLER_TYPE_PLAT = 1;
    const HANDLER_TYPE_USER = 2;

    // 公告操作类型
    const HANDLE_TYPE_NAME = [
        self::HANDLE_TYPE_EDIT          => '编辑',
        self::HANDLE_TYPE_REFRESH       => '刷新',
        self::HANDLE_TYPE_RELEASE       => '发布',
        self::HANDLE_TYPE_RELEASE_AGAIN => '再发布',
        self::HANDLE_TYPE_OFFLINE       => '下线',
        self::HANDLE_TYPE_AUDIT         => '审核',
        self::HANDLE_TYPE_HIDDEN        => '隐藏',
        self::HANDLE_TYPE_SHOW          => '显示',
        self::HANDLE_TYPE_DELETE        => '删除',
        self::HANDLE_TYPE_COPY          => '复制',
    ];

    /**
     * 创建公告操作日志
     * @throws Exception
     */
    public static function createInfo($data)
    {
        $handleLog                  = new self();
        $handleLog->add_time        = $data['add_time'] ?: date('Y-m-d H:i:s');
        $handleLog->announcement_id = $data['announcement_id'];
        $handleLog->handle_type     = $data['handle_type'];
        $handleLog->editor_type     = $data['editor_type'];
        $handleLog->handler_type    = $data['handler_type'];
        $handleLog->handler_id      = $data['handler_id'];
        $handleLog->handler_name    = $data['handler_name'];
        $handleLog->handle_before   = $data['handle_before'];
        $handleLog->handle_after    = $data['handle_after'];
        $handleLog->ip              = $data['ip'] ?: IpHelper::getIpInt();
        if (!$handleLog->save()) {
            throw new Exception($handleLog->getFirstErrorsMessage());
        }
    }
}