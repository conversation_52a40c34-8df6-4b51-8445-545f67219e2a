<?php

namespace common\base\models;

use admin\models\Announcement;
use admin\models\Article;
use admin\models\Company;
use admin\models\JobApply;
use common\base\BaseActiveRecord;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\FormatConverter;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\ColumnAuto\JobAutoClassify;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\libs\EmailQueue;
use common\libs\WxMiniApp;
use common\models\Job;
use common\models\OffSiteJobApply;
use common\service\chat\CommonRule;
use common\service\match\MatchCompleteService;
use common\service\messageCenter\MessageCenterApplication;
use common\service\search\CommonSearchApplication;
use queue\Producer;
use yii;
use yii\base\Exception;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\Url;

class BaseJob extends Job
{
    const DETAIL_CACHE_TIME = 18000;
    // const DETAIL_CACHE_TIME = 300;

    const IS_FIRST_RELEASE_YES = 1;
    const IS_FIRST_RELEASE_NO  = 2;

    const TYPE_WAGE_MONTH      = 1;          //月薪
    const TYPE_WAGE_YEARS      = 2;          //年薪
    const TYPE_WAGE_DAY        = 3;          //日薪
    const TYPE_WAGE_NEGOTIABLE = 0;          //面议

    const EVEN_NEGOTIABLE    = 1;       //面议
    const EVEN_NONNEGOTIABLE = 0;       //非面议

    const STATUS_PASS_AUDIT   = 1;      //审核通过
    const STATUS_WAIT_AUDIT   = 7;      //等待审核
    const STATUS_REFUSE_AUDIT = -1;   //审核拒

    const STATUS_ONLINE  = 1;      //上线
    const STATUS_WAIT    = 3;      //等待发布/编辑中/保存
    const STATUS_OFFLINE = 0;      //已下线
    const STATUS_DELETE  = 9;      //已删除

    const AUDIT_STATUS_REFUSE_AUDIT = -1;   //审核拒绝
    const AUDIT_STATUS_OFFLINE      = 0;      //已下线
    const AUDIT_STATUS_WAIT         = 3;      //编辑中
    const AUDIT_STATUS_WAIT_AUDIT   = 7;      //等待审核
    const AUDIT_STATUS_PASS_AUDIT   = 1;      //审核通过
    const AUDIT_STATUS_PASS_DELETE  = 9;      //已删除

    const WAGE_TYPE_MONEY = 1;     //薪资类型（月）
    const WAGE_TYPE_YEAR  = 2;     //薪资类型（年）
    const WAGE_TYPE_DAY   = 3;     //薪资类型（日）

    const OFFLINE_TYPE_AUTO      = 1;     //下线类型（自动）
    const OFFLINE_TYPE_HAND      = 2;     //下线类型（手动）
    const OFFLINE_TYPE_VIOLATION = 3;     //下线类型（违规）

    const REFRESH_LIMIT_DAY = 15;     //某一文件刷新天数限制，15天允许刷新一次
    const ACCOUNT_LIMIT_NUM = 10;     //某一账号一天最多可刷新职位个数，10个

    const ATTENTION_PERIOD_DAY  = 7;    //职位到期时间提醒，7天内
    const RELEASE_AGAIN_DAY     = 180;  //未付费单位再发布天数限制，180天
    const ADD_RELEASE_AGAIN_DAY = 365;  //职位再发布新增天数，365天，基于当前时间

    const CREATE_TYPE_SELF  = 1;     //创建类型，自建
    const CREATE_TYPE_AGENT = 2;     //创建类型，代理

    //公告模式
    const IS_ARTICLE_YES  = 1;
    const IS_ARTICLE_NO   = 2;
    const IS_ARTICLE_WAIT = 3;

    //职位是否申请了
    const JOB_APPLY_STATUS_YES = 1;
    const JOB_APPLY_STATUS_NO  = 2;

    //获取列表是否需要分页信息
    const NEED_PAGE_INFO_YES = true;
    const NEED_PAGE_INFO_NO  = false;

    //投递限制类型
    /** 学历限制 */
    const DELIVERY_LIMIT_TYPE_EDUCATION = 1;
    /** 应聘材料限制 */
    const DELIVERY_LIMIT_TYPE_FILE = 2;

    //自定义职位推荐MailRecommend
    const MAIL_RECOMMEND_NUM = 8;

    const DELIVERY_LIMIT_TYPE = [
        self::DELIVERY_LIMIT_TYPE_EDUCATION,
        self::DELIVERY_LIMIT_TYPE_FILE,
    ];

    //投递方式
    const DELIVERY_WAY_PLATFORM        = 1;
    const DELIVERY_WAY_EMAIL           = 2;
    const DELIVERY_WAY_LINK            = 3;
    const DELIVERY_WAY_EMAIL_LINK      = 66;
    const DELIVERY_WAY_PLATFORM_NAME   = '平台投递';
    const DELIVERY_WAY_EMAIL_NAME      = '邮件投递';
    const DELIVERY_WAY_LINK_NAME       = '网址投递';
    const DELIVERY_WAY_EMAIL_LINK_NAME = '邮件&网址投递';
    const DELIVERY_WAY                 = [
        self::DELIVERY_WAY_PLATFORM,
        self::DELIVERY_WAY_EMAIL,
        self::DELIVERY_WAY_LINK,
    ];

    const DELIVERY_WAY_LIST            = [
        self::DELIVERY_WAY_PLATFORM => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL    => self::DELIVERY_WAY_EMAIL_NAME,
        self::DELIVERY_WAY_LINK     => self::DELIVERY_WAY_LINK_NAME,
    ];
    const DELIVERY_WAY_EMAIL_LINK_LIST = [
        self::DELIVERY_WAY_EMAIL,
        self::DELIVERY_WAY_LINK,
    ];
    const DELIVERY_WAY_NAME            = [
        self::DELIVERY_WAY_PLATFORM => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL    => self::DELIVERY_WAY_EMAIL_NAME,
        self::DELIVERY_WAY_LINK     => self::DELIVERY_WAY_LINK_NAME,
    ];
    const DELIVERY_WAY_SELECT_NAME     = [
        self::DELIVERY_WAY_PLATFORM   => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL_LINK => self::DELIVERY_WAY_EMAIL_LINK_NAME,
    ];

    //职位投递状态
    const JOB_COLLECT_STATUS_YES = 1;
    const JOB_COLLECT_STATUS_NO  = 2;

    const RECOMMEND_JOB_NUM = 2;
    //个人中心列表限制条数
    const PERSONAL_SHOW_LIST = 6;

    // 薪酬自定义(是)
    const IS_NEGOTIABLE_YES = 1;
    // 薪酬自定义(否)
    const IS_NEGOTIABLE_NO = 2;

    // 是否显示(是)
    const IS_SHOW_YES = 1;
    // 是否显示(否)
    const IS_SHOW_NO   = 2;
    const IS_SHOW_NAME = [
        self::IS_SHOW_YES => '显示',
        self::IS_SHOW_NO  => '隐藏',
    ];

    const JOB_STATUS_NAME = [
        self::STATUS_OFFLINE      => '已下线',
        self::STATUS_ONLINE       => '在线',
        self::STATUS_WAIT         => '编辑中',
        self::STATUS_WAIT_AUDIT   => '待审核',
        self::STATUS_DELETE       => '已删除',
        self::STATUS_REFUSE_AUDIT => '审核拒绝',
    ];

    const JOB_SEARCH_STATUS_NAME = [
        self::STATUS_OFFLINE => '已下线',
        self::STATUS_ONLINE  => '在线',
    ];

    const JOB_AUDIT_STATUS_NAME = [
        0  => '已下线',
        1  => '审核通过',
        3  => '编辑中',
        7  => '待审核',
        9  => '已删除',
        -1 => '审核拒绝',
    ];

    const JOB_HISTORY_STATUS = [
        self::STATUS_OFFLINE,
        self::STATUS_ONLINE,
        self::STATUS_DELETE,
        self::STATUS_WAIT,
    ];

    const JOB_WAGE_TYPE_NAME = [
        1 => '月',
        2 => '年',
        3 => '日',
    ];

    const JOB_OFFLINE_TYPE_NAME = [
        0 => '-',
        1 => '自动下线',
        2 => '手动下线',
        3 => '违规下线',
    ];

    const AUDIT_STATUS_NAME = [
        0  => '已下线',
        1  => '审核通过',
        7  => '等待审核',
        9  => '已删除',
        -1 => '审核拒绝',
    ];

    const AUDIT_STATUS_CHECK_NAME = [
        1  => '审核通过',
        7  => '等待审核',
        -1 => '审核拒绝',
    ];

    const STATUS_NAME = [
        self::STATUS_OFFLINE => '已下线',
        self::STATUS_ONLINE  => '在线',
        self::STATUS_DELETE  => '已删除',
    ];

    const STATUS_HISTORY = [
        self::STATUS_OFFLINE,
        self::STATUS_ONLINE,
        self::STATUS_DELETE,
    ];

    const ATTRIBUTE_APPLY_EMAIL     = BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL;
    const ATTRIBUTE_APPLY_ONLINE    = BaseAnnouncement::ATTRIBUTE_APPLY_ONLINE;
    const ATTRIBUTE_APPLY_SCENE     = BaseAnnouncement::ATTRIBUTE_APPLY_SCENE;
    const ATTRIBUTE_APPLY_TELEPHONE = BaseAnnouncement::ATTRIBUTE_APPLY_TELEPHONE;
    const ATTRIBUTE_APPLY_FAX       = BaseAnnouncement::ATTRIBUTE_APPLY_FAX;
    const ATTRIBUTE_APPLY_MAIL      = BaseAnnouncement::ATTRIBUTE_APPLY_MAIL;
    const ATTRIBUTE_APPLY_OTHER     = BaseAnnouncement::ATTRIBUTE_APPLY_OTHER;

    // 职位是否有消费
    const IS_CONSUME_RELEASE_YES = 1;
    const IS_CONSUME_RELEASE_NO  = 2;

    // 投递限制
    const DELIVERY_LIMIT_EDUCATION = 1;
    const DELIVERY_LIMIT_MATERIAL  = 2;
    const DELIVERY_LIMIT_LIST      = [
        self::DELIVERY_LIMIT_EDUCATION => '学历',
        self::DELIVERY_LIMIT_MATERIAL  => '应聘材料',
    ];
    //投递类型
    const DELIVERY_TYPE_UP_ANNOUNCEMENT = 0;//跟公告
    const DELIVERY_TYPE_OUTER           = 1;//站外投递
    const DELIVERY_TYPE_OUTSIDE         = 2;//站内投递
    const DELIVERY_TYPE                 = [
        self::DELIVERY_TYPE_OUTER,
        self::DELIVERY_TYPE_OUTSIDE,
    ];
    const DELIVERY_TYPE_NAME            = [
        self::DELIVERY_TYPE_OUTER   => '站外投递',
        self::DELIVERY_TYPE_OUTSIDE => '站内投递',
    ];

    const ATTRIBUTE_APPLY_LIST = [
        self::ATTRIBUTE_APPLY_EMAIL     => '电子邮件',
        self::ATTRIBUTE_APPLY_ONLINE    => '网上系统',
        self::ATTRIBUTE_APPLY_SCENE     => '现场报名',
        self::ATTRIBUTE_APPLY_TELEPHONE => '电话报名',
        self::ATTRIBUTE_APPLY_FAX       => '传真',
        self::ATTRIBUTE_APPLY_MAIL      => '邮寄',
        self::ATTRIBUTE_APPLY_OTHER     => '其他',
    ];

    //是否被小程序调用
    const IS_MINIAPP_YES    = 1;
    const IS_MINIAPP_NO     = 2;
    const IS_MINIAPP_NO_SET = 0;
    //是否被运营手动标记
    const IS_MANUAL_TAG_NONE = 0;
    const IS_MANUAL_TAG_YES  = 1;
    const IS_MANUAL_TAG_NO   = 2;

    const IS_MINIAPP_LIST = [
        self::IS_MINIAPP_YES => '是',
        self::IS_MINIAPP_NO  => '否',
        // self::IS_MINIAPP_NO_SET => '未设置',
    ];

    //编制结论就是——
    //运营端-有编制：当作是其中一种编制类型，等同于行政编制/事业编
    //求职端-编制：勾选了了任意一种编制类型，均展示“编制”标签，操作“编制”查询时均能被筛选出来
    //是否编制
    const IS_ESTABLISHMENT_YES = 1;//有编制
    const IS_ESTABLISHMENT_NO  = 2;//无编制

    //职位投递热度
    const APPLY_HEAT_TYPE_PRIMARY   = 1;  //一般
    const APPLY_HEAT_TYPE_HOT       = 2;  //较热
    const APPLY_HEAT_TYPE_EXPLOSIVE = 3;  //火爆
    const APPLY_HEAT_TYPE_LIST      = [
        self::APPLY_HEAT_TYPE_PRIMARY,
        self::APPLY_HEAT_TYPE_HOT,
        self::APPLY_HEAT_TYPE_EXPLOSIVE,
    ];

    const APPLY_HEAT_TYPE_TEXT_LIST = [
        self::APPLY_HEAT_TYPE_PRIMARY   => '一般',
        self::APPLY_HEAT_TYPE_HOT       => '较热',
        self::APPLY_HEAT_TYPE_EXPLOSIVE => '火爆',
    ];

    //列表的vip广告
    const VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST       = 'pcJobList';
    const VIP_SHOWCASE_POSITION_TYPE_PC_PERSON         = 'pcPerson';
    const VIP_SHOWCASE_POSITION_TYPE_H5_JOB_LIST       = 'h5JobList';
    const VIP_SHOWCASE_POSITION_TYPE_H5_INDEX          = 'h5Index';
    const VIP_SHOWCASE_POSITION_TYPE_H5_FIRST_COLUMN   = 'h5FirstColumn';
    const VIP_SHOWCASE_POSITION_TYPE_H5_SECOND_COLUMN  = 'h5SecondColumn';
    const VIP_SHOWCASE_POSITION_TYPE_MINI_JOB_LIST     = 'miniJobList';
    const VIP_SHOWCASE_POSITION_TYPE_MINI_ANNOUNCEMENT = 'miniAnnouncement';

    const VIP_SHOWCASE_BELONG_PLATFORM = [
        'pc'     => [
            self::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST,
            self::VIP_SHOWCASE_POSITION_TYPE_PC_PERSON,
        ],
        'mobile' => [
            self::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST,
            self::VIP_SHOWCASE_POSITION_TYPE_PC_PERSON,
        ],
    ];

    const VIP_SHOWCASE_INFO_LIST = [
        self::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST       => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/job-vip.png?t=20240701',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/job-quick.png?t=20240701',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_PC_PERSON         => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/person-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/person-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_H5_INDEX          => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_H5_FIRST_COLUMN   => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_H5_JOB_LIST       => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_H5_SECOND_COLUMN  => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-column-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-column-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_MINI_JOB_LIST     => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
        self::VIP_SHOWCASE_POSITION_TYPE_MINI_ANNOUNCEMENT => [
            'vip'     => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-vip.png?t=20240327',
                'url' => '/vip.html',
            ],
            'jobFast' => [
                'img' => 'https://img.gaoxiaojob.com/uploads/static/showcase/mobile-quick.png?t=20240327',
                'url' => '/job-fast.html',
            ],
        ],
    ];

    /**
     * 获取薪资类型
     * @param $wageType
     * @return string
     */
    public static function getWageName($wageType)
    {
        if ($wageType == self::WAGE_TYPE_MONEY) {
            return '月';
        } elseif ($wageType == self::WAGE_TYPE_YEAR) {
            return '年';
        } elseif ($wageType == self::WAGE_TYPE_DAY) {
            return '日';
        }
    }

    /**
     * 创建职位信息
     * @throws Exception
     */
    public static function createInfo($data)
    {
        $baseJob                  = new self();
        $baseJob->add_time        = $data['add_time'];
        $baseJob->member_id       = $data['member_id'];
        $baseJob->company_id      = $data['company_id'];
        $baseJob->name            = $data['name'];
        $baseJob->code            = $data['code'];
        $baseJob->job_category_id = $data['job_category_id'];
        $baseJob->education_type  = $data['education_type'];
        $baseJob->major_id        = $data['major_id'];
        $baseJob->nature_type     = $data['nature_type'];
        $baseJob->wage_type       = $data['wage_type'];
        $baseJob->is_negotiable   = $data['is_negotiable'];
        $baseJob->min_wage        = $data['min_wage'];
        $baseJob->max_wage        = $data['max_wage'];
        $baseJob->experience_type = $data['experience_type'];
        $baseJob->age_type        = $data['age_type'];
        $baseJob->title_type      = $data['title_type'];
        $baseJob->political_type  = $data['political_type'];
        $baseJob->abroad_type     = $data['abroad_type'];
        $baseJob->amount          = $data['amount'];
        $baseJob->department      = $data['department'];
        $baseJob->province_id     = $data['province_id'];
        $baseJob->city_id         = $data['city_id'];
        $baseJob->district_id     = $data['district_id'];
        $baseJob->address         = $data['address'];
        $baseJob->welfare_tag     = $data['welfare_tag'];
        $baseJob->period_date     = str_replace([
            'T',
            'Z',
        ], ' ', $data['period_date']);;
        $baseJob->duty             = $data['duty'];
        $baseJob->requirement      = $data['requirement'];
        $baseJob->remark           = $data['remark'];
        $baseJob->status           = $data['status'];
        $baseJob->audit_status     = $data['audit_status'];
        $baseJob->creator          = $data['creator'];
        $baseJob->apply_audit_time = $data['apply_audit_time'];
        $baseJob->is_show          = BaseJob::IS_SHOW_YES;

        if (!$baseJob->save()) {
            throw new Exception($baseJob->getFirstErrorsMessage());
        }

        return $baseJob->id;
    }

    /**
     * 获取单条职位信息
     * @param $where
     * @param $select
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function selectInfo($where, $select)
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->asArray()
            ->one();
    }

    /**
     * 获取多条职位信息
     * @param array  $where
     * @param array  $select
     * @param array  $andWhere
     * @param string $limit
     * @param string $offset
     * @param string $orderBy
     * @param string $count
     * @return array|ActiveQuery|\yii\db\ActiveRecord[]
     */
    public static function selectInfos(
        array  $where = [],
        array  $select = [],
        array  $andWhere = [],
        string $limit = '',
        string $offset = '',
        string $orderBy = ''
    ) {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->select($select)
            ->limit($limit)
            ->offset($offset)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 获取总条数
     * @param array $where
     * @param array $andWhere
     * @return bool|int|string|null
     */
    public static function countInfos(
        array $where = [],
        array $andWhere = []
    ) {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->asArray()
            ->count();
    }

    /**
     * 查询工作表标签
     * @param      $welfareTag
     * @param null $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function findWelfareTag($welfareTag, $memberId = null)
    {
        $andWhere = [];
        if ($memberId) {
            $andWhere = [
                'member_id' => $memberId,
            ];
        }
        $select = [
            "id",
        ];

        return self::find()
            ->where("find_in_set($welfareTag,welfare_tag)")
            ->andWhere($andWhere)
            ->select($select)
            ->asArray()
            ->one();
    }

    public static function newSearchForList($data)
    {
        // 迁移到服务层
        $app = new CommonSearchApplication();

        return $app->pcJobListSearch($data);
    }

    /** 专门给职位列表用的,不要随便在这里加什么奇奇怪怪的东西 */
    public static function searchForList($data)
    {
        $app = CommonSearchApplication::getInstance();

        return $app->h5JobListSearch($data);
    }

    private static function checkSearchForList($data)
    {
        // 获取环境
        $env = Yii::$app->params['environment'];

        // 必须是正式环境才需要
        if ($env != 'prod') {
            return true;
        }

        // 在这里做一些非法字段的过滤
        $major    = $data['majorId'];
        $majorIds = explode('_', $major);
        // 最多5个
        if (count($majorIds) > 5) {
            throw new \Exception('非法搜索');
        }

        // 每一个都必须是数字
        foreach ($majorIds as $majorId) {
            if (!$major) {
                continue;
            }
            if (!is_numeric($majorId)) {
                throw new \Exception('非法字符');
            }
        }

        $ip = IpHelper::getIp();

        $paramIpList = Yii::$app->params['ipWhiteList'];

        if (in_array($ip, $paramIpList)) {
            return true;
        }

        $key       = Cache::ALL_IP_WHITE_LIST_JOB_SEARCH_KEY . ':' . $ip;
        $minute    = date('YmdHi');
        $countData = json_decode(Cache::get($key), true);
        // 同一分钟不允许超过10次

        $value = $countData[$minute] + 1;

        // if ($value > 30) {
        //     throw new \Exception('您搜索的太频繁了');
        // }
        $countData[$minute] = $value;
        Cache::set($key, json_encode($countData), 3600);

        return true;

        $paramIpList = Yii::$app->params['ipWhiteList'];

        // 这里还有一个通过脚本调用过来的,这里其实可以从redis里面取?
        $redis = new \Redis();
        $redis->connect('127.0.0.1');
        $redis->select(9);

        // 这个是公司内部的ip地址,会定时更新,具体逻辑可以参考这里  https://gaocai.yuque.com/dg74yi/hob5qt/dgmvr6
        $key = 'COMMON:INTRANET_IP';

        $INTRANET_IP = $redis->get($key);

        if ($INTRANET_IP) {
            // 合并白名单
            $ipWhiteList = array_merge($paramIpList, explode(',', $INTRANET_IP));
        } else {
            $ipWhiteList = $paramIpList ?: [];
        }

        // 白名单
        if (!in_array($ip, $ipWhiteList)) {
            $key       = Cache::ALL_IP_WHITE_LIST_JOB_SEARCH_KEY . ':' . $ip;
            $minute    = date('YmdHi');
            $countData = json_decode(Cache::get($key), true);
            // 同一分钟不允许超过10次

            $value = $countData[$minute] + 1;

            // if ($value > 30) {
            //     throw new \Exception('您搜索的太频繁了');
            // }
            $countData[$minute] = $value;
            Cache::set($key, json_encode($countData), 3600);
        }

        return true;
    }

    /**
     * 搜索职位(这个方法已经很巨大了,不能所有入口都来这里,不是什么页面都需要同样的数据的
     * @throws \Exception
     */
    // public static function search($data, $needPageInfo = false, $needOffLine = false)
    // {
    //     $ids = [];
    //     if (!empty($data['wageId'])) {
    //         $searchJobModel = self::find()
    //             ->where(1);
    //         //薪资范围查询
    //         $wageInfo = BaseDictionary::getMinAndMaxWage($data['wageId']);
    //         //面议
    //         if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
    //             $searchJobModel->andWhere([
    //                 'min_wage' => 0,
    //             ]);
    //             $searchJobModel->andWhere([
    //                 'max_wage' => 0,
    //             ]);
    //         }
    //         //其他
    //         if ($wageInfo['min'] > 0) {
    //             $searchJobModel->andWhere([
    //                 'or',
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
    //                     [
    //                         '>=',
    //                         'min_wage',
    //                         (int)$wageInfo['min'],
    //                     ],
    //                 ],
    //
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_DAY],
    //                     [
    //                         '>=',
    //                         'min_wage',
    //                         ($wageInfo['min'] / 30),
    //                     ],
    //
    //                 ],
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
    //                     [
    //                         '>=',
    //                         'min_wage',
    //                         ($wageInfo['min'] * 12),
    //                     ],
    //                 ],
    //             ]);
    //         }
    //
    //         if ($wageInfo['max'] > 0) {
    //             $searchJobModel->andWhere([
    //                 'or',
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
    //                     [
    //                         '<=',
    //                         'max_wage',
    //                         (int)$wageInfo['max'],
    //                     ],
    //                 ],
    //
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_DAY],
    //                     [
    //                         '<=',
    //                         'max_wage',
    //                         ($wageInfo['max'] / 30),
    //                     ],
    //
    //                 ],
    //                 [
    //                     'and',
    //                     ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
    //                     [
    //                         '<=',
    //                         'min_wage',
    //                         ($wageInfo['max'] * 12),
    //                     ],
    //                 ],
    //             ]);
    //         }
    //         $jobListInfo = $searchJobModel->select('id')
    //             ->asArray()
    //             ->indexBy('id')
    //             ->all();
    //         $ids[]       = array_keys($jobListInfo);
    //     }
    //
    //     //学科查询特殊处理
    //     $majorJobIds = [];
    //     if (!empty($data['majorId'])) {
    //         //判断当前学科的等级
    //         //            $majorLevel = BaseMajor::findOneVal(['id' => $data['majorId']], 'level');
    //         //            if ($majorLevel == 1) {
    //         //                //如果是第一级，要找出第二级所有学科
    //         //                $majorIdArr = BaseMajor::find()
    //         //                    ->where(['status' => BaseMajor::STATUS_ACTIVE])
    //         //                    ->andWhere(['parent_id' => $data['majorId']])
    //         //                    ->andWhere(['level' => 2])
    //         //                    ->indexBy('id')
    //         //                    ->asArray()
    //         //                    ->all();
    //         //                $majorIdArr = array_keys($majorIdArr);
    //         //
    //         //                $majorInfo = self::formatFindInSetQuery($majorIdArr, 'major_id');
    //         //
    //         //                $jobIds = self::find()
    //         //                    ->where($majorInfo)
    //         //                    ->select('id')
    //         //                    ->indexBy('id')
    //         //                    ->asArray()
    //         //                    ->all();
    //         //                $ids[]  = array_keys($jobIds);
    //         //            } else {
    //         //                if ($majorLevel == 2) {
    //         //                    $majorId    = $data['majorId'];
    //         //                    $majorWhere = "find_in_set($majorId, major_id)";
    //         //                }
    //         //            }
    //
    //         //        if (count($ids) > 0) {
    //         //            //合并
    //         //            if (count($ids) > 1) {
    //         //                //数组大于1，取交集
    //         //                $ids = call_user_func_array('array_intersect', $ids);
    //         //            } elseif (count($ids) == 1) {
    //         //                $ids = $ids[0];
    //         //            }
    //         //        }
    //
    //         //todo 这里修改成多选
    //         $majorIds   = explode('_', $data['majorId']);
    //         $majorWhere = ['or'];
    //         foreach ($majorIds as $item) {
    //             $majorLevel = BaseMajor::findOneVal(['id' => $item], 'level');
    //             if ($majorLevel == 1) {
    //                 //如果是第一级，要找出第二级所有学科
    //                 $majorIdArr = BaseMajor::find()
    //                     ->where(['status' => BaseMajor::STATUS_ACTIVE])
    //                     ->andWhere(['parent_id' => $item])
    //                     ->andWhere(['level' => 2])
    //                     ->indexBy('id')
    //                     ->asArray()
    //                     ->all();
    //                 $majorIdArr = array_keys($majorIdArr);
    //             } else {
    //                 $majorIdArr = [$item];
    //             }
    //             $majorInfo = self::formatFindInSetQuery($majorIdArr, 'major_id');
    //             $jobIds    = self::find()
    //                 ->where($majorInfo)
    //                 ->select('id')
    //                 ->indexBy('id')
    //                 ->asArray()
    //                 ->all();
    //             $ids       = array_merge($ids, array_keys($jobIds));
    //         }
    //     }
    //
    //     if (count($ids) > 0) {
    //         //合并
    //         $ids = array_flip($ids);
    //         $ids = array_keys($ids);
    //     }
    //
    //     $searchModel = self::find()
    //         ->alias('j')
    //         ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
    //         ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
    //         ->where(['j.is_show' => self::IS_SHOW_YES]);
    //
    //     //存在ids，或者存在任意触发ids的条件（即ids为空，查询数据为空），都需要加入条件搜索，
    //     if (!empty($ids) || !empty($data['wageId']) || !empty($data['majorId'])) {
    //         $searchModel->andWhere([
    //             'in',
    //             'j.id',
    //             $ids,
    //         ]);
    //     }
    //
    //     //其他情况默认为职位关键词查询
    //     $searchModel->andFilterWhere([
    //         'or',
    //         [
    //             'like',
    //             'j.name',
    //             $data['keyword'],
    //         ],
    //         [
    //             'like',
    //             'c.full_name',
    //             $data['keyword'],
    //         ],
    //         [
    //             'like',
    //             'j.department',
    //             $data['keyword'],
    //         ],
    //         [
    //             'like',
    //             'a.title',
    //             $data['keyword'],
    //         ],
    //     ]);
    //
    //     //城市查询
    //
    //     if ($data['areaId']) {
    //         $area = $data['areaId'];
    //         if (count(explode('_', $area)) > 1) {
    //             $areaIds = explode('_', $area);
    //         } else {
    //             $areaIds = [$area];
    //         }
    //
    //         $areaIds = BaseArea::getCityIds($areaIds);
    //
    //         $searchModel->andWhere(['j.city_id' => $areaIds]);
    //     }
    //
    //     // if (count(explode('_', $data['areaId'])) > 1) {
    //     //     $areaIds = explode('_', $data['areaId']);
    //     //     $searchModel->andFilterWhere([
    //     //         'in',
    //     //         'j.city_id',
    //     //         $areaIds,
    //     //     ]);
    //     // } else {
    //     //     $searchModel->andFilterWhere(['j.city_id' => $data['areaId']]);
    //     // }
    //
    //     //单位性质查询
    //     if (count(explode('_', $data['companyNature'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'c.nature',
    //             explode('_', $data['companyNature']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['c.nature' => $data['companyNature']]);
    //     }
    //
    //     if ($data['companyType']) {
    //         //单位类型查询
    //         if (count(explode('_', $data['companyType'])) > 1) {
    //             $searchModel->andFilterWhere([
    //                 'in',
    //                 'c.type',
    //                 explode('_', $data['companyType']),
    //             ]);
    //         } else {
    //             $searchModel->andFilterWhere(['c.type' => $data['companyType']]);
    //         }
    //     }
    //
    //     //学科分类查询
    //     if (!empty($data['majorId']) && !empty($majorWhere)) {
    //         $searchModel->andWhere($majorWhere);
    //     }
    //
    //     //职位福利查询
    //     if (!empty($data['welfareLabelId'])) {
    //         $welfareLabelArr = explode('_', $data['welfareLabelId']);
    //         foreach ($welfareLabelArr as $k => $v) {
    //             $searchModel->andWhere(new Expression("FIND_IN_SET(:tags_{$v}, welfare_tag)", [":tags_{$v}" => $v]));
    //         }
    //     }
    //
    //     //学历查询
    //     if (count(explode('_', $data['educationType'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'j.education_type',
    //             explode('_', $data['educationType']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['j.education_type' => $data['educationType']]);
    //     }
    //
    //     //工作经验查询
    //     if (count(explode('_', $data['experienceType'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'j.experience_type',
    //             explode('_', $data['experienceType']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['j.experience_type' => $data['experienceType']]);
    //     }
    //
    //     //职位性质查询
    //     if (count(explode('_', $data['natureType'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'j.nature_type',
    //             explode('_', $data['natureType']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['j.nature_type' => $data['natureType']]);
    //     }
    //
    //     //单位规模查询
    //     if (count(explode('_', $data['scale'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'c.scale',
    //             explode('_', $data['scale']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['c.scale' => $data['scale']]);
    //     }
    //
    //     //行业类别查询
    //     if (count(explode('_', $data['industryId'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'c.industry_id',
    //             explode('_', $data['industryId']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);
    //     }
    //
    //     //单位查询
    //     if (count(explode('_', $data['companyId'])) > 1) {
    //         $searchModel->andFilterWhere([
    //             'in',
    //             'j.company_id',
    //             explode('_', $data['companyId']),
    //         ]);
    //     } else {
    //         $searchModel->andFilterWhere(['j.company_id' => $data['companyId']]);
    //     }
    //
    //     if ($data['jobCategoryId'] && $data['jobCategoryId'] != 'undefined') {
    //         if (count(explode('_', $data['jobCategoryId'])) > 1) {
    //             $searchModel->andFilterWhere([
    //                 'in',
    //                 'j.job_category_id',
    //                 explode('_', $data['jobCategoryId']),
    //             ]);
    //         } else {
    //             $searchModel->andFilterWhere(['j.job_category_id' => $data['jobCategoryId']]);
    //         }
    //     }
    //
    //     //todo：下面的暂时没有做，或是需要调整
    //     //岗位类型\职位类型查询
    //     // jobType改成多选，以上保留，预防别的调用
    //     if ($data['jobType'] && $data['jobType'] != 'undefined') {
    //         if (count(explode('_', $data['jobType'])) > 1) {
    //             $searchModel->andFilterWhere([
    //                 'in',
    //                 'j.job_category_id',
    //                 explode('_', $data['jobType']),
    //             ]);
    //         } else {
    //             $searchModel->andFilterWhere(['j.job_category_id' => $data['jobType']]);
    //         }
    //     }
    //
    //     //发布时间查询
    //     if (!empty($data['releaseTimeType'])) {
    //         $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($data['releaseTimeType']);
    //
    //         if (!empty($releaseTimeInfo)) {
    //             $searchModel->andFilterWhere([
    //                 'between',
    //                 'j.release_time',
    //                 $releaseTimeInfo,
    //                 date('Y-m-d H:i:s'),
    //             ]);
    //         } else {
    //             throw new Exception('发布时间参数错误');
    //         }
    //     }
    //     //克隆原始$searchModel到$onlineQuery
    //     $onlineQuery = clone $searchModel;
    //     //获取在线职位总数
    //     $onlineJobCount = $onlineQuery->andWhere(['j.status' => self::STATUS_ONLINE])
    //         ->count();
    //     //状态条件移动到这个位置是不影响原始逻辑
    //     if ($needOffLine) {
    //         $searchModel->andWhere([
    //             'in',
    //             'j.status',
    //             [
    //                 self::STATUS_ONLINE,
    //                 self::STATUS_OFFLINE,
    //             ],
    //         ]);
    //     } else {
    //         $searchModel->andWhere(['j.status' => self::STATUS_ONLINE]);
    //     }
    //     //获取总数量
    //     $count = $searchModel->count();
    //
    //     $pageSize = $data['pageSize'] ?: \Yii::$app->params['jobListDefaultPageSize'];
    //
    //     $pages = self::setPage($count, $data['page'], $pageSize);
    //
    //     //获取排序方式
    //     if ($data['sort'] == 'new') {
    //         //最新排序
    //         $sort = 'j.status desc,j.refresh_time desc,j.id desc';
    //     } elseif ($data['sort'] == 'default') {
    //         //综合排序
    //         $sort = 'j.status desc,j.click desc,j.id desc';
    //     } else {
    //         // 单位等级排序,refresh_time日期
    //         $sort = 'j.status desc,j.refresh_time desc,j.id desc';
    //     }
    //
    //     $list = $searchModel->select([
    //         'j.id as jobId',
    //         'j.status',
    //         'j.name as jobName',
    //         'j.company_id as companyId',
    //         'j.min_wage as minWage',
    //         'j.max_wage as maxWage',
    //         'j.wage_type as wageType',
    //         'j.experience_type as experienceType',
    //         'j.education_type as educationType',
    //         'j.amount',
    //         'j.job_category_id as jobCategoryId',
    //         'j.welfare_tag as welfareTag',
    //         'j.province_id as provinceId',
    //         'j.city_id as cityId',
    //         'c.full_name as companyName',
    //         'c.type as companyType',
    //         'c.nature as companyNature',
    //         'j.release_time as releaseTime',
    //         'j.refresh_time as refreshTime',
    //         'j.apply_type as applyType',
    //         'j.apply_address as applyAddress',
    //         'j.announcement_id as announcementId',
    //         'a.title as announcementName',
    //         'j.major_id as majorId',
    //     ])
    //         ->orderBy($sort)
    //         ->asArray()
    //         ->offset($pages['offset'])
    //         ->limit($pages['limit'])
    //         ->all();
    //
    //     foreach ($list as $k => &$jobRecord) {
    //         $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
    //         //拼接工资
    //         if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
    //             $jobRecord['wage'] = '面议';
    //         } else {
    //             $jobRecord['wage'] = self::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
    //                 $jobRecord['wageType']);
    //         }
    //         //获取经验要求
    //         $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
    //         //获取学历水平
    //         $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
    //         //获取意向职能
    //         $jobRecord['jobCategory'] = BaseCategoryJob::getName($jobRecord['jobCategoryId']);
    //         //获取福利标签
    //         $jobRecord['welfareTagArr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($jobRecord['welfareTag']),
    //             0, 2);
    //         //获取地区名称
    //         $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . '-' . BaseArea::getAreaName($jobRecord['cityId']);
    //         $jobRecord['city']     = BaseArea::getAreaName($jobRecord['cityId']);
    //         //获取单位类型
    //         $jobRecord['companyTypeName'] = BaseDictionary::getCompanyTypeName($jobRecord['companyType']);
    //         //获取单位性质
    //         $jobRecord['companyNatureName'] = BaseDictionary::getCompanyNatureName($jobRecord['companyNature']);
    //         //获取职位需求专业
    //         $major = '';
    //         if ($jobRecord['majorId']) {
    //             $jobMajorIds = explode(',', $jobRecord['majorId']);
    //             if (count($jobMajorIds) > 0) {
    //                 $major = BaseMajor::getAllMajorName($jobMajorIds);
    //             }
    //         }
    //         $jobRecord['major'] = $major;
    //         //处理发布时间
    //         $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
    //         $releaseTime  = strtotime($jobRecord['releaseTime']);
    //         if ($releaseTime > $thisYearTime) {
    //             $jobRecord['releaseTime'] = date('m-d', $releaseTime);
    //         } else {
    //             $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
    //         }
    //         $jobRecord['url']             = Url::toRoute([
    //             'job/detail',
    //             'id' => $jobRecord['jobId'],
    //         ]);
    //         $jobRecord['companyUrl']      = Url::toRoute([
    //             'company/detail',
    //             'id' => $jobRecord['companyId'],
    //         ]);
    //         $jobRecord['announcementUrl'] = Url::toRoute([
    //             'announcement/detail',
    //             'id' => $jobRecord['announcementId'],
    //         ]);
    //         //判断职位是否是合作单位的职位
    //         $jobRecord['applyStatus']      = BaseJob::JOB_APPLY_STATUS_NO;
    //         $jobRecord['userEmail']        = '';
    //         $jobRecord['isEmailApply']     = "false";
    //         $jobRecord['shortRefreshTime'] = date('m-d', strtotime($jobRecord['refreshTime']));
    //
    //         $cooperationInfo = BaseCompany::findOneVal(['id' => $jobRecord['companyId']], 'is_cooperation');
    //         if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
    //             //如果是合作单位，站内投递
    //             $jobRecord['isCooperation'] = "true";
    //             //如果用户已经登录了，获取用户投递信息
    //             if (!empty($data['memberId'])) {
    //                 //获取用户对该职位投递情况
    //                 $jobRecord['applyStatus'] = BaseJobApply::checkJobApplyStatus($data['memberId'],
    //                     $jobRecord['jobId']);
    //             }
    //         } else {
    //             //站外投递
    //             $jobRecord['isCooperation'] = "false";
    //             if (!empty($data['memberId'])) {
    //                 //获取用户对该职位投递情况
    //                 $jobRecord['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($data['memberId'],
    //                     $jobRecord['jobId']);
    //
    //                 $jobRecord['userEmail'] = BaseMember::findOneVal(['id' => $data['memberId']], 'email');;
    //             }
    //             //判断职位投递方式是否是邮箱投递
    //             $applyTypeArr = explode(',', $jobRecord['applyType']);
    //
    //             if (in_array(self::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
    //                 $jobRecord['isEmailApply'] = "true";
    //             }
    //
    //             foreach ($applyTypeArr as $k => $v) {
    //                 $jobRecord['applyTypeText'] .= BaseDictionary::getSignUpName($v) . ';';
    //             }
    //             if (!empty($jobRecord['applyTypeText'])) {
    //                 //去除结尾分号
    //                 $jobRecord['applyTypeText'] = substr($jobRecord['applyTypeText'], 0, -1);
    //             }
    //         }
    //     }
    //     if ($needPageInfo) {
    //         return [
    //             'list'           => $list,
    //             'pageSize'       => $pageSize,
    //             'currentPage'    => $data['page'],
    //             'totalNum'       => $count,
    //             'onlineTotalNum' => $onlineJobCount,
    //         ];
    //     } else {
    //         return $list;
    //     }
    // }

    public static function search($data, $needPageInfo = false, $needOffLine = false)
    {
        $ids = [];
        if (!empty($data['wageId'])) {
            $searchJobModel = self::find()
                ->where(1);
            //薪资范围查询
            $wageInfo = BaseDictionary::getMinAndMaxWage($data['wageId']);
            //面议
            if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                $searchJobModel->andWhere([
                    'min_wage' => 0,
                ]);
                $searchJobModel->andWhere([
                    'max_wage' => 0,
                ]);
            }
            //其他
            if ($wageInfo['min'] > 0) {
                $searchJobModel->andWhere([
                    'or',
                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
                        [
                            '>=',
                            'min_wage',
                            (int)$wageInfo['min'],
                        ],
                    ],

                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_DAY],
                        [
                            '>=',
                            'min_wage',
                            ($wageInfo['min'] / 30),
                        ],

                    ],
                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
                        [
                            '>=',
                            'min_wage',
                            ($wageInfo['min'] * 12),
                        ],
                    ],
                ]);
            }

            if ($wageInfo['max'] > 0) {
                $searchJobModel->andWhere([
                    'or',
                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_MONEY],
                        [
                            '<=',
                            'max_wage',
                            (int)$wageInfo['max'],
                        ],
                    ],

                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_DAY],
                        [
                            '<=',
                            'max_wage',
                            ($wageInfo['max'] / 30),
                        ],

                    ],
                    [
                        'and',
                        ['wage_type' => BaseJob::WAGE_TYPE_YEAR],
                        [
                            '<=',
                            'min_wage',
                            ($wageInfo['max'] * 12),
                        ],
                    ],
                ]);
            }
            $jobListInfo = $searchJobModel->select('id')
                ->asArray()
                ->indexBy('id')
                ->all();
            $ids[]       = array_keys($jobListInfo);
        }

        //学科查询特殊处理
        $majorJobIds = [];
        if (!empty($data['majorId'])) {
            //判断当前学科的等级
            //            $majorLevel = BaseMajor::findOneVal(['id' => $data['majorId']], 'level');
            //            if ($majorLevel == 1) {
            //                //如果是第一级，要找出第二级所有学科
            //                $majorIdArr = BaseMajor::find()
            //                    ->where(['status' => BaseMajor::STATUS_ACTIVE])
            //                    ->andWhere(['parent_id' => $data['majorId']])
            //                    ->andWhere(['level' => 2])
            //                    ->indexBy('id')
            //                    ->asArray()
            //                    ->all();
            //                $majorIdArr = array_keys($majorIdArr);
            //
            //                $majorInfo = self::formatFindInSetQuery($majorIdArr, 'major_id');
            //
            //                $jobIds = self::find()
            //                    ->where($majorInfo)
            //                    ->select('id')
            //                    ->indexBy('id')
            //                    ->asArray()
            //                    ->all();
            //                $ids[]  = array_keys($jobIds);
            //            } else {
            //                if ($majorLevel == 2) {
            //                    $majorId    = $data['majorId'];
            //                    $majorWhere = "find_in_set($majorId, major_id)";
            //                }
            //            }

            //        if (count($ids) > 0) {
            //            //合并
            //            if (count($ids) > 1) {
            //                //数组大于1，取交集
            //                $ids = call_user_func_array('array_intersect', $ids);
            //            } elseif (count($ids) == 1) {
            //                $ids = $ids[0];
            //            }
            //        }

            //todo 这里修改成多选
            $majorIds   = explode('_', $data['majorId']);
            $majorWhere = ['or'];
            foreach ($majorIds as $item) {
                $majorLevel = BaseMajor::findOneVal(['id' => $item], 'level');
                if ($majorLevel == 1) {
                    //如果是第一级，要找出第二级所有学科
                    $majorIdArr = BaseMajor::find()
                        ->where(['status' => BaseMajor::STATUS_ACTIVE])
                        ->andWhere(['parent_id' => $item])
                        ->andWhere(['level' => 2])
                        ->indexBy('id')
                        ->asArray()
                        ->all();
                    $majorIdArr = array_keys($majorIdArr);
                } else {
                    $majorIdArr = [$item];
                }
                $majorInfo = self::formatFindInSetQuery($majorIdArr, 'major_id');
                $jobIds    = self::find()
                    ->where($majorInfo)
                    ->select('id')
                    ->indexBy('id')
                    ->asArray()
                    ->all();
                $ids       = array_merge($ids, array_keys($jobIds));
            }
        }

        if (count($ids) > 0) {
            //合并
            $ids = array_flip($ids);
            $ids = array_keys($ids);
        }

        $searchModel = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            ->where(['j.is_show' => self::IS_SHOW_YES]);

        //存在ids，或者存在任意触发ids的条件（即ids为空，查询数据为空），都需要加入条件搜索，
        if (!empty($ids) || !empty($data['wageId']) || !empty($data['majorId'])) {
            $searchModel->andWhere([
                'in',
                'j.id',
                $ids,
            ]);
        }

        //其他情况默认为职位关键词查询
        $searchModel->andFilterWhere([
            'or',
            [
                'like',
                'j.name',
                $data['keyword'],
            ],
            [
                'like',
                'c.full_name',
                $data['keyword'],
            ],
            [
                'like',
                'j.department',
                $data['keyword'],
            ],
            [
                'like',
                'a.title',
                $data['keyword'],
            ],
        ]);

        //城市查询

        if ($data['areaId']) {
            $area = $data['areaId'];
            if (count(explode('_', $area)) > 1) {
                $areaIds = explode('_', $area);
            } else {
                $areaIds = [$area];
            }

            $areaIds = BaseArea::getCityIds($areaIds);

            $searchModel->andWhere(['j.city_id' => $areaIds]);
        }

        // if (count(explode('_', $data['areaId'])) > 1) {
        //     $areaIds = explode('_', $data['areaId']);
        //     $searchModel->andFilterWhere([
        //         'in',
        //         'j.city_id',
        //         $areaIds,
        //     ]);
        // } else {
        //     $searchModel->andFilterWhere(['j.city_id' => $data['areaId']]);
        // }

        //单位性质查询
        if (count(explode('_', $data['companyNature'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'c.nature',
                explode('_', $data['companyNature']),
            ]);
        } else {
            $searchModel->andFilterWhere(['c.nature' => $data['companyNature']]);
        }

        if ($data['companyType']) {
            //单位类型查询
            if (count(explode('_', $data['companyType'])) > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'c.type',
                    explode('_', $data['companyType']),
                ]);
            } else {
                $searchModel->andFilterWhere(['c.type' => $data['companyType']]);
            }
        }

        //学科分类查询
        if (!empty($data['majorId']) && !empty($majorWhere)) {
            $searchModel->andWhere($majorWhere);
        }

        //职位福利查询
        if (!empty($data['welfareLabelId'])) {
            $welfareLabelArr = explode('_', $data['welfareLabelId']);
            foreach ($welfareLabelArr as $k => $v) {
                $searchModel->andWhere(new Expression("FIND_IN_SET(:tags_{$v}, welfare_tag)", [":tags_{$v}" => $v]));
            }
        }

        //学历查询
        if (count(explode('_', $data['educationType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.education_type',
                explode('_', $data['educationType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.education_type' => $data['educationType']]);
        }

        //工作经验查询
        if (count(explode('_', $data['experienceType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.experience_type',
                explode('_', $data['experienceType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.experience_type' => $data['experienceType']]);
        }

        //职位性质查询
        if (count(explode('_', $data['natureType'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.nature_type',
                explode('_', $data['natureType']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.nature_type' => $data['natureType']]);
        }

        //单位规模查询
        if (count(explode('_', $data['scale'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'c.scale',
                explode('_', $data['scale']),
            ]);
        } else {
            $searchModel->andFilterWhere(['c.scale' => $data['scale']]);
        }

        //行业类别查询
        if (count(explode('_', $data['industryId'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'c.industry_id',
                explode('_', $data['industryId']),
            ]);
        } else {
            $searchModel->andFilterWhere(['c.industry_id' => $data['industryId']]);
        }

        //单位查询
        if (count(explode('_', $data['companyId'])) > 1) {
            $searchModel->andFilterWhere([
                'in',
                'j.company_id',
                explode('_', $data['companyId']),
            ]);
        } else {
            $searchModel->andFilterWhere(['j.company_id' => $data['companyId']]);
        }

        if ($data['jobCategoryId'] && $data['jobCategoryId'] != 'undefined') {
            if (count(explode('_', $data['jobCategoryId'])) > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'j.job_category_id',
                    explode('_', $data['jobCategoryId']),
                ]);
            } else {
                $searchModel->andFilterWhere(['j.job_category_id' => $data['jobCategoryId']]);
            }
        }

        //todo：下面的暂时没有做，或是需要调整
        //岗位类型\职位类型查询
        // jobType改成多选，以上保留，预防别的调用
        if ($data['jobType'] && $data['jobType'] != 'undefined') {
            if (count(explode('_', $data['jobType'])) > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'j.job_category_id',
                    explode('_', $data['jobType']),
                ]);
            } else {
                $searchModel->andFilterWhere(['j.job_category_id' => $data['jobType']]);
            }
        }

        //发布时间查询
        if (!empty($data['releaseTimeType'])) {
            $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($data['releaseTimeType']);

            if (!empty($releaseTimeInfo)) {
                $searchModel->andFilterWhere([
                    'between',
                    'j.release_time',
                    $releaseTimeInfo,
                    date('Y-m-d H:i:s'),
                ]);
            } else {
                throw new Exception('发布时间参数错误');
            }
        }
        //克隆原始$searchModel到$onlineQuery
        $onlineQuery = clone $searchModel;
        //获取在线职位总数
        $onlineJobCount = $onlineQuery->andWhere(['j.status' => self::STATUS_ONLINE])
            ->count();
        //状态条件移动到这个位置是不影响原始逻辑
        if ($needOffLine) {
            $searchModel->andWhere([
                'in',
                'j.status',
                [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ]);
        } else {
            $searchModel->andWhere(['j.status' => self::STATUS_ONLINE]);
        }
        //获取总数量
        $count = $searchModel->count();

        $pageSize = $data['pageSize'] ?: \Yii::$app->params['jobListDefaultPageSize'];

        $pages = self::setPage($count, $data['page'], $pageSize);

        //获取排序方式
        if ($data['sort'] == 'new') {
            //最新排序
            $sort = 'j.status desc,j.refresh_time desc,j.id desc';
        } elseif ($data['sort'] == 'default') {
            //综合排序
            $sort = 'j.status desc,j.click desc,j.id desc';
        } else {
            // 单位等级排序,refresh_time日期
            $sort = 'j.status desc,j.refresh_time desc,j.id desc';
        }

        $list = $searchModel->select([
            'j.id as jobId',
            'j.status',
            'j.name as jobName',
            'j.company_id as companyId',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.experience_type as experienceType',
            'j.education_type as educationType',
            'j.amount',
            'j.job_category_id as jobCategoryId',
            'j.welfare_tag as welfareTag',
            'j.province_id as provinceId',
            'j.city_id as cityId',
            'c.full_name as companyName',
            'c.type as companyType',
            'c.nature as companyNature',
            'j.release_time as releaseTime',
            'j.refresh_time as refreshTime',
            'j.apply_type as applyType',
            'j.apply_address as applyAddress',
            'j.announcement_id as announcementId',
            'a.title as announcementName',
            'j.major_id as majorId',
        ])
            ->orderBy($sort)
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();

        foreach ($list as $k => &$jobRecord) {
            $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
            //拼接工资
            if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                $jobRecord['wage'] = '面议';
            } else {
                $jobRecord['wage'] = self::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                    $jobRecord['wageType']);
            }
            //获取经验要求
            $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
            //获取学历水平
            $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
            //获取意向职能
            $jobRecord['jobCategory'] = BaseCategoryJob::getName($jobRecord['jobCategoryId']);
            //获取福利标签
            $jobRecord['welfareTagArr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($jobRecord['welfareTag']),
                0, 2);
            //获取地区名称
            $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . '-' . BaseArea::getAreaName($jobRecord['cityId']);
            $jobRecord['city']     = BaseArea::getAreaName($jobRecord['cityId']);
            //获取单位类型
            $jobRecord['companyTypeName'] = BaseDictionary::getCompanyTypeName($jobRecord['companyType']);
            //获取单位性质
            $jobRecord['companyNatureName'] = BaseDictionary::getCompanyNatureName($jobRecord['companyNature']);
            //获取职位需求专业
            $major = '';
            if ($jobRecord['majorId']) {
                $jobMajorIds = explode(',', $jobRecord['majorId']);
                if (count($jobMajorIds) > 0) {
                    $major = BaseMajor::getAllMajorName($jobMajorIds);
                }
            }
            $jobRecord['major'] = $major;
            //处理发布时间
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            $releaseTime  = strtotime($jobRecord['releaseTime']);
            if ($releaseTime > $thisYearTime) {
                $jobRecord['releaseTime'] = date('m-d', $releaseTime);
            } else {
                $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
            }
            $jobRecord['url']             = Url::toRoute([
                'job/detail',
                'id' => $jobRecord['jobId'],
            ]);
            $jobRecord['companyUrl']      = Url::toRoute([
                'company/detail',
                'id' => $jobRecord['companyId'],
            ]);
            $jobRecord['announcementUrl'] = Url::toRoute([
                'announcement/detail',
                'id' => $jobRecord['announcementId'],
            ]);
            //判断职位是否是合作单位的职位
            $jobRecord['applyStatus']      = BaseJob::JOB_APPLY_STATUS_NO;
            $jobRecord['userEmail']        = '';
            $jobRecord['isEmailApply']     = "false";
            $jobRecord['shortRefreshTime'] = date('m-d', strtotime($jobRecord['refreshTime']));

            $cooperationInfo = BaseCompany::findOneVal(['id' => $jobRecord['companyId']], 'is_cooperation');
            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                //如果是合作单位，站内投递
                $jobRecord['isCooperation'] = "true";
                //如果用户已经登录了，获取用户投递信息
                if (!empty($data['memberId'])) {
                    //获取用户对该职位投递情况
                    $jobRecord['applyStatus'] = BaseJobApply::checkJobApplyStatus($data['memberId'],
                        $jobRecord['jobId']);
                }
            } else {
                //站外投递
                $jobRecord['isCooperation'] = "false";
                if (!empty($data['memberId'])) {
                    //获取用户对该职位投递情况
                    $jobRecord['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($data['memberId'],
                        $jobRecord['jobId']);

                    $jobRecord['userEmail'] = BaseMember::findOneVal(['id' => $data['memberId']], 'email');;
                }
                //判断职位投递方式是否是邮箱投递
                $applyTypeArr = explode(',', $jobRecord['applyType']);

                if (in_array(self::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                    $jobRecord['isEmailApply'] = "true";
                }

                foreach ($applyTypeArr as $k => $v) {
                    $jobRecord['applyTypeText'] .= BaseDictionary::getSignUpName($v) . ';';
                }
                if (!empty($jobRecord['applyTypeText'])) {
                    //去除结尾分号
                    $jobRecord['applyTypeText'] = substr($jobRecord['applyTypeText'], 0, -1);
                }
            }
        }
        if ($needPageInfo) {
            return [
                'list'           => $list,
                'pageSize'       => $pageSize,
                'currentPage'    => $data['page'],
                'totalNum'       => $count,
                'onlineTotalNum' => $onlineJobCount,
            ];
        } else {
            return $list;
        }
    }

    /**
     * 职位信息获取（学历、性质、经验等）
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function transformation($data, $isConvertHump = true)
    {
        if ($isConvertHump) {
            $list = FormatConverter::convertHump($data);
        } else {
            $list = $data;
        }
        //关联公告，获得公告名称
        $data['announcementTitle'] = "";
        if ($list['status']) {
            $data['statusTitle'] = self::JOB_STATUS_NAME[$list['status']];
        }
        if ($list['member_id']) {
            $companyInfo         = BaseCompany::getCompanyInfo($list['member_id']);
            $data['companyName'] = $companyInfo['full_name'];
        }
        if ($list['is_article']) {
            if ($list['is_article'] == 1) {
                $data['isArticleTitle'] = "公告+职位模式";
            } else {
                $data['isArticleTitle'] = "纯职位模式";
            }
        }
        if ($list['is_stick']) {
            if ($list['is_stick'] == 1) {
                $data['isStickTitle'] = "长期";
            } else {
                $data['isStickTitle'] = "非长期";
            }
        }
        if ($list['job_category_id']) {
            $CompanyJobCategory       = BaseCategoryJob::getCompanyJobCategory($list['job_category_id']);
            $data['jobCategoryTitle'] = $CompanyJobCategory['secondName'] . '-' . $CompanyJobCategory['name'];
        }

        if ($list['education_type']) {
            $eductionTypeList           = BaseDictionary::getEducationList();
            $data['educationTypeTitle'] = $eductionTypeList[$list['education_type']];
            if ($list['education_type'] < 1) {
                $data['educationTypeTitle'] = "学历不限";
            }
        } else {
            $data['educationTypeTitle'] = "-";
        }

        if ($list['major_id']) {
            $majorIds           = explode(',', $list['major_id']);
            $data['majorTitle'] = BaseMajor::getAllMajorName($majorIds);

            if (strlen($list['major_id']) < 1) {
                $data['majorTitle'] = "专业不限";
            }
        } else {
            $data['majorTitle'] = "-";
        }

        if ($list['nature_type']) {
            $data['natureTypeTitle'] = BaseDictionary::getNatureName($list['nature_type']);
        }
        if ($list['wage_type']) {
            $jobWageTypeList       = self::JOB_WAGE_TYPE_NAME;
            $data['wageTypeTitle'] = $jobWageTypeList[$list['wage_type']];
        }
        if ($list['experience_type']) {
            $experienceList              = BaseDictionary::getExperienceList();
            $data['experienceTypeTitle'] = $experienceList[$list['experience_type']];
            if ($list['experience_type'] < 1) {
                $data['experienceTypeTitle'] = "经验不限";
            }
        } else {
            $data['experienceTypeTitle'] = "经验不限";
        }

        if ($list['title_type']) {
            $data['titleTypeTitle'] = BaseDictionary::getTitleName($list['title_type']);
            if ($list['title_type'] < 1) {
                $data['titleTypeTitle'] = "职称不限";
            }
        } else {
            $data['titleTypeTitle'] = "-";
        }
        if ($list['political_type']) {
            $data['politicalTypeTitle'] = BaseDictionary::getPoliticalStatusName($list['political_type']);
            if ($list['political_type'] < 1) {
                $data['politicalTypeTitle'] = "不限";
            }
        } else {
            $data['politicalTypeTitle'] = "-";
        }

        if ($list['district_id']) {
            $areaCache             = BaseArea::setAreaCache();
            $data['districtTitle'] = $areaCache[$list['district_id']]['name'];
        }
        if ($list['province_id']) {
            $areaCache             = BaseArea::setAreaCache();
            $data['provinceTitle'] = $areaCache[$list['province_id']]['name'];
        }
        if ($list['city_id']) {
            $areaCache         = BaseArea::setAreaCache();
            $data['cityTitle'] = $areaCache[$list['city_id']]['name'];
        }
        if ($list['offline_type']) {
            if ($list['offlineType'] == 2) {
                $data['offlineTypeTitle'] = "手动下线";
            } elseif ($list['offlineType'] == 3) {
                $data['offlineTypeTitle'] = "违规";
            } else {
                $data['offlineTypeTitle'] = "自动下线";
            }
        } else {
            $data['offlineTypeTitle'] = "-";
        }

        if ($list['gender_type']) {
            if ($list['gender_type'] == 1) {
                $data['genderTypeTitle'] = "男";
            } else {
                if ($list['gender_type'] == 2) {
                    $data['genderTypeTitle'] = "女";
                } else {
                    $data['genderTypeTitle'] = "不限";
                }
            }
        } else {
            $data['genderTypeTitle'] = "-";
        }

        if ($list['abroad_type']) {
            $data['abroadTypeTitle'] = "海外经历不限";
            if ($list['abroad_type'] == 1) {
                $data['abroadTypeTitle'] = "要求";
            }
            if ($list['abroad_type'] == 2) {
                $data['abroadTypeTitle'] = "不要求";
            }
        } else {
            $data['abroadTypeTitle'] = "-";
        }

        if ($list['welfare_tag']) {
            $welfareTag     = explode(',', $list['welfare_tag']);
            $welfareTagList = BaseWelfareLabel::find()
                ->select(['name'])
                ->where([
                    'in',
                    'id',
                    $welfareTag,
                ])
                ->andWhere([
                    'is_delete' => BaseWelfareLabel::SYSTEM_NO,
                ])
                ->asArray()
                ->all();

            $welfareTagTitle = '';
            foreach ($welfareTagList as $list) {
                $welfareTagTitle .= $list['name'] . ',';
            }
            $welfareTagTitle         = substr($welfareTagTitle, 0, -1);
            $data['welfareTagTitle'] = $welfareTagTitle;
        }

        $data['wage'] = BaseJob::formatWage($data['min_wage'], $data['max_wage'], $data['wage_type']);

        if ($list['file_ids']) {
            $data['fileList'] = BaseAnnouncement::getAppendixList($list['file_ids']);
        }

        return $data;
    }

    /**
     * 获取薪资对应字典code值
     * @param $min ,$max
     */
    public static function getWageId($min, $max): int
    {
        $code = 1;
        $data = [];

        // 获取对应薪资范围列表
        $wageRangeList = BaseDictionary::getWageRangeList();

        foreach ($wageRangeList as $k => $list) {
            if ($list == '面议') {
                $data[$k]['min'] = 0;
                $data[$k]['max'] = 0;
            } elseif ($list == '3k以下') {
                $data[$k]['min'] = 0;
                $data[$k]['max'] = 3000;
            } else {
                $wageArr         = explode('-', str_replace('k', '', $list));
                $data[$k]['min'] = $wageArr[0] * 1000;
                $data[$k]['max'] = $wageArr[1] * 1000;
            }
            $data[$k]['code'] = $k;
        }

        foreach ($data as $wage) {
            if ($min == $wage['min'] && $max == $wage['max']) {
                $code = $wage['code'];
            }
        }

        return $code;
    }

    /**
     * 编辑职位信息
     * @throws Exception
     */
    public static function editInfo($data, $condition)
    {
        $baseJob = new self();
        if (!self::updateAll($data, $condition)) {
            throw new Exception($baseJob->getFirstErrorsMessage());
        }
    }

    /**
     * 获取职位详情
     * @param $id
     * @param $isDelCache
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function getJobDetail($id, $memberId = '', $isDelCache = false)
    {
        $cacheKey = Cache::ALL_JOB_DETAIL_KEY . ':' . $id;
        //需要清空缓存重新设置
        if ($isDelCache) {
            Cache::delete($cacheKey);
        }
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            $jobDetail = json_decode($cacheData, true);
        } else {
            //todo:缺少报名方式、性别要求
            $jobDetail = self::find()
                ->alias('j')
                ->where(['j.id' => $id])
                ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->select([
                    'c.full_name as companyName',
                    'c.scale',
                    'c.industry_id as industryId',
                    'c.nature',
                    'c.type',
                    'c.member_id as companyMemberId',
                    'c.english_name as englishName',
                    'c.logo_url as logoUrl',
                    'c.is_cooperation as isCooperation',
                    'j.id as jobId',
                    'j.status',
                    'j.is_show',
                    'j.name as jobName',
                    'j.company_id as companyId',
                    'j.min_wage as minWage',
                    'j.max_wage as maxWage',
                    'j.wage_type as wageType',
                    'j.experience_type as experienceType',
                    'j.education_type as educationType',
                    'j.amount',
                    'j.job_category_id as jobCategoryId',
                    'j.welfare_tag as welfareTag',
                    'j.department',
                    'j.province_id as provinceId',
                    'j.city_id as cityId',
                    'j.district_id',
                    'j.address',
                    'j.nature_type as jobNatureType',
                    'j.major_id as majorId',
                    'j.title_type as titleType',
                    'j.age_type as age',
                    'j.political_type as politicalType',
                    'j.abroad_type as abroadType',
                    'j.duty',
                    'j.requirement',
                    'j.remark',
                    'j.gender_type',
                    'j.period_date as periodDate',
                    'j.apply_type as applyType',
                    'j.apply_address as applyAddress',
                    'j.announcement_id as announcementId',
                    'j.release_time',
                    'j.click',
                    'j.id',
                    'j.file_ids',
                    'j.delivery_way',
                    'j.delivery_type',
                    'j.refresh_date',
                    'j.refresh_time',
                    'j.is_establishment as isEstablishment',
                    'j.establishment_type as establishmentType',
                ])
                ->asArray()
                ->one();

            $jobDetail['jobName'] = str_replace(PHP_EOL, '', $jobDetail['jobName']);

            //格式化薪资
            $jobDetail['wage'] = self::formatWage($jobDetail['minWage'], $jobDetail['maxWage'], $jobDetail['wageType']);
            //格式化日期
            if ($jobDetail['periodDate'] == TimeHelper::ZERO_TIME) {
                // 这里还有一层逻辑,就是如果公告有过期时间,就拿公告的截止日期
                $announcementPeriodDate = BaseAnnouncement::find()
                    ->where(['id' => $jobDetail['announcementId']])
                    ->select(['period_date'])
                    ->asArray()
                    ->one();
                if ($announcementPeriodDate && $announcementPeriodDate['period_date'] != TimeHelper::ZERO_TIME) {
                    $jobDetail['periodDate'] = date('Y-m-d', strtotime($announcementPeriodDate['period_date']));
                } else {
                    $jobDetail['periodDate'] = '详见正文';
                }
            } else {
                $jobDetail['periodDate'] = date('Y-m-d', strtotime($jobDetail['periodDate']));
            }
            $welfareTagArr                  = BaseWelfareLabel::getWelfareLabelNameList($jobDetail['welfareTag']);
            $jobDetail['welfareTagArr']     = [];
            $jobDetail['moreWelfareTagArr'] = [];
            foreach ($welfareTagArr as $k => $v) {
                if ($k < 4) {
                    array_push($jobDetail['welfareTagArr'], $v);
                } else {
                    array_push($jobDetail['moreWelfareTagArr'], $v);
                }
            }
            $province = BaseArea::getAreaName($jobDetail['provinceId']);
            $city     = BaseArea::getAreaName($jobDetail['cityId']);
            if ($province == $city) {
                $area = $city;
            } else {
                $area = $province . $city;
            }
            $jobDetail['fullArea']    = $area . $jobDetail['address'];
            $jobDetail['area']        = $jobDetail['city'] = BaseArea::getAreaName($jobDetail['cityId']);
            $jobDetail['majorTxt']    = BaseMajor::getAllMajorName(explode(',', $jobDetail['majorId']));
            $jobDetail['major']       = BaseMajor::getAllMajorLinkList(explode(',', $jobDetail['majorId']));
            $jobDetail['experience']  = BaseDictionary::getExperienceName($jobDetail['experienceType']);
            $jobDetail['education']   = BaseDictionary::getEducationName($jobDetail['educationType']);
            $jobDetail['jobCategory'] = BaseCategoryJob::getName($jobDetail['jobCategoryId']);
            $jobDetail['jobNature']   = BaseDictionary::getNatureName($jobDetail['jobNatureType']);
            $jobDetail['title']       = BaseDictionary::getTitleName($jobDetail['titleType']);
            $jobDetail['political']   = BaseDictionary::getPoliticalStatusName($jobDetail['politicalType']);
            $jobDetail['abroad']      = BaseDictionary::getAbroadName($jobDetail['abroadType']);
            $jobDetail['scale']       = BaseDictionary::getCompanyScaleName($jobDetail['scale']);
            $jobDetail['industry']    = BaseTrade::getIndustryName($jobDetail['industryId']);
            $jobDetail['nature']      = BaseDictionary::getCompanyNatureName($jobDetail['nature']);
            $jobDetail['type']        = BaseDictionary::getCompanyTypeName($jobDetail['type']);
            $jobDetail['age']         = $jobDetail['age'] ?: '';
            $jobDetail['logo']        = BaseCompany::getLogoFullUrl($jobDetail['logoUrl']);
            //获取申请状态
            $jobDetail['applyStatus'] = self::JOB_APPLY_STATUS_NO;
            //获取单位在招职位数量
            $jobDetail['jobAmount'] = self::getCompanyJobAmount($jobDetail['companyId']);
            //获取申请类型字段
            $jobDetail['isEmailApply']  = "false";
            $jobDetail['isOnlineApply'] = false;
            $jobDetail['isOtherApply']  = false;
            if ($jobDetail['announcementId'] > 0) {
                $jobDetail['announcement_file_ids'] = BaseAnnouncement::findOneVal(['id' => $jobDetail['announcementId']],
                    'file_ids');
            }
            //保留多一个id，用于判断
            $jobDetail['isCooperationId'] = $jobDetail['isCooperation'];

            //判断是否合作单位
            if ($jobDetail['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                $jobDetail['isCooperation'] = 'true';
                //获取单位简历查看率
                $jobDetail['viewingRate'] = BaseJobApply::statCompanyViewingRate(['company_member_id' => $jobDetail['companyMemberId']]);
                //获取单位登录时间
                $jobDetail['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($jobDetail['companyMemberId']);
            } else {
                $jobDetail['isCooperation'] = 'false';
                $jobDetail['viewingRate']   = '-';
                $jobDetail['lastLoginTime'] = '-';
            }
            //            if (!empty($memberId)) {
            //                //获取简历信息
            //                $resume_info = BaseResume::findOne([
            //                    'member_id' => $memberId,
            //                    'status'    => BaseResume::STATUS_ACTIVE,
            //                ]);
            //                //获取用户对该职位投递情况
            //                $jobDetail['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resume_info->id,
            //                    $jobDetail['jobId']);
            //            }
            //反馈快
            $is_fast = 2;
            //置顶
            $is_top_bool = BaseJobTopConfig::getJobIsTop($jobDetail['jobId']);
            if ($is_top_bool) {
                $jobDetail['isTop'] = 1;
            } else {
                $jobDetail['isTop'] = 2;
            }

            if ($jobDetail['delivery_type']) {
                if ($jobDetail['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTER) {
                    $jobDetail['applyTypeText'] = BaseJob::getApplyTypeName($jobDetail['applyType']);
                } else {
                    $jobDetail['applyTypeText'] = '站内投递';
                }
                if ($jobDetail['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
                    $is_fast = 1;
                }
            } else {
                if ($jobDetail['announcementId'] > 0) {
                    $announcementInfo = BaseAnnouncement::findOne(['id' => $jobDetail['announcementId']]);
                    if ($announcementInfo['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                        $jobDetail['applyTypeText'] = BaseJob::getApplyTypeName($announcementInfo['apply_type']);
                    } else {
                        $jobDetail['applyTypeText'] = '站内投递';
                    }
                    if ($announcementInfo['delivery_way'] == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                        $is_fast = 1;
                    }
                }
            }
            $jobDetail['isFast'] = $is_fast;

            //获取收藏状态
            $jobDetail['collectStatus'] = self::JOB_COLLECT_STATUS_NO;
            //            if (!empty($memberId)) {
            //                $isCollect = BaseJobCollect::checkIsCollect($memberId, $jobDetail['jobId']);
            //                if ($isCollect) {
            //                    $jobDetail['collectStatus'] = self::JOB_COLLECT_STATUS_YES;
            //                }
            //                //获取用户邮箱
            //                $jobDetail['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');
            //            }
            $jobDetail['genderType'] = BaseResume::getGenderName($jobDetail['gender_type']); //性别
            $jobDetail['companyUrl'] = UrlHelper::createCompanyDetailPath($jobDetail['companyId']);

            $jobDetail['jobWorkUrl'] = UrlHelper::createUrlParamsPath('/job', ['keyword' => $jobDetail['department']]);

            $jobDetail['announcementTitle']  = BaseAnnouncement::findOneVal(['id' => $jobDetail['announcementId']],
                'title');
            $jobDetail['announcementUrl']    = UrlHelper::createAnnouncementDetailPath($jobDetail['announcementId']);
            $jobDetail['announcementJobUrl'] = UrlHelper::createPcAnnouncementJobListPath($jobDetail['announcementId']);

            //获取栏目信息
            $jobDetail['columnInfo'] = BaseHomeColumn::getInfoListByAnnouncementId($jobDetail['announcementId']);

            $releaseTime  = strtotime($jobDetail['release_time']);
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            if ($releaseTime > $thisYearTime) {
                $jobDetail['releaseTime'] = date('m-d', $releaseTime);
            } else {
                $jobDetail['releaseTime'] = date('Y-m-d', $releaseTime);
            }

            // 换行前端显示
            $jobDetail['requirement'] = str_replace("\n", '<br>', $jobDetail['requirement']);
            $jobDetail['remark']      = str_replace("\n", '<br>', $jobDetail['remark']);
            $jobDetail['duty']        = str_replace("\n", '<br>', $jobDetail['duty']);

            // 编制相关
            // 编制相关文案拼接
            if ($jobDetail['isEstablishment'] == self::IS_ESTABLISHMENT_YES) {
                $jobDetail['establishmentTxt'] = self::getEstablishmentName($jobDetail['establishmentType']);
            }
            // 1.6直聊版本添加联系人信息
            $jobDetail['contactInfo'] = BaseJobContact::getInfoByJob($id);

            Cache::set($cacheKey, json_encode($jobDetail), self::DETAIL_CACHE_TIME);
        }

        // 由于需求变动，这里就没法用缓存的方法来保存了
        $jobDetail['jobTypeUrl'] = self::handelJobTypeUrlForDetail($jobDetail['jobCategoryId']);

        $jobDetail['chatInfo'] = [
            'buttonInfo' => CommonRule::checkButtonAuth($id, $memberId),
            'activeInfo' => CommonRule::checkChatActive($id),
        ];

        return $jobDetail;
    }

    /**
     * @param $code
     * 这个方法是专门给编制在前端展示的,和求职者那边隔离开来,因为两边的文案要求可能是不一致的
     */
    public static function getEstablishmentName($code)
    {
        // 首先转成数组,本来是逗号隔开的
        $codeArr = explode(',', $code);
        // 循环去字典表里面取
        $establishmentNameArr = [];
        foreach ($codeArr as $item) {
            $establishmentNameArr[] = BaseDictionary::getEstablishmentName($item);
        }

        // 最后/隔开
        return implode('/', $establishmentNameArr);
    }

    /**
     * todo：临时数据，后续完善
     * 获取推荐职位列表
     * @param array $searchData
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getRecommendList($searchData = [])
    {
        return [];
        // $query    = self::find()
        //     ->alias('j')
        //     ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
        //     ->where(['j.status' => self::STATUS_ACTIVE])
        //     ->select([
        //         'j.id as jobId',
        //         'j.name',
        //         'j.province_id',
        //         'j.city_id',
        //         'j.wage_type',
        //         'j.min_wage',
        //         'j.max_wage',
        //         'j.release_time',
        //         'j.education_type',
        //         'c.full_name as companyName',
        //         'c.id as companyId',
        //         'c.nature',
        //         'c.type',
        //         'c.is_cooperation as isCooperation',
        //         'j.apply_type as applyType',
        //         'j.experience_type',
        //         'j.announcement_id',
        //         'j.period_date',
        //         'j.amount',
        //     ]);
        // $count    = $query->count();
        // $pageSize = $searchData['pageSize'] ?: Yii::$app->params['recommendJobCount'];
        //
        // $pages = self::setPage($count, $searchData['page'], $pageSize);
        //
        // $list = $query->offset($pages['offset'])
        //     ->limit($pages['limit'])
        //     ->asArray()
        //     ->all();
        // foreach ($list as $k => &$record) {
        //     $record['isEmailApply'] = "false";
        //     //判断是否合作单位
        //     if ($record['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
        //         $record['isCooperation'] = 'true';
        //     } else {
        //         $record['isCooperation'] = 'false';
        //         //判断职位投递方式是否是邮箱投递
        //         $applyTypeArr = explode(',', $record['applyType']);
        //
        //         if (in_array(self::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
        //             $record['isEmailApply'] = "true";
        //         }
        //     }
        //
        //     //拼接工作地点
        //     $province             = BaseArea::getAreaName($record['province_id']);
        //     $city                 = BaseArea::getAreaName($record['city_id']);
        //     $record['areaName']   = $province.'-'.$city;
        //     $record['city']       = BaseArea::getAreaName($record['city_id']);
        //     $record['periodDate'] = date('Y-m-d', strtotime($record['periodDate']));//职位有效期
        //     //获取公告名称
        //     $record['announcementName'] = BaseAnnouncement::findOneVal(['id' => $record['announcement_id']], 'title');
        //     //拼接月薪
        //     $record['wage'] = self::formatWage($record['min_wage'], $record['max_wage'], $record['wage_type']);
        //     //获取学历
        //     $record['education'] = BaseDictionary::getEducationName($record['education_type']);
        //     //获取经验要求
        //     $record['experience'] = BaseDictionary::getExperienceName($record['experience_type']);
        //     $record['nature']     = BaseDictionary::getCompanyNatureName($record['nature']);
        //     $record['type']       = BaseDictionary::getCompanyTypeName($record['type']);
        //     if ($record['major_id']) {
        //         $majorIds        = explode(',', $record['major_id']);
        //         $record['major'] = BaseMajor::getAllMajorName($majorIds);
        //
        //         if (strlen($record['major_id']) < 1) {
        //             $record['major'] = "专业不限";
        //         }
        //     }
        //     //处理发布时间
        //     $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
        //     $releaseTime  = strtotime($record['release_time']);
        //     if ($releaseTime > $thisYearTime) {
        //         $record['releaseTime'] = date('m-d', $releaseTime);
        //     } else {
        //         $record['releaseTime'] = date('Y-m-d', $releaseTime);
        //     }
        //     $record['url']        = Url::toRoute([
        //         'job/detail',
        //         'id' => $record['jobId'],
        //     ]);
        //     $record['companyUrl'] = Url::toRoute([
        //         'company/detail',
        //         'id' => $record['companyId'],
        //     ]);
        // }

        return $list;
    }

    /**
     * 格式化薪资范围
     * @param $minWage
     * @param $maxWage
     * @param $wageType
     * @return string
     */
    public static function formatWage($minWage, $maxWage, $wageType)
    {
        // 如果最大和最小的薪资都是0,其实就是面议
        if ($minWage == 0 && $maxWage == 0) {
            return '面议';
        }

        if ($wageType == self::TYPE_WAGE_DAY) {
            //日薪
            $unit = '元';
            $type = '日';
        } elseif ($wageType == self::TYPE_WAGE_MONTH) {
            //月薪
            if ($maxWage >= 10000) {
                //如果薪资最大值大于等于万元，单位取万
                $minWage = $minWage / 10000;
                $maxWage = $maxWage / 10000;
                $unit    = '万';
                $type    = '月';
            } else {
                //如果薪资最大值小于万元，单位取千
                $minWage = $minWage / 1000;
                $maxWage = $maxWage / 1000;
                $unit    = '千';
                $type    = '月';
            }
        } elseif ($wageType == self::TYPE_WAGE_YEARS) {
            //年薪
            $minWage = $minWage / 10000;
            $maxWage = $maxWage / 10000;
            $unit    = '万';
            $type    = '年';
        } elseif ($wageType == self::TYPE_WAGE_NEGOTIABLE) {
            //如果是面议，判断是否都为0
            if ($minWage == 0 && $maxWage == 0) {
                return '面议';
            } else {
                //如果不是，不满足条件，为空
                return '';
            }
        } else {
            return '';
        }
        if ($minWage == $maxWage) {
            return $maxWage . $unit . '/' . $type;
        } else {
            return $minWage . '-' . $maxWage . $unit . '/' . $type;
        }
    }

    /**
     * 获取单位在招职位
     * @param $companyId
     * @return bool|int|string|null
     */
    public static function getCompanyJobAmount($companyId)
    {
        return self::find()
            ->where([
                'company_id' => $companyId,
                'status'     => self::STATUS_ONLINE,
                'is_show'    => self::IS_SHOW_YES,
            ])
            ->count();
    }

    /**
     * 获取多个单位在招职位数量
     * @param array|string $companyId
     * @return array
     */
    public static function getCompanysJobAmount($companyIds)
    {
        if (is_string($companyIds)) {
            $companyIds = explode(',', $companyIds);
        }
        $result = self::find()
            ->select([
                'count(*) AS job_count',
                'company_id',
            ])
            ->from('job')
            ->where([
                'in',
                'company_id',
                $companyIds,
            ])
            ->groupBy('company_id')
            ->asArray()
            ->all();

        return $result ? array_column($result, 'job_count', 'company_id') : [];
    }

    /**
     * 获取公告下职位数量
     * @param $announcementId
     * @param $type
     * @return bool|int|string|null
     */
    public static function getAnnouncementJobAmount($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_RECRUIT_STAGING) {
            $where = [
                'status' => self::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_RECRUIT_ONLINE) {
            $where = [
                'status' => [
                    self::STATUS_ONLINE,
                ],
            ];
        } else {
            $where = [
                'status' => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ];
        }

        return self::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->count();
    }

    /**
     * 公告下的职位列表
     * @param        $announcementId
     * @param string $select
     * @return array|yii\db\ActiveRecord[]
     */
    public static function getAnnouncementJobList($announcementId, $select = '*')
    {
        return self::find()
            ->select($select)
            ->where([
                'announcement_id' => $announcementId,
            ])
            ->andWhere([
                '<>',
                'status',
                self::STATUS_DELETE,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取公告下不同状态的职位数量
     * @param $announcementId
     * @param $type
     * @return bool|int|string|null
     */
    public static function getAnnouncementJobStatusAmount($announcementId, $type = 0)
    {
        if ($type == self::STATUS_ONLINE) {
            $where = [
                'status' => self::STATUS_ONLINE,
            ];
        } elseif ($type == self::STATUS_OFFLINE) {
            $where = [
                'status' => self::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => self::STATUS_WAIT,
            ];
        }

        return self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->count();
    }

    /**
     * 获取公告下职位招聘人数总量
     * @param $announcementId
     * @param $type
     * @return bool|int|mixed|string|null
     */
    public static function getAnnouncementJobRecruitAmount($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_RECRUIT_STAGING) {
            $where = [
                'status' => self::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_RECRUIT_OFFLINE) {
            $where = [
                'status' => self::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    self::STATUS_OFFLINE,
                    self::STATUS_ONLINE,
                ],
            ];
        }

        $hasSpcialInfo = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->andWhere(['amount' => '若干'])
            ->select('id')
            ->asArray()
            ->one();
        if (!empty($hasSpcialInfo['id'])) {
            return '若干';
        } else {
            return self::find()
                ->where([
                    'announcement_id' => $announcementId,
                    'is_show'         => self::IS_SHOW_YES,
                ])
                ->andWhere($where)
                ->sum('amount');
        }
    }

    /**
     * 获取公告下职位招聘人数总量(公告列表使用)
     * @param $announcementId
     * @param $type
     * @return bool|int|mixed|string|null
     */
    public static function getAnnouncementJobListAmount($announcementId, $type)
    {
        if ($type == self::STATUS_WAIT) {
            $where = [
                'status' => self::STATUS_WAIT,
            ];
        } else {
            $where = [
                'status' => [
                    self::STATUS_OFFLINE,
                    self::STATUS_ONLINE,
                ],
            ];
        }

        $hasSpcialInfo = self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->andWhere(['amount' => '若干'])
            ->select('id')
            ->asArray()
            ->one();
        if (!empty($hasSpcialInfo['id'])) {
            return '若干';
        } else {
            return self::find()
                ->where(['announcement_id' => $announcementId])
                ->andWhere($where)
                ->sum('amount');
        }
    }

    /**
     * 获取公告下的职位的地区名称
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAnnouncementJobArea($announcementId, $type = 0)
    {
        if ($type == self::STATUS_WAIT) {
            $where = [
                'status' => [
                    self::STATUS_ACTIVE,
                    self::STATUS_WAIT,
                ],
            ];
        } else {
            $where = [
                'status' => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ];
        }

        $jobList = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select([
                'city_id',
                'province_id',
            ])
            ->orderBy('id desc')
            ->limit(BaseAnnouncement::SHOW_JOB_AREA_NAME_NUM)
            ->all();
        if (!empty($jobList)) {
            if (count($jobList) == 1) {
                $provinceName = BaseArea::getAreaName($jobList[0]['province_id']);
                $cityName     = BaseArea::getAreaName($jobList[0]['city_id']);
                if ($provinceName == $cityName) {
                    $areaName = $cityName;
                } else {
                    $areaName = $provinceName . '-' . $cityName;
                }
            } else {
                $areaName = '';
                $cityArr  = [];
                foreach ($jobList as $k => $job) {
                    $cityArr[] = $job['city_id'];
                }
                $cityArr = array_unique($cityArr);
                foreach ($cityArr as $city) {
                    $areaName .= BaseArea::getAreaName($city) . ' ';
                }

                $areaName = substr($areaName, 0, -1);
            }

            return $areaName;
        } else {
            return '';
        }
    }

    /**
     * 获取公告下职位的投递名称
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getJobSignUpName($announcementId, $type = 0)
    {
        if ($type == BaseAnnouncement::STATUS_RECRUIT_STAGING) {
            $where = [
                'status' => BaseJob::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_RECRUIT_OFFLINE) {
            $where = [
                'status' => BaseJob::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => BaseJob::STATUS_ACTIVE,
            ];
        }

        $jobList = self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->select([
                'apply_type',
            ])
            ->orderBy('id desc')
            ->limit(BaseAnnouncement::SHOW_JOB_AREA_NAME_NUM)
            ->all();
        if (!empty($jobList)) {
            if (count($jobList) == 1) {
                $signUpName = BaseDictionary::getSignUpName($jobList[0]['apply_type']);
            } else {
                $signUpNameArr = [];
                foreach ($jobList as $k => $job) {
                    $signUpNameArr[] = BaseDictionary::getSignUpName($job['apply_type']);
                }

                $signUpName = implode(' ', array_unique($signUpNameArr));
            }

            return $signUpName;
        } else {
            return '';
        }
    }

    /**
     * 申请职位
     * @throws \Exception
     */
    public static function apply($params)
    {
        //新增投递记录
        $jobInfo = self::findOne($params['job_id']);
        if (empty($jobInfo)) {
            throw new Exception('职位信息不存在');
        }
        $companyMemberId = BaseCompany::findOneVal(['id' => $jobInfo->company_id], 'member_id');
        //获取用户名
        $resumeName                                      = BaseResume::findOneVal(['id' => $params['resume_id']],
            'name');
        $jobApplyModel                                   = new BaseJobApply();
        $jobApplyModel->status                           = BaseJobApply::STATUS_HANDLE_WAIT;
        $jobApplyModel->company_id                       = $jobInfo->company_id;
        $jobApplyModel->resume_id                        = $params['resume_id'];
        $jobApplyModel->resume_member_id                 = $params['member_id'];
        $jobApplyModel->company_member_id                = $companyMemberId;
        $jobApplyModel->resume_name                      = $resumeName;
        $jobApplyModel->job_id                           = $params['job_id'];
        $jobApplyModel->job_name                         = $jobInfo->name;
        $jobApplyModel->source                           = BaseJobApply::SOURCE_NO;
        $jobApplyModel->stuff_file_id                    = $params['stuff_file_id'];
        $jobApplyModel->resume_attachment_id             = $params['resume_attachment_id'];
        $jobApplyModel->is_resume_operation_remind_check = BaseJobApply::IS_RESUME_OPERATION_REMIND_CHECK_YES;
        $jobApplyModel->is_resume_check_remind_check     = BaseJobApply::IS_RESUME_CHECK_REMIND_CHECK_YES;
        $jobApplyModel->equity_status                    = $params['equity_status'] ?? BaseJobApply::EQUITY_STATUS_EXPIRE;
        if (!$jobApplyModel->save()) {
            throw new Exception($jobApplyModel->getFirstErrorsMessage());
        }
        //在这里调用一下匹配度算法进行计算匹配度
        $service        = new MatchCompleteService();
        $init           = [
            'job_id'    => $params['job_id'],
            'resume_id' => $params['resume_id'],
        ];
        $service_result = $service->setRuleKey()
            ->setProject(MatchCompleteService::PROJECT_TYPE_2)
            ->setOparetion($params['operation_platform'])
            ->init($init)
            ->run();
        //新增投递总表记录
        $apply_model                  = new BaseJobApplyRecord();
        $apply_model->delivery_type   = BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE;
        $apply_model->delivery_way    = $params['delivery_way'];
        $apply_model->add_time        = CUR_DATETIME;
        $apply_model->update_time     = CUR_DATETIME;
        $apply_model->apply_id        = $jobApplyModel->id;
        $apply_model->company_id      = $jobInfo->company_id;
        $apply_model->resume_id       = $params['resume_id'];
        $apply_model->job_id          = $params['job_id'];
        $apply_model->announcement_id = $jobInfo->announcement_id;
        $apply_model->source          = BaseJobApplyRecord::SOURCE_AUTO;
        $apply_model->platform        = $params['platform'];
        $apply_model->match_complete  = $service_result['rule_id'] ?? 0;
        $apply_model->save();
        if (!$apply_model->save()) {
            throw new Exception($apply_model->getFirstErrorsMessage());
        }
        //新增投递操作记录
        $applyHandleModel               = new BaseJobApplyHandleLog();
        $applyHandleModel->job_apply_id = $jobApplyModel->id;
        $applyHandleModel->handler_type = BaseJobApplyHandleLog::HANDLER_TYPE_PERSON;
        $applyHandleModel->handler_name = $resumeName;
        $applyHandleModel->handle_type  = BaseJobApplyHandleLog::TYPE_DELIVERY;
        $applyHandleModel->handle_id    = Yii::$app->user->id;
        //新增简历投递次数
        BaseResume::OnSiteApplyAdd($params['resume_id'], $params['resume_attachment_id']);

        if (!$applyHandleModel->save()) {
            throw new Exception($applyHandleModel->getFirstErrorsMessage());
        }

        //发送应聘消息
        //BaseMemberMessage::send($companyMemberId, BaseMemberMessage::TYPE_COMPANY_APPLY, '求职应聘',
        //    '您发布的职位“'.$jobInfo->name.'”收到一份简历投递，请您尽快对简历进行处理。');
        // 发送邮件通知,企业联系人
        $companyContact = BaseCompanyContact::findOne(['company_id' => $jobInfo->company_id]);
        $companyName    = BaseCompany::findOneVal(['id' => $jobInfo->company_id], 'full_name');

        // 判断其他方式投递为电子邮件
        $applyTypeArr    = explode(',', $jobInfo['apply_type']);
        $applyAddressArr = explode(',', $jobInfo['apply_address']);

        $cityName = BaseArea::findOneVal(['id' => $jobInfo->city_id], 'name');

        $educationName = BaseResume::getEducation($params['resume_id']);

        $data = [
            'resumeId'      => $params['resume_id'],
            'name'          => $companyName,
            'resumeName'    => $resumeName,
            'jobId'         => $params['job_id'],
            'jobName'       => $jobApplyModel->job_name,
            'department'    => $jobInfo->department,
            'cityName'      => $cityName,
            'educationName' => $educationName,
            'jobApplyId'    => $jobApplyModel->id,
        ];
        if ($params['resume_attachment_id']) {
            $data['resumeAttachmentId'] = $params['resume_attachment_id'];
        }

        if ($params['stuff_file_id']) {
            $data['stuffFileId'] = $params['stuff_file_id'];
        }
        //记录到单位简历库
        $sendData = [
            'resumeId'  => $params['resume_id'],
            'companyId' => $jobInfo->company_id,
        ];
        BaseCompanyResumeLibrary::saveApplyRecord($sendData);
        //判断投递职位属于什么类型
        $delivery_way         = 0;
        $extra_notify_address = '';
        $apply_address        = '';
        if ($jobInfo->delivery_way > 0) {
            $delivery_way         = $jobInfo->delivery_way;
            $extra_notify_address = $jobInfo->extra_notify_address;
            $apply_address        = $jobInfo->apply_address;
        } else {
            if ($jobInfo->announcement_id > 0) {
                $announcementInfo     = BaseAnnouncement::findOne($jobInfo->announcement_id);
                $delivery_way         = $announcementInfo->delivery_way;
                $extra_notify_address = $announcementInfo->extra_notify_address;
                $apply_address        = $announcementInfo->apply_address;
            }
        }
        if ($delivery_way == BaseJob::DELIVERY_WAY_PLATFORM) {
            if ($extra_notify_address) {
                $extra_notify_address_arr = explode(',', $extra_notify_address);
                foreach ($extra_notify_address_arr as $item_email) {
                    Producer::email($item_email, BaseMember::TYPE_COMPANY, EmailQueue::EMAIL_PLATFORM_DELIVERY, $data);
                }
            } else {
                if ($companyContact->email) {
                    //发到单位配置邮箱$companyContact->email
                    Producer::email($companyContact->email, BaseMember::TYPE_COMPANY,
                        EmailQueue::EMAIL_PLATFORM_DELIVERY, $data);
                }
            }
        } elseif ($delivery_way == BaseJob::DELIVERY_WAY_EMAIL) {
            if ($apply_address) {
                $apply_address_arr = explode(',', $apply_address);
                foreach ($apply_address_arr as $item_email) {
                    Producer::email($item_email, BaseMember::TYPE_COMPANY, EmailQueue::EMAIL_POST_DELIVERY, $data);
                }
                //抄送到求职者邮箱
                $user_email = BaseMember::findOneVal(['id' => $params['member_id']], 'email');
                if ($user_email) {
                    Producer::email($user_email, BaseMember::TYPE_PERSON, EmailQueue::EMAIL_SEEKER_DELIVERY, $data);
                }
            }
        }
        //        if (!empty($email)) {
        //            Producer::email($email, 2, EmailQueue::EMAIL_TYPE_JOB_APPLY, $data);
        //        }
        //推送消息到对应的单位
        try {
            (new MessageCenterApplication())->sideDeliveryNotice($jobApplyModel->id);
        } catch (\Exception $e) {
        }

        // 更新邀约投递相关记录
        BaseJobApplyRecord::updateInvite($apply_model->id);

        return $jobApplyModel->id;
    }

    public static function getDetailUrl($id)
    {
        return Url::toRoute([
            '/job/detail',
            'id' => $id,
        ]);
    }

    /**
     * 获取职位名称列表
     * @param $keywords
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getJobNameList($keywords)
    {
        $select = [
            'id',
            'name',
        ];

        $query = self::find()
            ->select($select)
            ->where([
                '<>',
                'status',
                self::STATUS_DELETE,
            ])
            ->andWhere(['company_id' => $keywords['companyId']]);

        if ($keywords['nameNumber']) {
            JobApply::uidJudgeWhere($keywords['nameNumber'], 'id', 'name', $query);
        }

        $pageSize = \Yii::$app->params['default_page_size'];

        $data = $query->orderBy('add_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        return $data;
    }

    /**
     * 获取公告职位编辑数据
     * @throws Exception
     * @throws \Exception
     */
    public static function getJobEditData($id)
    {
        $jobWhere   = ['id' => $id];
        $jobSelect  = [
            'id as jobTemplateId',
            'status',
            'member_id',
            'company_id',
            'is_article',
            'name',
            'period_date',
            'is_stick',
            'code',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'is_show',
            'apply_type',
            'apply_address',
            'delivery_limit_type',
            'delivery_type',
            'delivery_way',
            'extra_notify_address',
        ];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);
        //投递方式处理
        if (in_array($jobDetails['delivery_way'], BaseJob::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $jobDetails['delivery_way'] = (string)BaseJob::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($jobDetails['delivery_way'])) {
            $jobDetails['delivery_way'] = '';
        }
        if (empty($jobDetails['delivery_type'])) {
            $jobDetails['delivery_type'] = '';
        }
        if ($jobDetails['experience_type'] == '0') {
            $jobDetails['experience_type'] = '';
        }
        if ($jobDetails['political_type'] == '0') {
            $jobDetails['political_type'] = '';
        }
        if ($jobDetails['abroad_type'] == '0') {
            $jobDetails['abroad_type'] = '';
        }
        if ($jobDetails['nature_type'] == '0') {
            $jobDetails['nature_type'] = '';
        }
        if ($jobDetails['wage_type'] == '0') {
            $jobDetails['wage_type'] = '';
        }
        if ($jobDetails['age_type'] == '0') {
            $jobDetails['age_type'] = '';
        }
        if ($jobDetails['title_type'] == '0') {
            $jobDetails['title_type'] = '';
        }
        if ($jobDetails['min_wage'] == '0') {
            $jobDetails['min_wage'] = '';
        }
        if ($jobDetails['max_wage'] == '0') {
            $jobDetails['max_wage'] = '';
        }
        if ($jobDetails['min_age'] == '0') {
            $jobDetails['min_age'] = '';
        }
        if ($jobDetails['max_age'] == '0') {
            $jobDetails['max_age'] = '';
        }
        if ($jobDetails['district_id'] == '0') {
            $jobDetails['district_id'] = '';
        }
        if ($jobDetails['amount'] == '-1') {
            $jobDetails['amount'] = '若干';
        }

        $jobDetails['majorTitle'] = '';
        if ($jobDetails['major_id']) {
            //需求专业
            $jobDetails['major_id'] = explode(',', $jobDetails['major_id']);
            if (count($jobDetails['major_id']) > 1) {
                foreach ($jobDetails['major_id'] as $v) {
                    $jobDetails['majorTitle'] .= BaseMajor::getAllMajorName($v) . ',';
                }
                $jobDetails['majorTitle'] = substr($jobDetails['majorTitle'], '0', '-1');
            } else {
                $jobDetails['majorTitle'] = BaseMajor::getAllMajorName($jobDetails['major_id']);
            }
        } else {
            $jobDetails['major_id'] = [];
        }

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        $jobDetails['company_area_name'] = $areaCache[$jobDetails['province_id']]['name'] . $areaCache[$jobDetails['city_id']]['name'];
        $jobDetails['area_name']         = $areaCache[$jobDetails['province_id']]['id'] . ',' . $areaCache[$jobDetails['city_id']]['id'];
        $jobDetails['area_name']         = explode(',', $jobDetails['area_name']);
        //薪资code回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }

        //查询福利标签
        $welfareLabelWhere  = ['id' => explode(',', $jobDetails['welfare_tag'])];
        $welfareLabelSelect = [
            'id',
            'name',
        ];

        $welfareLabelList          = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        $welfareTag                = [];
        $jobDetails['welfareTage'] = [];
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $welfareTag[$k]['k']       = $welfareLabel['id'];
            $welfareTag[$k]['v']       = $welfareLabel['name'];
            $jobDetails['welfareTage'] = $welfareTag;
        }

        $contact                               = BaseJob::getJobContact($id);
        $jobDetails['job_contact_id']          = $contact['company_member_info_id'] ?: '';
        $contact_synergy                       = BaseJob::getJobContactSynergy($id);
        $jobDetails['job_contact_synergy']     = $contact_synergy;
        $jobDetails['job_contact_synergy_ids'] = count($contact_synergy) > 0 ? array_column($contact_synergy,
            'company_member_info_id') : [];

        return $jobDetails;
    }

    /**
     * 获取职位编辑数据
     * @throws Exception
     * @throws \Exception
     */
    public static function getEdit($data)
    {
        if (!$data['id']) {
            throw new Exception('职位Id不能为空');
        }

        $statusData = BaseAnnouncement::getAuditStatus($data['id']);
        // 操作职位编辑前置条件
        if ($statusData['jobAuditStatus'] == BaseJob::STATUS_WAIT_AUDIT || $statusData['jobStatus'] == BaseJob::STATUS_OFFLINE || $statusData['articleStatus'] == BaseArticle::STATUS_OFFLINE || $statusData['announcementAuditStatus'] == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('待审核或下线状态不支持操作编辑');
        }

        $jobSelect  = [
            'id',
            'status',
            'is_article',
            'name',
            'period_date',
            'company_id',
            'is_stick',
            'code',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'is_show',
            'address',
            'announcement_id',
            'apply_type',
            'apply_address',
            'first_release_time',
            'delivery_limit_type',
            'delivery_type',
            'delivery_way',
            'extra_notify_address',
            'establishment_type',
        ];
        $jobWhere   = ['id' => $data['id']];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);
        $jobDetails = self::transformation($jobDetails);

        // 如果是审核拒绝，回显拒绝的数据
        if ($statusData['jobAuditStatus'] == self::STATUS_REFUSE_AUDIT) {
            $editData = BaseJobEdit::find()
                ->select('edit_content')
                ->where(['job_id' => $data['id']])
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $editContent = json_decode($editData['edit_content'], true);
            $editKey     = array_keys($editContent);
            if (in_array('duty', $editKey)) {
                $jobDetails['duty'] = $editContent['duty'];
            }
            if (in_array('requirement', $editKey)) {
                $jobDetails['requirement'] = $editContent['requirement'];
            }
            if (in_array('remark', $editKey)) {
                $jobDetails['remark'] = $editContent['remark'];
            }
        }
        //投递方式处理
        if (in_array($jobDetails['delivery_way'], BaseJob::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $jobDetails['delivery_way'] = (string)BaseJob::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($jobDetails['delivery_way'])) {
            $jobDetails['delivery_way'] = '';
        }

        if ($jobDetails['experience_type'] == '0') {
            $jobDetails['experience_type'] = '';
        }
        if ($jobDetails['political_type'] == '0') {
            $jobDetails['political_type'] = '';
        }
        if ($jobDetails['abroad_type'] == '0') {
            $jobDetails['abroad_type'] = '';
        }

        if ($jobDetails['nature_type'] == '0') {
            $jobDetails['nature_type'] = '';
        }
        if ($jobDetails['wage_type'] == '0') {
            $jobDetails['wage_type'] = '';
        }

        if ($jobDetails['age_type'] == '0') {
            $jobDetails['age_type'] = '';
        }
        if ($jobDetails['title_type'] == '0') {
            $jobDetails['title_type'] = '';
        }
        if ($jobDetails['min_wage'] == '0') {
            $jobDetails['min_wage'] = '';
        }
        if ($jobDetails['max_wage'] == '0') {
            $jobDetails['max_wage'] = '';
        }
        if ($jobDetails['min_age'] == '0') {
            $jobDetails['min_age'] = '';
        }
        if ($jobDetails['max_age'] == '0') {
            $jobDetails['max_age'] = '';
        }
        if ($jobDetails['district_id'] == '0') {
            $jobDetails['district_id'] = '';
        }
        if ($jobDetails['period_date'] == TimeHelper::ZERO_TIME) {
            $jobDetails['period_date'] = '';
        }
        if (empty($jobDetails['delivery_type'])) {
            $jobDetails['delivery_type'] = '';
        }
        if ($jobDetails['amount'] == '-1') {
            $jobDetails['amount'] = '若干';
        }

        //薪资wage_id回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }
        if (!$jobDetails['min_wage'] && !$jobDetails['max_wage']) {
            $jobDetails['wage'] = '面议';
        } else {
            $jobDetails['wage'] = BaseJob::formatWage($jobDetails['min_wage'], $jobDetails['max_wage'],
                $jobDetails['wage_type']) ?: '-';
        }

        //查询福利标签
        $welfareLabelWhere         = ['id' => explode(',', $jobDetails['welfare_tag']),];
        $welfareLabelSelect        = [
            'id',
            'name',
        ];
        $welfareLabelList          = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        $welfareTag                = [];
        $jobDetails['welfareTage'] = [];
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $welfareTag[$k]['k']       = $welfareLabel['id'];
            $welfareTag[$k]['v']       = $welfareLabel['name'];
            $jobDetails['welfareTage'] = $welfareTag;
        }

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        $jobDetails['company_area_name'] = $areaCache[$jobDetails['province_id']]['name'] . $areaCache[$jobDetails['city_id']]['name'];
        $jobDetails['area_name']         = $areaCache[$jobDetails['province_id']]['id'] . ',' . $areaCache[$jobDetails['city_id']]['id'];
        $jobDetails['area_name']         = explode(',', $jobDetails['area_name']);
        $jobDetails['is_temp']           = BaseJobTemp::IS_TEMP_NO;
        //职位联系人
        $contact                      = BaseJob::getJobContact($jobDetails['id']);
        $jobDetails['job_contact']    = $contact;
        $jobDetails['job_contact_id'] = $contact['company_member_info_id'];
        //职位协同账号
        $contact_synergy                       = BaseJob::getJobContactSynergy($jobDetails['id']);
        $jobDetails['job_contact_synergy']     = $contact_synergy;
        $jobDetails['job_contact_synergy_num'] = count($contact_synergy);
        $jobDetails['job_contact_synergy_ids'] = array_column($contact_synergy, 'company_member_info_id');

        unset($jobDetails['announcementTitle'], $jobDetails['cityTitle'], $jobDetails['companyFullName'], $jobDetails['districtId']);
        unset($jobDetails['educationTypeTitle'], $jobDetails['isStick']);
        unset($jobDetails['natureTypeTitle'], $jobDetails['provinceTitle'], $jobDetails['provinceId'], $jobDetails['statusTitle']);

        return $jobDetails;
    }

    /**
     * 职位管理职位列表统计信息
     * 投递总数、面试总数、点击总数
     * @return array
     * @throws \Exception
     */
    public static function getJobListStatistics($keywords): array
    {
        $select = [
            'j.id',
            'j.click',
            'a.is_invitation',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['a' => JobApply::tableName()], 'j.id = a.job_id')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->select($select);

        $query->andFilterCompare('j.status', [
            self::STATUS_ONLINE,
            self::STATUS_OFFLINE,
        ], 'in');

        if ($keywords['announcement']) {
            $keywords['announcement_title_num'] = $keywords['announcement'];
        }

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);
        $query->andFilterCompare('c.is_cooperation', BaseCompany::COOPERATIVE_UNIT_YES);
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('concat(j.city_id,j.address)', $keywords['city'], 'like');
        //筛选投递类型
        if ($keywords['delivery_type']) {
            $query->andWhere([
                'or',
                ['j.delivery_type' => $keywords['delivery_type']],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['delivery_type']],
                ],
            ]);
        }
        //筛选投递方式
        if ($keywords['delivery_way']) {
            $query->andWhere([
                    'or',
                    ['j.delivery_way' => $keywords['delivery_way']],
                    [
                        'and',
                        ['an.delivery_way' => $keywords['delivery_way']],
                        ['j.delivery_way' => 0],
                    ],
                ]

            );
        }
        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andFilterCompare('j.education_type', $educationType, 'in');
        }

        if ($keywords['experience_type']) {
            $experience = explode(',', $keywords['experience_type']);
            $query->andFilterCompare('j.experience_type', $experience, 'in');
        }

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['company']) {
            $findIds = BaseCompany::find()
                ->select('id')
                ->where([
                    'like',
                    'concat(full_name,id)',
                    $keywords['company'],
                ])
                ->asArray()
                ->all();

            $companyIds = [];
            foreach ($findIds as $id) {
                $companyIds[] = $id['id'];
            }
            $query->andFilterCompare('j.company_id', $companyIds, 'in');
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }
        $jobTotal        = $query->asArray()
            ->all();
        $allClick        = 0;
        $jobInterviewNum = 0;
        $ids             = [];
        foreach ($jobTotal as $item) {
            $allClick += $item['click'];
            if ($item['is_invitation'] > 0) {
                $jobInterviewNum += $item['is_invitation'];
            }
            array_push($ids, $item['id']);
        }

        //统计所有投递数据数据
        $applyTotal = BaseJobApplyRecord::find()
            ->select([
                'sum(If(delivery_way=1,1,0)) as platformNum',
                'sum(If(delivery_way=2,1,0)) as emailNum',
                'sum(If(delivery_way=3,1,0)) as linkNum',
            ])
            ->andWhere([
                'in',
                'job_id',
                $ids,
            ])
            ->andWhere([
                '>',
                'job_id',
                0,
            ])
            ->asArray()
            ->one();

        return [
            'allClick'        => $allClick,
            'jobInterviewNum' => $jobInterviewNum,
            'applyTotal'      => $applyTotal,
        ];
    }

    /**
     * 合作单位公告下的职位统计信息
     * 投递总数、面试总数、点击总数
     * @return array
     */
    public static function getAnnouncementJobListStatistics(): array
    {
        $select = [
            'SUM(j.click) as allClick',
            'COUNT(a.job_id) as jobApplyNum',
            'COUNT(CASE WHEN a.is_invitation>0 THEN 1 ElSE null END) as jobInterviewNum',
        ];

        $query = self::find()
            ->alias('j')
            ->innerJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->leftJoin(['a' => JobApply::tableName()], 'j.id = a.job_id')
            ->select($select)
            ->where([
                'in',
                'j.status',
                [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ])
            ->andWhere([
                '<>',
                'j.announcement_id',
                0,
            ])
            ->andWhere([
                'j.is_article'     => self::IS_ARTICLE_YES,
                'c.is_cooperation' => Company::COOPERATIVE_UNIT_YES,
            ]);

        $allJobCount = $query->count();
        $dataCount   = $query->asArray()
            ->one();
        //统计公告下面的所有投递数据数据
        $applyTotal = BaseJobApplyRecord::find()
            ->select([
                'sum(If(delivery_way=1,1,0)) as platformNum',
                'sum(If(delivery_way=2,1,0)) as emailNum',
                'sum(If(delivery_way=3,1,0)) as linkNum',
            ])
            ->andWhere([
                '>',
                'announcement_id',
                0,
            ])
            ->asArray()
            ->one();

        return [
            'apply'       => $applyTotal,
            'allJobCount' => $allJobCount,
            'dataCount'   => $dataCount,
        ];
    }

    /**
     * 公告下的职位投递统计
     * @param $announcementId
     * @return array
     */
    public static function getAnnouncementJobApplyStatistics($announcementId): array
    {
        $jobids = self::find()
            ->select('id')
            ->where([
                'announcement_id' => $announcementId,
                'is_article'      => self::IS_ARTICLE_YES,
            ])
            ->andWhere([
                'in',
                'status',
                [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ])
            ->column();

        $jobApplyCount     = 0;
        $jobOffApplyCount  = 0;
        $jobInterviewCount = 0;
        foreach ($jobids as $jobid) {
            $jobApplyNum[$jobid] = JobApply::find()
                ->where([
                    'job_id' => $jobid,
                ])
                ->count();

            $jobInterviewNum[$jobid] = JobApply::find()
                ->where([
                    'job_id' => $jobid,
                ])
                ->andWhere([
                    '>',
                    'is_invitation',
                    0,
                ])
                ->count();

            $jobOffApplyNum[$jobid] = OffSiteJobApply::find()
                ->where([
                    'job_id' => $jobid,
                ])
                ->count();

            // 站内投递
            $jobApplyCount += $jobApplyNum[$jobid];
            // 站外投递
            $jobOffApplyCount += $jobOffApplyNum[$jobid];
            // 面试
            $jobInterviewCount += $jobInterviewNum[$jobid];
        }

        return [
            'jobApplyCount'     => $jobApplyCount,
            'jobOffApplyCount'  => $jobOffApplyCount,
            'jobInterviewCount' => $jobInterviewCount,
        ];
    }

    /**
     * 获取公告下的职位信息，包括职位数量，招聘数量，城市列表、专业列表
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementJobInfo($announcementId)
    {
        $where = [
            'status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ];

        $query = self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where);

        //获取职位数量
        $jobAmount = $query->count();

        //获取职位招聘数量
        $hasSpcialInfo = self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->andWhere(['amount' => '若干'])
            ->select('id')
            ->asArray()
            ->one();
        if (!empty($hasSpcialInfo['id'])) {
            $recruitAmount = '若干';
        } else {
            $recruitAmount = $query->sum('amount');
        }
        //获取公告下的城市、专业
        $jobInfoList = $query->select([
            'city_id',
            'major_id',
            'id',
        ])
            ->asArray()
            ->all();
        $majorText   = '';
        $cityText    = '';
        foreach ($jobInfoList as $job) {
            $majorText .= $job['major_id'] . ',';
            $cityText  .= $job['city_id'] . ',';
        }
        $cityText  = substr($cityText, 0, -1);
        $majorText = substr($majorText, 0, -1);

        return [
            'jobAmount'     => $jobAmount,
            'recruitAmount' => $recruitAmount,
            'cityText'      => $cityText,
            'majorText'     => $majorText,
        ];
    }

    /**
     * 获取公告下的职位信息，包括职位数量，招聘数量，城市列表、专业列表
     * @param $announcementId
     * @return array
     * @throws \Exception
     */
    public static function getAnnouncementInfo($announcementId)
    {
        $jobInfoList = self::find()
            ->select([
                'city_id',
                'major_id',
                'education_type',
                'job_category_id',
                'id',
                'amount',
            ])
            ->where([
                'announcement_id' => $announcementId,
                'status'          => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ])
            ->asArray()
            ->all();

        $majorArray         = [];
        $cityArray          = [];
        $educationTypeArray = [];
        $categoryArray      = [];
        $jobAmount          = 0;
        $recruitAmount      = 0;

        // 去重
        foreach ($jobInfoList as $job) {
            // 职位人数如果有若干就不统计,直接返回若干
            $jobAmount++;
            if ($job['amount'] == '若干' || $recruitAmount == '若干') {
                $recruitAmount = '若干';
            } else {
                $recruitAmount += $job['amount'];
            }
            $majorArray[$job['major_id']]               = $job['major_id'];
            $cityArray[$job['city_id']]                 = $job['city_id'];
            $educationTypeArray[$job['education_type']] = $job['education_type'];
            $categoryArray[$job['job_category_id']]     = $job['job_category_id'];
        }

        // 找出文案
        $majorText     = '';
        $cityText      = '';
        $educationText = '';
        $categoryText  = '';
        if (!empty($majorArray)) {
            $majorText = BaseMajor::find()
                ->where(['id' => $majorArray])
                ->select('name')
                ->column();
            $majorText = implode(',', $majorText);
        }
        if (!empty($cityArray)) {
            $cityText = BaseArea::find()
                ->where(['id' => $cityArray])
                ->select('name')
                ->column();
            $cityText = implode(',', $cityText);
        }
        if (!empty($educationTypeArray)) {
            $educationArray = BaseDictionary::getEducationDesc($educationTypeArray);
            $educationText  = implode(',', $educationArray);
        }
        if (!empty($categoryArray)) {
            $categoryText = BaseCategoryJob::find()
                ->where(['id' => $categoryArray])
                ->select('name')
                ->column();
            $categoryText = implode(',', $categoryText);
        }

        return [
            'jobAmount'     => $jobAmount,
            'recruitAmount' => $recruitAmount,
            'cityText'      => $cityText,
            'majorText'     => $majorText,
            'educationText' => $educationText,
            'categoryText'  => $categoryText,
        ];
    }

    /**
     * 判断职位是否是合作单位下的职位
     * @param $jobId
     * @return mixed
     */
    public static function checkCooperationStatus($jobId)
    {
        $info = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->where(['j.id' => $jobId])
            ->select('c.is_cooperation')
            ->asArray()
            ->one();

        return $info['is_cooperation'];
    }

    /**
     * 获取推荐职位跳转url
     * @return string
     */
    public static function getRecommendJobUrl()
    {
        return Url::toRoute([
            '/job',
            'sort' => 'default',
        ]);
    }

    /**
     * 获取公告下职位随机一个专业
     * @throws \Exception
     */
    public static function getAnnouncementJobMajor($announcementId): string
    {
        $majorIdList = BaseJob::find()
            ->select(['GROUP_CONCAT(major_id SEPARATOR ",") as majorIds'])
            ->where([
                'announcement_id' => $announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
            ])
            ->asArray()
            ->one();

        $majorIds = explode(',', $majorIdList['majorIds']);

        $length = sizeof($majorIds);
        if ($length > 0) {
            $major = BaseMajor::getAllMajorName($majorIds[0]);
            if ($length > 1 && strlen($major) > 0) {
                $major = $major . "等";
            }
        } else {
            $major = "";
        }

        return $major;
    }

    /**
     * 获取公告下职位学历要求（最低）
     * @throws \Exception
     */
    public static function getAnnouncementJobEducationType($announcementId): string
    {
        $majorIdList = BaseJob::find()
            ->select(['GROUP_CONCAT(education_type SEPARATOR ",") as educationType'])
            ->where([
                'announcement_id' => $announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->asArray()
            ->one();

        $educationType = explode(',', $majorIdList['educationType']);

        $length = sizeof($educationType);
        if ($length > 0) {
            $educationTypeNumber = min($educationType);
            $educationText       = BaseDictionary::getEducationName($educationTypeNumber) ?: '';
            $educationText       = $educationText . ($educationTypeNumber == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上');
            //            if ($length > 1 && strlen($educationText) > 0) {
            //                $educationText = $educationText.($educationText == $topEducationName ? '' : '及以上');
            //            }
        } else {
            $educationText = "";
        }

        return $educationText;
    }

    /**
     * 获取职位其他投递类型名称
     * @param        $applyTypeIds
     * @param string $glue
     * @return mixed|string
     * @throws \Exception
     */
    public static function getApplyTypeName($applyTypeIds, string $glue = ',')
    {
        $applyType = explode(',', $applyTypeIds);
        if (count($applyType) > 1) {
            $applyTypeData = [];
            foreach ($applyType as $value) {
                $applyTypeData[] = BaseDictionary::getSignUpName($value);
            }
            $applyTypeName = implode($glue, $applyTypeData);
        } else {
            $applyTypeName = BaseDictionary::getSignUpName($applyType[0]);
        }

        return $applyTypeName;
    }

    /**
     * 获取报名方式
     */
    public static function getSignUpList(): array
    {
        return BaseDictionary::find()
            ->select([
                'code',
                'name',
            ])
            ->where([
                'type'   => BaseDictionary::TYPE_SIGN_UP,
                'status' => 1,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 检查邮箱投递地址
     * @param $applyAddress
     * @return false|string
     * @throws Exception
     */
    public static function checkEmailApplyAddress($applyAddress)
    {
        // <EMAIL>,adsfadf@sadkf,<EMAIL>，<EMAIL>
        //如果含有，；直接报错告诉只能用英文逗号
        if (strpos($applyAddress, '，') !== false || strpos($applyAddress, '；') !== false || strpos($applyAddress,
                ';') !== false) {
            throw new Exception('邮箱地址只能用英文逗号分隔');
        }
        $applyAddress      = trim(str_replace('，', ',', $applyAddress));
        $applyAddressMulti = explode(',', $applyAddress);
        // 如果有多个邮件
        if (count($applyAddressMulti) > 1) {
            if (count($applyAddressMulti) > 3) {
                throw new Exception('最多可输入3个邮箱地址');
            }
            // 判断是否有重复的邮箱
            if (count($applyAddressMulti) != count(array_unique($applyAddressMulti))) {
                throw new Exception('不支持输入重复的邮箱，请确认');
            }

            $applyEmail = '';
            foreach ($applyAddressMulti as $value) {
                // 校验邮箱正确性
                if (!ValidateHelper::isEmail($value)) {
                    throw new Exception('请输入正确的邮箱地址');
                }

                $applyEmail .= $value . ',';
            }
            $applyAddress = substr($applyEmail, '0', ' -1');
        } else {
            // 校验邮箱正确性
            if (!ValidateHelper::isEmail($applyAddress)) {
                throw new Exception('请输入正确的邮箱地址');
            }
        }

        return $applyAddress;
    }

    /**
     * 校验投递地址
     * @param     $applyTypeArr
     * @param     $applyAddress
     * @param int $isCooperation
     * @return false|mixed|string
     * @throws Exception
     */
    public static function validateApplyAddress($applyTypeArr, $applyAddress)
    {
        if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
            $applyAddress = BaseJob::checkEmailApplyAddress($applyAddress);
        } elseif (in_array(BaseJob::ATTRIBUTE_APPLY_ONLINE, $applyTypeArr)) {
            if (!ValidateHelper::isUrl($applyAddress)) {
                throw new Exception('单位报名网址格式错误');
            }
        } else {
            $applyAddress = '';
        }

        return $applyAddress;
    }

    /**
     * 获取公告下的职位的地区名称
     * @param $announcementId
     * @param $type
     * @return false|string
     */
    public static function getAnnouncementJobAreaAccount($announcementId, $limit = 5)
    {
        $where = [
            'status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ];

        $jobList = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select([
                'city_id',
                'province_id',
            ])
            ->groupBy('city_id')
            ->orderBy('refresh_time desc')
            ->asArray()
            ->limit($limit)
            ->all();
        if (!empty($jobList)) {
            if (count($jobList) == 1) {
                $provinceName = BaseArea::getAreaName($jobList[0]['province_id']);
                $cityName     = BaseArea::getAreaName($jobList[0]['city_id']);
                if ($provinceName == $cityName) {
                    $areaName = $cityName;
                } else {
                    $areaName = $provinceName . ' - ' . $cityName;
                }
            } else {
                $areaName = '';
                $cityArr  = [];
                foreach ($jobList as $k => $job) {
                    array_push($cityArr, $job['city_id']);
                }

                $cityArr = array_unique($cityArr);
                foreach ($cityArr as $city) {
                    $areaName .= BaseArea::getAreaName($city) . ' ';
                }
                $areaName = substr($areaName, 0, -1);
            }

            return $areaName;
        } else {
            return '';
        }
    }

    public static function getAnnouncementJobCityName($announcementId, $more = '等')
    {
        $where = [
            'status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ];

        $jobList = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere($where)
            ->select([
                'city_id',
            ])
            ->groupBy('city_id')
            ->orderBy('refresh_time desc')
            ->asArray()
            ->limit(2)
            ->all();
        if (!empty($jobList)) {
            if (count($jobList) == 1) {
                $cityName = BaseArea::getAreaName($jobList[0]['city_id']);

                return $cityName;
            } else {
                $cityName = BaseArea::getAreaName($jobList[0]['city_id']) . $more;

                return $cityName;
            }
        } else {
            return '';
        }
    }

    /**
     * 获取单条职位公司信息
     * @param $jobId
     * @return array|yii\db\ActiveRecord|null
     */
    public static function getJobCompanyInfo($jobId)
    {
        return self::find()
            ->with([
                'company',
                'announcement',
            ])
            ->where([
                'id'     => $jobId,
                'status' => self::STATUS_ONLINE,
            ])
            ->asArray()
            ->one();
    }

    /**
     * 职位对单位一对一关系
     */
    public function getCompany()
    {
        return $this->hasOne(BaseCompany::class, ['id' => 'company_id']);
    }

    /**
     * 职位对公告一对一关系
     */
    public function getAnnouncement()
    {
        return $this->hasOne(BaseAnnouncement::class, ['id' => 'announcement_id']);
    }

    /**
     * 职位对职位联系人一对一关系
     */
    public function getContact()
    {
        return $this->hasOne(BaseJobContact::class, ['job_id' => 'id']);
    }

    /**
     * 职位对职位协同账号一对多关系
     */
    public function getContactSynergy()
    {
        return $this->hasMany(BaseJobContactSynergy::class, ['job_id' => 'id']);
    }

    public static function getCompanyJobList($data)
    {
        unset($data['/api/person/company/get-job-list']);
        if (count($data) == 0) {
            $stringKey = '0';
        } else {
            // 把搜索条件转换成字符串
            $stringKey = ArrayHelper::arrayToStringKey($data);
        }
        //获取memberId
        $memberId   = Yii::$app->user->id;
        $baseKey    = Cache::ALL_COMPANY_JOB_LIST;
        $cacheKey   = $baseKey . ':' . $stringKey;
        $resultData = json_decode(Cache::get($cacheKey), true);
        if ($resultData) {
            return $resultData;
        } else {
            //学科查询特殊处理
            $searchModel = self::find()
                ->alias('j')
                ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
                ->where(['j.is_show' => self::IS_SHOW_YES]);

            if ($data['majorId']) {
                $majorIds = explode('_', $data['majorId']);
                $searchModel->innerJoin(['jm' => BaseJobMajorRelation::tableName()], 'jm.job_id = j.id');
                $searchModel->andWhere(['jm.major_id' => $majorIds]);
            }

            //城市查询
            if ($data['areaId']) {
                $area = $data['areaId'];
                if (count(explode('_', $area)) > 1) {
                    $areaIds = explode('_', $area);
                } else {
                    $areaIds = [$area];
                }

                $areaIds = BaseArea::getCityIds($areaIds);

                $searchModel->andWhere(['j.city_id' => $areaIds]);
            }

            if ($data['jobCategoryId'] && $data['jobCategoryId'] != 'undefined') {
                if (count(explode('_', $data['jobCategoryId'])) > 1) {
                    $searchModel->andFilterWhere([
                        'in',
                        'j.job_category_id',
                        explode('_', $data['jobCategoryId']),
                    ]);
                } else {
                    $searchModel->andFilterWhere(['j.job_category_id' => $data['jobCategoryId']]);
                }
            }

            //单位查询
            if (count(explode('_', $data['companyId'])) > 1) {
                $searchModel->andFilterWhere([
                    'in',
                    'j.company_id',
                    explode('_', $data['companyId']),
                ]);
            } else {
                $searchModel->andFilterWhere(['j.company_id' => $data['companyId']]);
            }
            //克隆原始$searchModel到$onlineQuery
            $onlineQuery = clone $searchModel;
            //获取在线职位总数
            $onlineJobCount = $onlineQuery->andWhere(['j.status' => self::STATUS_ONLINE])
                ->count();
            //状态条件移动到这个位置是不影响原始逻辑
            $searchModel->andWhere([
                'in',
                'j.status',
                [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ]);

            //获取总数量
            $count = $searchModel->count();

            $pageSize = $data['pageSize'] ?: \Yii::$app->params['jobListDefaultPageSize'];

            $pages = self::setPage($count, $data['page'], $pageSize);

            // 单位等级排序,refresh_time日期
            $sort = 'j.status desc,j.refresh_time desc,j.id desc';
            $list = $searchModel->select([
                'j.id as jobId',
                'j.status',
                'j.name as jobName',
                'j.company_id as companyId',
                'j.min_wage as minWage',
                'j.max_wage as maxWage',
                'j.wage_type as wageType',
                'j.amount',
                'j.job_category_id as jobCategoryId',
                'j.experience_type as experienceType',
                'j.education_type as educationType',
                'j.province_id as provinceId',
                'j.city_id as cityId',
                'c.full_name as companyName',
                'j.release_time as releaseTime',
                'j.refresh_time as refreshTime',
                'j.apply_type as applyType',
                'j.apply_address as applyAddress',
                'j.announcement_id as announcementId',
                'a.title as announcementName',
                'j.major_id as majorId',
                'c.is_cooperation',
            ])
                ->orderBy($sort)
                ->asArray()
                ->offset($pages['offset'])
                ->limit($pages['limit'])
                ->all();

            foreach ($list as $k => &$jobRecord) {
                //获取经验要求
                $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
                $jobRecord['jobName']    = str_replace(PHP_EOL, '', $jobRecord['jobName']);
                //拼接工资
                if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                    $jobRecord['wage'] = '面议';
                } else {
                    $jobRecord['wage'] = self::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                        $jobRecord['wageType']);
                }
                //获取学历水平
                $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
                //获取地区名称
                $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . ' - ' . BaseArea::getAreaName($jobRecord['cityId']);
                $jobRecord['city']     = BaseArea::getAreaName($jobRecord['cityId']);
                //获取职位需求专业
                $major = '';
                if ($jobRecord['majorId']) {
                    $jobMajorIds = explode(',', $jobRecord['majorId']);
                    if (count($jobMajorIds) > 0) {
                        $major = BaseMajor::getAllMajorName($jobMajorIds);
                    }
                }
                $jobRecord['major'] = $major;
                //处理发布时间
                $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
                $releaseTime  = strtotime($jobRecord['releaseTime']);
                if ($releaseTime > $thisYearTime) {
                    $jobRecord['releaseTime'] = date('m-d', $releaseTime);
                } else {
                    $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
                }

                $refreshTime = $jobRecord['refreshTime'];
                // if ($refreshTime > $thisYearTime) {
                //     $jobRecord['refreshTime'] = date('m-d', $refreshTime);
                // } else {
                //     $jobRecord['refreshTime'] = date('Y-m-d', $refreshTime);
                // }
                $jobRecord['refreshTime'] = TimeHelper::formatDateByYear($refreshTime);

                $jobRecord['url']             = Url::toRoute([
                    'job/detail',
                    'id' => $jobRecord['jobId'],
                ]);
                $jobRecord['companyUrl']      = Url::toRoute([
                    'company/detail',
                    'id' => $jobRecord['companyId'],
                ]);
                $jobRecord['announcementUrl'] = Url::toRoute([
                    'announcement/detail',
                    'id' => $jobRecord['announcementId'],
                ]);
                //判断职位是否是合作单位的职位
                $jobRecord['applyStatus']      = BaseJob::JOB_APPLY_STATUS_NO;
                $jobRecord['userEmail']        = '';
                $jobRecord['isEmailApply']     = "false";
                $jobRecord['shortRefreshTime'] = date('m-d', strtotime($refreshTime));

                if ($jobRecord['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                    //如果是合作单位，站内投递
                    $jobRecord['isCooperation'] = "true";
                } else {
                    //站外投递
                    $jobRecord['isCooperation'] = "false";
                    if (!empty($data['memberId'])) {
                        $jobRecord['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');;
                    }
                }
                if ($memberId) {
                    //获取简历信息
                    $resume_info              = BaseResume::findOne([
                        'member_id' => $memberId,
                        'status'    => BaseResume::STATUS_ACTIVE,
                    ]);
                    $jobRecord['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resume_info->id,
                        $jobRecord['jobId']);
                }
            }
            $resultData = [
                'list'           => $list,
                'pageSize'       => $pageSize,
                'currentPage'    => $data['page'],
                'totalNum'       => $count,
                'onlineTotalNum' => $onlineJobCount,
            ];
            Cache::set($cacheKey, json_encode($resultData), 3600);

            return $resultData;
        }
    }

    /**
     * 这个方法比较简单，只是获取单位下的在线职位
     * @param $params
     * @return array|yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public static function getCompanyHotJobList($params)
    {
        $searchModel = self::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->where([
                'j.is_show' => self::IS_SHOW_YES,
                'j.status'  => self::STATUS_ONLINE,
                'c.id'      => $params['companyId'],
            ]);

        $sort = 'j.status desc,j.refresh_time desc,j.id desc';

        $list = $searchModel->select([
            'j.id as jobId',
            'j.status',
            'j.name as jobName',
            'j.company_id as companyId',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.experience_type as experienceType',
            'j.education_type as educationType',
            'j.amount',
            'j.job_category_id as jobCategoryId',
            'j.welfare_tag as welfareTag',
            'j.province_id as provinceId',
            'j.city_id as cityId',
            'c.full_name as companyName',
            'c.type as companyType',
            'c.nature as companyNature',
            'j.release_time as releaseTime',
            'j.refresh_time as refreshTime',
            'j.apply_type as applyType',
            'j.apply_address as applyAddress',
            'j.announcement_id as announcementId',
            'j.major_id as majorId',
        ])
            ->orderBy($sort)
            ->asArray()
            ->limit($params['pageSize'])
            ->all();
        foreach ($list as $k => &$jobRecord) {
            $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
            //拼接工资
            if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                $jobRecord['wage'] = '面议';
            } else {
                $jobRecord['wage'] = self::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                    $jobRecord['wageType']);
            }
            //获取经验要求
            $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
            //获取学历水平
            $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
            //获取地区名称
            $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . ' - ' . BaseArea::getAreaName($jobRecord['cityId']);
            $jobRecord['city']     = BaseArea::getAreaName($jobRecord['cityId']);
            //处理发布时间
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            $releaseTime  = strtotime($jobRecord['releaseTime']);
            if ($releaseTime > $thisYearTime) {
                $jobRecord['releaseTime'] = date('m-d', $releaseTime);
            } else {
                $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
            }
            $jobRecord['url'] = Url::toRoute([
                'job/detail',
                'id' => $jobRecord['jobId'],
            ]);
            //判断职位是否是合作单位的职位
            $refreshTime                   = $jobRecord['refreshTime'];
            $jobRecord['refreshTime']      = TimeHelper::formatDateByYear($refreshTime);
            $jobRecord['shortRefreshTime'] = date('m-d', strtotime($jobRecord['refreshTime']));
        }

        return $list;
    }

    /**
     * 更新职位以后的后续操作
     * @param $id
     * @return void
     * @throws Exception
     */
    public static function afterUpdate($id)
    {
        $auto = new JobAutoClassify($id);
        $auto->run();
    }

    /**
     *  服务邮件信息--职位部分信息
     * @throws \Exception
     */
    public static function getMailJob($jobId): array
    {
        $job                     = BaseJob::findOne(['id' => $jobId]);
        $visitUrl                = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        $job['refresh_date']     = TimeHelper::formatDateByYear($job['refresh_date']);
        $result                  = [
            'id'          => $jobId,
            'jobName'     => $job['name'],
            'refreshDate' => $job['refresh_date'],
            'requirement' => $job['requirement'],
            'amount'      => $job['amount'],
            'department'  => $job['department'] ? '&nbsp;|&nbsp;' . $job['department'] : '',
            'companyId'   => $job['company_id'],
        ];
        $result['companyName']   = BaseCompany::findOneVal(['id' => $job['company_id']], 'full_name');
        $result['education']     = BaseDictionary::getEducationName($job['education_type']);
        $result['areaName']      = BaseArea::getAreaName($job['province_id']) . '-' . BaseArea::getAreaName($job['city_id']);
        $result['welfareTagArr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($job['welfare_tag']), 0, 3);
        $result['welfareTagAll'] = BaseWelfareLabel::getWelfareLabelNameList($job['welfare_tag']);

        $result['companyLink'] = $visitUrl . '/company/detail/' . $job['company_id'] . '.html';
        $result['jobLink']     = $visitUrl . '/job/detail/' . $jobId . '.html';

        if (sizeof($result['welfareTagAll']) > 3) {
            $result['isWelfare'] = 1;
        } else {
            $result['isWelfare'] = 0;
        }

        return $result;
    }

    /**
     *  服务邮件信息--投递处理不合适职位
     * @throws \Exception
     */
    public static function getInappropriateJob($memberId, $jobId): array
    {
        $job    = BaseJob::findOne(['id' => $jobId]);
        $result = [
            'id'      => $jobId,
            'jobName' => $job['name'],
        ];

        //这里拿简历非必填项目
        $resumeInfo = BaseResume::find()
            ->alias('r')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            ->select([
                'm.avatar',
                'r.advantage',
                'r.last_work_id as lastWorkId',
            ])
            ->where(['r.member_id' => $memberId])
            ->asArray()
            ->one();

        $where = [
            'member_id' => $memberId,
            'status'    => BaseActiveRecord::STATUS_ACTIVE,
        ];
        $key   = 'id';

        //求职意向
        $resumeInfo['intentionId'] = BaseResumeIntention::findOneVal($where, $key);
        //研究方向
        $resumeInfo['researchProjectId'] = BaseResumeResearchDirection::findOneVal($where, $key);
        //学术成果
        $academicBookId         = BaseResumeAcademicBook::findOneVal($where, $key);
        $academicPageId         = BaseResumeAcademicPage::findOneVal($where, $key);
        $academicPatentId       = BaseResumeAcademicPatent::findOneVal($where, $key);
        $resumeInfo['academic'] = $academicBookId + $academicPageId + $academicPatentId;
        //荣誉奖励
        $academicRewardId      = BaseResumeAcademicReward::findOneVal($where, $key);
        $academicOtherRewardId = BaseResumeOtherReward::findOneVal($where, $key);
        $resumeInfo['reward']  = $academicRewardId + $academicOtherRewardId;
        //技能特长
        $certificateId       = BaseResumeCertificate::findOneVal($where, $key);
        $skillId             = BaseResumeSkill::findOneVal($where, $key);
        $otherSkillId        = BaseResumeOtherSkill::findOneVal($where, $key);
        $resumeInfo['skill'] = $certificateId + $skillId + $otherSkillId;

        //做一个非必填项目
        $checkList = [
            'avatar'            => '个人头像',
            'advantage'         => '个人优势',
            'intentionId'       => '求职意向',
            'researchProjectId' => '研究方向',
            'lastWorkId'        => '工作经历',
            'academic'          => '学术成果',
            'reward'            => '荣誉奖励',
            'skill'             => '技能特长',
        ];

        $tags = '';
        foreach ($checkList as $k => $item) {
            if (!$resumeInfo[$k]) {
                $tags = $tags . $item . '、';
            }
        }
        $tags = substr($tags, 0, -3);

        $result['tags']        = $tags;
        $visitUrl              = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        $result['companyName'] = BaseCompany::findOneVal(['id' => $job['company_id']], 'full_name');
        $result['companyLink'] = $visitUrl . '/company/detail/' . $job['company_id'] . '.html';
        $result['jobLink']     = $visitUrl . '/job/detail/' . $jobId . '.html';
        $result['resumeLink']  = $visitUrl . '/member/person/resume';
        $result['memberName']  = BaseResume::findOneVal(['member_id' => $memberId], 'name');

        return $result;
    }

    /**
     *  服务邮件信息--投递反馈不合适，职位推荐
     * @throws \Exception
     */
    public static function getMailRecommend($memberId, $jobId): array
    {
        //获取参数三样--专业、学历、职位类型
        $lastEducationId = BaseResume::findOneVal(['member_id' => $memberId], 'last_education_id');
        $info            = BaseResumeEducation::find()
            ->select([
                'major_id',
                'education_id',
            ])
            ->where(['id' => $lastEducationId])
            ->asArray()
            ->one();

        $majorId       = BaseMajor::getOneParentId($info['major_id']);
        $educationId   = $info['education_id'];
        $jobCategoryId = BaseJob::findOneVal(['id' => $jobId], 'job_category_id');

        $select = [
            'j.id',
            'j.name',
            'j.education_type as educationType',
            'j.major_id as majorId',
            'j.amount',
            'j.refresh_date as refreshDate',
            'c.full_name as fullName',
            'c.city_id as cityId',
        ];
        $query  = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->select($select)
            ->where([
                'j.status' => BaseActiveRecord::STATUS_ACTIVE,
                // 1.3.2去掉这个限制
                // 'c.package_type' => BaseCompany::PACKAGE_TYPE_SENIOR,
            ]);

        // 拿出当前置顶中职位
        $topListJob = BaseJobTopConfig::find()
            ->select(['job_id'])
            ->where([
                'status' => BaseJobTopConfig::STATUS_TOP_ING,
            ])
            ->asArray()
            ->column();

        /**
         * 近7天（包括当前日）有置顶历史的职位＞发布时间（指最新发布时间）＞高级会员的职位＞其他合作单位的职位＞非合作单位的职位；即符合规则1的职位
         */
        $orderBy = [];
        if ($topListJob) {
            // 特定id在最前面
            $orderBy['FIELD(j.id,' . implode(',', $topListJob) . ')'] = SORT_DESC;
        }
        $orderBy['j.refresh_time'] = SORT_DESC;
        $orderBy['c.sort']         = SORT_DESC;

        $query->andFilterCompare('j.id', $jobId, '<>');
        if ($majorId) {
            $query->innerJoin(['jm' => BaseJobMajorRelation::tableName()], 'jm.job_id = j.id');
            $query->andWhere(['jm.major_id' => $majorId]);
        }
        $query->andFilterCompare('j.education_type', $educationId);
        $query->andFilterCompare('j.job_category_id', $jobCategoryId);

        $list = $query->limit(self::MAIL_RECOMMEND_NUM)
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //数据返回做个处理
        $visitUrl = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        foreach ($list as &$item) {
            $item['educationName'] = BaseDictionary::getEducationName($item['educationType']);
            $item['majorName']     = BaseMajor::getMajorName($item['majorId']);
            $majorIds              = explode(',', $item['majorId']);
            if (sizeof($majorIds) > 1) {
                $item['majorName'] = $item['majorName'] . '等';
            }
            $item['city']        = BaseArea::getAreaName($item['cityId']);
            $item['jobLink']     = $visitUrl . '/job/detail/' . $item['id'] . '.html';
            $item['address']     = 'https://img.gaoxiaojob.com/uploads/static/image/resume/address.png';
            $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate']);
        }

        $allowance = self::MAIL_RECOMMEND_NUM - sizeof($list);
        //另外推荐--规则调整
        if ($allowance > 0) {
            $jobIds = array_column($list, 'id');
            array_push($jobIds, (string)$jobId);
            $majorId = BaseMajor::findOneVal([
                'name'  => '专业不限',
                'level' => 2,
            ], 'id');

            $repairQuery = BaseJob::find()
                ->alias('j')
                ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
                ->select($select)
                ->where([
                    'j.status' => BaseActiveRecord::STATUS_ACTIVE,
                ])
                ->andWhere([
                    'NOT IN',
                    'j.id',
                    $jobIds,
                ]);

            if ($majorId) {
                $repairQuery->andWhere("find_in_set(" . $majorId . ",j.major_id)");
            }
            $repairQuery->andFilterCompare('j.education_type', $educationId);
            $repairQuery->andFilterCompare('j.job_category_id', $jobCategoryId);
            $repairList = $repairQuery->limit($allowance)
                ->orderBy($orderBy)
                ->asArray()
                ->all();

            foreach ($repairList as &$item) {
                $item['educationName'] = BaseDictionary::getEducationName($item['educationType']);
                $majorId               = explode(',', $item['majorId']);
                $item['majorName']     = BaseMajor::getAllMajorName($majorId[0]);
                if (sizeof($majorId) > 1) {
                    $item['majorName'] = $item['majorName'] . '等';
                }
                $item['city']        = BaseArea::getAreaName($item['cityId']);
                $item['jobLink']     = $visitUrl . '/job/detail/' . $item['id'] . '.html';
                $item['address']     = 'https://img.gaoxiaojob.com/uploads/static/image/resume/address.png';
                $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate']);
            }

            $list = array_merge($list, $repairList);
        }

        // 邮件只需要8条数据
        $list = array_slice($list, 0, 8);

        $checkData = [
            'educationName',
            'majorName',
            'amount',
            'city',
            'refreshDate',
        ];

        $list = ArrayHelper::arrayToDisplay($list, $checkData);

        return $list;
    }

    public static function isFast($id)
    {
        // 查询职位是否反馈快的,其实这里有两种情况,一种只看职位本身就好了'j.delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
        //                         'j.delivery_way'  => BaseJob::DELIVERY_WAY_PLATFORM,

        $jobInfo = self::find()
            ->select([
                'j.delivery_type',
                'j.delivery_way',
                'j.announcement_id',
            ])
            ->alias('j')
            ->where([
                'j.id' => $id,
            ])
            ->asArray()
            ->one();

        if (!$jobInfo) {
            return false;
        }

        if ($jobInfo['delivery_type'] == self::DELIVERY_TYPE_OUTSIDE && $jobInfo['delivery_way'] == self::DELIVERY_WAY_PLATFORM) {
            return true;
        }

        if ($jobInfo['delivery_type'] == self::DELIVERY_TYPE_UP_ANNOUNCEMENT) {
            // 这个时候就是看职位所属公告了
            $announcementInfo = BaseAnnouncement::find()
                ->select([
                    'a.delivery_type',
                    'a.delivery_way',
                ])
                ->alias('a')
                ->where([
                    'a.id' => $jobInfo['announcement_id'],
                ])
                ->asArray()
                ->one();

            if (!$announcementInfo) {
                return false;
            }

            if ($announcementInfo['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE && $announcementInfo['delivery_way'] == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                return true;
            }
        }

        return false;
    }

    public static function isEstablishment($id)
    {
        $jobInfo = self::find()
            ->select([
                'j.is_establishment',
            ])
            ->alias('j')
            ->where([
                'j.id' => $id,
            ])
            ->asArray()
            ->one();

        if (!$jobInfo) {
            return false;
        }

        if ($jobInfo['establishment'] == self::IS_ESTABLISHMENT_YES) {
            return true;
        }

        return false;
    }

    /**
     * 获取小程序首页推荐职位列表
     * @throws \Exception
     */
    public static function getMiniAppRecommendJobList()
    {
        $cacheKey = Cache::MINI_HOME_RECOMMEND_JOB_KEY;
        $data     = Cache::get($cacheKey);
        if ($data) {
            return json_decode($data, true);
        }

        return BaseJob::setMiniAppRecommendJobList();
    }

    /**
     * 小程序首页推荐职位列表
     * 无求职意向
     * @throws \Exception
     */
    public static function setMiniAppRecommendJobList(): array
    {
        $cacheKey = Cache::MINI_HOME_RECOMMEND_JOB_KEY;
        $today    = date('Y-m-d', time());
        $select   = [
            'j.announcement_id',
            'j.id',
            'j.name as job_name',
            'j.job_category_id',
            'j.education_type',
            'j.major_id',
            'j.amount',
            'j.welfare_tag',
            'j.add_time',
            'j.city_id',
            'j.announcement_id',
            'j.delivery_type',
            'j.delivery_way',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.city_id',
            'j.company_id',
            'j.refresh_time',
            'j.is_establishment',
            'j.first_release_time',
        ];

        $where = [
            'j.status'     => BaseActiveRecord::STATUS_ACTIVE,
            'j.is_show'    => BaseJob::IS_SHOW_YES,
            'j.is_miniapp' => BaseJob::IS_MINIAPP_YES,
        ];

        $mimiTime = 60 * 60 * 24 * 7;//取7天内
        $sevenDay = TimeHelper::computeDatetime(CUR_DATETIME, $mimiTime, 2);

        $todayJobIds = BaseJobTopConfig::find()
            ->alias('t')
            ->select(['job_id'])
            ->where([
                't.status'    => BaseJobTopConfig::STATUS_TOP_ING,
                't.date'      => $today,
                't.is_delete' => BaseJobTopConfig::IS_DELETE_NO,
            ])
            ->groupBy('job_id')
            ->column();

        $list = BaseJob::find()
            ->alias('j')
            ->select($select)
            ->where($where)
            ->andWhere([
                'j.id' => $todayJobIds,
            ])
            ->groupBy('j.id')
            ->orderBy('rand()')
            ->limit(20)
            ->asArray()
            ->all();

        if (20 > sizeof($list)) {
            $sevenLimit = 20 - sizeof($list);
            $sevenList  = BaseJob::find()
                ->alias('j')
                ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'jar.job_id = j.id')
                ->select(array_merge($select, [
                    'count(jar.id) as jobApplyNum',
                ]))
                ->where($where)
                ->andWhere([
                    'not in',
                    'j.id',
                    $todayJobIds,
                ])
                ->andWhere([
                    '>=',
                    'j.first_release_time',
                    $sevenDay,
                ])
                ->groupBy('j.id')
                ->orderBy('jobApplyNum desc,j.refresh_time desc,j.id desc')
                ->limit($sevenLimit)
                ->asArray()
                ->all();

            $list = array_merge($list, $sevenList);
        }

        //这里补一下数据
        if (20 > sizeof($list)) {
            $carLimit  = 20 - sizeof($list);
            $fifthTime = 60 * 60 * 24 * 300;//取15天内
            $fifthDay  = TimeHelper::computeDatetime(CUR_DATETIME, $fifthTime, 2);
            $otherList = BaseJob::find()
                ->alias('j')
                ->select($select)
                ->where($where)
                ->andWhere([
                    'not in',
                    'j.id',
                    $todayJobIds,
                ])
                ->andWhere([
                    '<',
                    'j.first_release_time',
                    $sevenDay,
                ])
                ->andWhere([
                    '>=',
                    'j.first_release_time',
                    $fifthDay,
                ])
                ->groupBy('j.id')
                ->orderBy('j.refresh_time desc,j.id desc')
                ->limit($carLimit)
                ->asArray()
                ->all();
            $list      = array_merge($list, $otherList);
        }

        $areaCache = BaseArea::setAreaCache();
        foreach ($list as &$item) {
            $item['isTop']     = $item['typeNum'] ? 1 : 2;
            $item['education'] = BaseDictionary::getEducationName($item['education_type']);
            $major             = '';
            if ($item['major_id']) {
                $jobMajorIds = explode(',', $item['major_id']);
                $length      = count($jobMajorIds);
                if ($length > 1) {
                    // $major = BaseMajor::getAllMajorName($jobMajorIds[0]);
                    $major = BaseMajor::findOneVal(['id' => $jobMajorIds[0]], 'name');
                    $major .= "等";
                } else {
                    // $major = BaseMajor::getAllMajorName($jobMajorIds);
                    $major = BaseMajor::findOneVal(['id' => $jobMajorIds[0]], 'name');
                }
            }
            $item['majorName'] = $major;
            if ($item['min_wage'] == 0 && $item['max_wage'] == 0) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = self::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            }
            $item['companyName']      = BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name');
            $item['announcementName'] = BaseAnnouncement::findOneVal(['id' => $item['announcement_id']], 'title');
            $item['city']             = $areaCache[$item['city_id']]['name'];
            $item['welfareTagArr']    = BaseWelfareLabel::getWelfareLabelNameList($item['welfare_tag']);
            $item['welfareTagTxt']    = implode(',', $item['welfareTagArr']);
            //反馈快
            $is_fast = 2;
            if ($item['delivery_type']) {
                if ($item['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTER) {
                    $item['apply_type_text'] = BaseJob::getApplyTypeName($item['apply_type']);
                } else {
                    $item['apply_type_text'] = '站内投递';
                }
                if ($item['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
                    $is_fast = 1;
                }
            } else {
                if ($item['announcement_id'] > 0) {
                    if ($item['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                        $item['apply_type_text'] = BaseJob::getApplyTypeName($item['apply_type']);
                    } else {
                        $item['apply_type_text'] = '站内投递';
                    }
                    if ($item['delivery_way'] == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                        $is_fast = 1;
                    }
                }
            }
            $item['is_fast'] = $is_fast;
        }

        Cache::set($cacheKey, json_encode($list));

        return $list;
    }

    /**
     * 获取小程序搜索页面的职位列表
     * @throws \Exception
     */
    public static function searchForMiniAppList($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            'j.major_id',
            'j.job_category_id',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.amount',
            'j.is_negotiable',
            'j.company_id',
            'j.education_type',
            'j.announcement_id',
            'j.experience_type',
            'j.refresh_time',
            'j.city_id',
            'c.full_name',
            'j.education_type',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->select($select)
            ->where([
                'j.status'     => [
                    BaseActiveRecord::STATUS_ACTIVE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.is_show'    => BaseJob::IS_SHOW_YES,
                'j.is_miniapp' => 1,
            ]);

        if ($keywords['cityIds']) {
            $cityIds   = explode(',', $keywords['cityIds']);
            $condition = ['or'];
            foreach ($cityIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.city_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['jobCategoryIds']) {
            $jobCategoryIds = explode(',', $keywords['jobCategoryIds']);
            $condition      = ['or'];
            foreach ($jobCategoryIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.job_category_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['addTimeStart']) {
            $query->andWhere([
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($keywords['refreshTimeStart']),
            ]);
        }
        if ($keywords['addTimeEnd']) {
            $query->andWhere([
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($keywords['refreshTimeEnd']),
            ]);
        }

        if ($keywords['welfareTag']) {
            $welfare_tag = explode(',', $keywords['welfare_tag']);
            $condition   = ['or'];
            foreach ($welfare_tag as $item) {
                $condition[] = "find_in_set(" . $item . ",j.welfare_tag)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['majorIds']) {
            $majorIds  = explode(',', $keywords['majorIds']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        $query->andFilterCompare('j.education_type', $keywords['educationType']);
        $query->andFilterCompare('c.type', $keywords['companyType']);
        $query->andFilterCompare('c.nature', $keywords['companyNature']);
        $query->andFilterCompare('j.nature_type', $keywords['natureType']);
        $query->andFilterCompare('j.title_type', $keywords['titleType']);
        $query->andFilterCompare('j.experience_type', $keywords['experienceType']);

        $count = $query->count();

        $pageSize = $keywords['pageSize'] ?: \Yii::$app->params['jobListDefaultPageSize'];

        $pages = self::setPage($count, $keywords['page'], $pageSize);

        $list = $query->groupBy('j.id')
            ->orderBy('j.status ')
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        $areaCache = BaseArea::setAreaCache();

        foreach ($list as &$item) {
            $item['educationTxt'] = BaseDictionary::getEducationName($item['education_type']);
            $major                = '';
            if ($item['major_id']) {
                $jobMajorIds = explode(',', $item['major_id']);
                if (count($jobMajorIds) > 0) {
                    $major = BaseMajor::getAllMajorName($jobMajorIds);
                }
            }
            $item['majorTxt']  = $major;
            $item['amountTxt'] = '招' . $item['amount'] . '人';
            if ($item['min_wage'] == 0 && $item['max_wage'] == 0) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = self::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            }
            $item['companyTxt']       = BaseCompany::findOneVal(['id' => $item['company_id']], 'full_name') ?: '';
            $item['announcementName'] = BaseAnnouncement::findOneVal(['id' => $item['announcement_id']], 'title') ?: '';
            $item['cityTxt']          = $areaCache[$item['city_id']]['name'];
        }

        return $list;
    }

    public static function isTop($id)
    {
        $topInfo = BaseJobTopConfig::find()
            ->select('id')
            ->where([
                'job_id' => $id,
                'status' => BaseJobTopConfig::STATUS_TOP_ING,
            ])
            ->asArray()
            ->one();
        if (!empty($topInfo['id'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取在线职位列表-人才推荐
     * @param $memberId
     * @param $jobIds
     * @return array|yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public static function onlineList($memberId, $jobIds = [])
    {
        //获取单位company ID
        $query = self::find()
            ->alias('j')
            ->select([
                'j.id as job_id',
                'j.name as job_name',
                'a.title as announcement_name',
                'j.education_type as education_id',
                'j.city_id',
                'j.job_category_id',
                'j.department',
                'j.major_id',
            ])
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->where([
                'j.status' => self::STATUS_ACTIVE,
            ]);
        //按照先按照$jobIds的顺序排序再按照职位发布时间排序
        //按照先按照$jobIds的顺序排序再按照职位发布时间排序
        if (!empty($jobIds)) {
            //将$jobIds倒叙一下---FIELD倒叙才起作用 所以先反转一下 查询再反转一下 宠儿得到进入时候的顺序
            $jobIds = array_reverse($jobIds);
            $query->orderBy(new Expression("FIELD(j.id," . implode(',', $jobIds) . ") desc,j.refresh_time desc"));
        } else {
            $query->orderBy('j.refresh_time desc');
        }

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }
        $list         = $query->asArray()
            ->all();
        $job_cache_id = [];
        foreach ($list as &$item) {
            $job_intention = [
                BaseDictionary::getEducationName($item['education_id']),
                BaseArea::getAreaName($item['city_id']),
            ];
            if ($item['department']) {
                array_push($job_intention, $item['department']);
            }
            $item['job_intention'] = implode('·', $job_intention);
            if (count($job_cache_id) < 7) {
                array_push($job_cache_id, $item['job_id']);
            }
            // 这里由于前端选择更改,所以需要专业的三级用于选择
            $item['majorLevelThreeIds'] = implode(',', BaseMajor::getAllLevel3($item['major_id']));
        }
        if (count($job_cache_id) > 0 && Cache::get(Cache::COMPANY_JOB_ONLINE_SORT . ':' . $memberId) === null) {
            Cache::setex(Cache::COMPANY_JOB_ONLINE_SORT . ':' . $memberId, 604800, implode(',', $job_cache_id));
        }

        return $list;
    }

    /**
     * 获取缓存职位-人才推荐
     * @param $companyId
     * @return array|yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public static function cacheOnlineList($jobIds)
    {
        $query = self::find()
            ->alias('j')
            ->select([
                'j.id as job_id',
                'j.name as job_name',
                'a.title as announcement_name',
                'j.education_type as education_id',
                'j.city_id',
                'j.job_category_id',
                'j.department',
                'j.major_id',
            ])
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->where([
                'j.status' => self::STATUS_ACTIVE,
                'j.id'     => $jobIds,
            ])
            //按照$jobIds的顺序排序
            ->orderBy(new Expression('FIELD(j.id,' . implode(',', $jobIds) . ')'));

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $list = $query->asArray()
            ->all();

        foreach ($list as &$item) {
            $job_intention = [
                BaseDictionary::getEducationName($item['education_id']),
                BaseArea::getAreaName($item['city_id']),
            ];
            if ($item['department']) {
                array_push($job_intention, $item['department']);
            }
            $item['job_intention'] = implode('·', $job_intention);
            // 这里由于前端选择更改,所以需要专业的三级用于选择
            $item['majorLevelThreeIds'] = implode(',', BaseMajor::getAllLevel3($item['major_id']));
        }

        return $list;
    }

    /**
     * 获取最新一个在线职位
     * @return array|yii\db\ActiveRecord|null
     */
    public static function getLastInfo()
    {
        return self::find()
            ->andWhere(['status' => self::STATUS_ONLINE])
            ->orderBy('add_time desc')
            ->limit(1)
            ->asArray()
            ->one();
    }

    /**
     * 获取专业名称，只取1个+等号
     * @param $jobId
     * @return mixed|string
     * @throws \Exception
     */
    public static function getSimpleMajorText($jobId)
    {
        $majorText = self::findOneVal(['id' => $jobId], 'major_id');

        $majorIds = explode(',', $majorText);
        $length   = sizeof($majorIds);

        $major = "";

        if ($length > 0) {
            $major = BaseMajor::getAllMajorName($majorIds[0]);
            if ($length > 1) {
                $major .= "等";
            }
        }

        return $major;
    }

    /**
     * @throws \Exception
     */
    public static function getRecommendJobMajorText($jobId, $memberId)
    {
        $majorText = self::findOneVal(['id' => $jobId], 'major_id');

        $majorIds = explode(',', $majorText);

        $length = sizeof($majorIds);

        $major = "";

        //获取用户最高教育经历的专业名称
        $topMajorId = BaseResume::getTopEducationMajorId($memberId);
        //需要往上推1级，因为职位只到二级
        $topMajorParentId = BaseMajor::findOneVal(['id' => $topMajorId], 'parent_id');

        if ($length > 0) {
            if (!empty($topMajorId) && in_array($topMajorParentId, $majorIds)) {
                //判断是否在数组里
                $major = BaseMajor::getMajorName($topMajorId);
            } else {
                $major = BaseMajor::getAllMajorName($majorIds[0]);
            }

            if ($length > 1) {
                $major .= "等";
            }
        }

        return $major;
    }

    /**
     * 获取职位分析报告url
     */
    public static function getReportUrl($resumeId, $id)
    {
        $token = BaseResumeJobReportRecord::findOneVal([
            'resume_id' => $resumeId,
            'job_id'    => $id,
        ], 'token');

        return UrlHelper::toRoute([
            '/job/report',
            'id'    => $id,
            'token' => $token,
        ]);
    }

    /**
     * 根据端口获取职位详情数据
     * @param     $jobId
     * @param     $platformType
     * @param int $memberId
     * @return array
     * @throws Exception
     */
    public static function getDetailService($jobId, $platformType, $memberId = 0)
    {
        if ($jobId <= 0 || !in_array($platformType, BaseCommon::PLATFORM_TYPE_LIST)) {
            throw new Exception('参数错误');
        }
        //获取职位信息
        $job_info = BaseJob::findOne($jobId);
        if (empty($job_info)) {
            throw new Exception('职位不存在');
        }
        //获取单位信息
        $company_info = BaseCompany::findOne($job_info->company_id);
        //定义返回结果
        $result_info = [];
        //关键ID返回
        $result_info['job_id']               = $job_info->id;
        $result_info['company_id']           = $job_info->company_id;
        $result_info['company_name']         = $company_info->full_name;
        $result_info['company_package_type'] = $company_info->package_type;
        $result_info['job_category_id']      = $job_info->job_category_id;
        $result_info['city_id']              = $job_info->city_id;
        $result_info['job_amount']           = $job_info->amount;
        $result_info['department']           = $job_info->department;
        $result_info['is_establishment']     = $job_info->is_establishment;
        $result_info['status']               = $job_info->status;
        $result_info['name']                 = str_replace(PHP_EOL, '', $job_info->name);
        //格式化薪资
        $result_info['wage']                 = BaseJob::formatWage($job_info->min_wage, $job_info->max_wage,
            $job_info->wage_type);
        $welfareTagArr                       = BaseWelfareLabel::getWelfareLabelNameList($job_info->welfare_tag);
        $result_info['welfare_tag_arr']      = [];
        $result_info['more_welfare_tag_arr'] = [];
        if ($platformType == BaseCommon::PLATFORM_MINI) {
            $result_info['welfare_tag_arr'] = $welfareTagArr;
        } else {
            foreach ($welfareTagArr as $k => $v) {
                if ($k < 4) {
                    array_push($result_info['welfare_tag_arr'], $v);
                } else {
                    array_push($result_info['more_welfare_tag_arr'], $v);
                }
            }
        }

        $province = BaseArea::getAreaName($job_info->province_id);
        $city     = BaseArea::getAreaName($job_info->city_id);
        if ($province == $city) {
            $area = $city;
        } else {
            $area = $province . $city;
        }
        $result_info['full_area']          = $area . $job_info->address;
        $result_info['city']               = BaseArea::getAreaName($job_info->city_id);
        $result_info['province']           = BaseArea::getAreaName($job_info->province_id);
        $result_info['address']            = implode('-', [
            $result_info['province'],
            $result_info['city'],
        ]);
        $result_info['major_txt']          = BaseMajor::getAllMajorName(explode(',', $job_info->major_id));
        $result_info['major']              = BaseMajor::getAllMajorLinkList(explode(',', $job_info->major_id));
        $result_info['experience']         = BaseDictionary::getExperienceName($job_info->experience_type);
        $result_info['education']          = BaseDictionary::getEducationName($job_info->education_type);
        $result_info['jobCategory']        = BaseCategoryJob::getName($job_info->job_category_id);
        $result_info['jobNature']          = BaseDictionary::getNatureName($job_info->nature_type);
        $result_info['title']              = BaseDictionary::getTitleName($job_info->title_type);
        $result_info['political']          = BaseDictionary::getPoliticalStatusName($job_info->political_type);
        $result_info['abroad']             = BaseDictionary::getAbroadName($job_info->abroad_type);
        $result_info['scale']              = BaseDictionary::getCompanyScaleName($company_info->scale);
        $result_info['industry']           = BaseTrade::getIndustryName($company_info->industry_id);
        $result_info['nature']             = BaseDictionary::getCompanyNatureName($company_info->nature);
        $result_info['type']               = BaseDictionary::getCompanyTypeName($company_info->type);
        $result_info['age']                = $job_info->age_type ?: '';
        $result_info['logo']               = BaseCompany::getLogoFullUrl($company_info->logo_url);
        $result_info['refresh_time']       = TimeHelper::formatDateByYear($job_info->refresh_time);
        $result_info['establishment_name'] = BaseDictionary::getAllEstablishmentName(explode(',',
            $job_info->establishment_type));
        // 1.6直聊版本添加联系人信息
        $result_info['contactInfo'] = BaseJobContact::getInfoByJob($jobId);

        // 2.4 更多查询逻辑
        $result_info['more_search_params'] = [
            'major_id'        => strval($job_info->major_id),
            'major_txt'       => explode(',', $result_info['major_txt'])[0] ?? '',
            'education_type'  => strval($job_info->education_type),
            'education'       => $result_info['education'],
            'job_category_id' => strval($job_info->job_category_id),
            'jobCategory'     => $result_info['jobCategory'],
            'city_id'         => strval($job_info->city_id),
            'city'            => $result_info['city'],
            'job_name'        => $result_info['name'],
        ];

        //职位基础信息拼接
        $job_basics_info = [];
        //工作经验
        if ($result_info['experience']) {
            array_push($job_basics_info, $result_info['experience']);
        }
        //招聘人数
        if ($job_info->amount) {
            array_push($job_basics_info, '招' . $job_info->amount . '人');
        }
        //学历
        if ($result_info['education']) {
            array_push($job_basics_info, $result_info['education']);
        }
        //地区
        if ($result_info['city']) {
            array_push($job_basics_info, $result_info['city']);
        }
        $result_info['job_basics_info'] = implode(' | ', $job_basics_info);
        //单位基本信息拼接
        $company_basics_info = [];
        //单位性质
        if ($result_info['nature']) {
            array_push($company_basics_info, $result_info['nature']);
        }
        //单位类型
        if ($result_info['type']) {
            array_push($company_basics_info, $result_info['type']);
        }
        //单位规模
        if ($result_info['scale']) {
            array_push($company_basics_info, $result_info['scale']);
        }
        $result_info['company_basics_info'] = implode('·', $company_basics_info);

        //获取申请状态
        $result_info['apply_status'] = self::JOB_APPLY_STATUS_NO;
        //获取单位在招职位数量
        $result_info['job_amount'] = self::getCompanyJobAmount($company_info->id);
        //获取申请类型字段
        $result_info['is_email_apply']  = "false";
        $result_info['is_online_apply'] = false;
        $result_info['is_other_apply']  = false;
        //保留多一个id，用于判断
        $result_info['is_cooperation_id'] = $company_info->is_cooperation;

        //判断是否合作单位
        if ($company_info->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $result_info['is_cooperation'] = true;
            //获取单位简历查看率
            $result_info['viewing_rate'] = BaseJobApply::statCompanyViewingRate(['company_member_id' => $company_info->member_id]);
            //获取单位登录时间
            $result_info['last_login_time'] = BaseMemberActionLog::getCompanyLastLoginDateText($company_info->member_id);
        } else {
            $result_info['is_cooperation']  = false;
            $result_info['viewing_rate']    = '-';
            $result_info['last_login_time'] = '-';
        }
        //获取收藏状态
        $result_info['collect_status'] = self::JOB_COLLECT_STATUS_NO;
        //如果用户已经登录了，获取用户投递信息
        $result_info['percent'] = 0;
        //        if (!empty($memberId)) {
        //            $resume_info                 = BaseResume::findOne(['member_id' => $memberId]);
        //            $result_info['apply_status'] = BaseJobApplyRecord::checkJobApplyStatus($resume_info->id, $job_info->id);
        //            $isCollect                   = BaseJobCollect::checkIsCollect($memberId, $job_info->id);
        //            if ($isCollect) {
        //                $result_info['collect_status'] = self::JOB_COLLECT_STATUS_YES;
        //            }
        //        }
        $result_info['announcement_title'] = ''; //公告标题
        $result_info['column_info']        = ''; //栏目信息
        //获取公告信息，先判断是否有公告
        $file_ids                       = '';
        $result_info['announcement_id'] = 0;
        if ($job_info->announcement_id > 0) {
            $result_info['announcement_id']       = $job_info->announcement_id;
            $announcement_info                    = BaseAnnouncement::findOne($job_info->announcement_id);
            $file_ids                             = $announcement_info->file_ids;
            $result_info['announcement_file_ids'] = $announcement_info->file_ids;
            $result_info['announcement_title']    = $announcement_info->title;
            //获取栏目信息
            $result_info['column_info'] = BaseHomeColumn::getInfoListByAnnouncementId($job_info->announcement_id);
            //格式化日期
            if ($job_info->period_date == TimeHelper::ZERO_TIME) {
                if ($announcement_info->period_date && $announcement_info->period_date != TimeHelper::ZERO_TIME) {
                    $result_info['period_date'] = date('Y-m-d', strtotime($announcement_info->period_date));
                } else {
                    $result_info['period_date'] = '详见正文';
                }
            } else {
                $result_info['period_date'] = date('Y-m-d', strtotime($job_info->period_date));
            }
        }
        //反馈快
        $is_fast = 2;
        if ($job_info->delivery_type) {
            if ($job_info->delivery_type == BaseJob::DELIVERY_TYPE_OUTER) {
                $result_info['apply_type_text'] = BaseJob::getApplyTypeName($job_info->apply_type);
            } else {
                $result_info['apply_type_text'] = '站内投递';
            }
            if ($job_info->delivery_way == BaseJob::DELIVERY_WAY_PLATFORM) {
                $is_fast = 1;
            }
        } else {
            if ($job_info->announcement_id > 0) {
                if ($announcement_info->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                    $result_info['apply_type_text'] = BaseJob::getApplyTypeName($announcement_info->apply_type);
                } else {
                    $result_info['apply_type_text'] = '站内投递';
                }
                if ($job_info->delivery_way == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                    $is_fast = 1;
                }
            }
        }
        $result_info['is_fast'] = $is_fast;
        //置顶
        $is_top_bool = BaseJobTopConfig::getJobIsTop($job_info->id);
        if ($is_top_bool) {
            $result_info['is_top'] = 1;
        } else {
            $result_info['is_top'] = 2;
        }

        $result_info['gender_type'] = BaseResume::getGenderName($job_info->gender_type); //性别
        $releaseTime                = strtotime($job_info->release_time);
        $thisYearTime               = mktime(0, 0, 0, 1, 1, date('Y'));
        if ($releaseTime > $thisYearTime) {
            $result_info['release_time'] = date('m-d', $releaseTime);
        } else {
            $result_info['release_time'] = date('Y-m-d', $releaseTime);
        }

        // 换行前端显示
        $result_info['requirement'] = str_replace("\n", '<br>', $job_info->requirement);
        $result_info['remark']      = str_replace("\n", '<br>', $job_info->remark);
        $result_info['duty']        = str_replace("\n", '<br>', $job_info->duty);

        //查看公告是否有附件需要显示
        $result_info['file_list'] = [];
        if (!empty($job_info->file_ids)) {
            $file_ids = $job_info->file_ids;
        }
        if (!empty($file_ids)) {
            $fileIdsArr = explode(',', $file_ids);
            $file_data  = BaseFile::getIdsList($fileIdsArr);
            $fileArr    = [];
            foreach ($file_data as &$value) {
                if (!empty($value['path'])) {
                    $item['path']   = FileHelper::getFullUrl($value['path']);
                    $item['name']   = $value['name'];
                    $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                    $item['id']     = $value['id'];
                    array_push($fileArr, $item);
                }
            }
            $result_info['file_list'] = $fileArr;
        }
        //预留各端有特殊处理的地方
        //        switch ($platformType) {
        //            case BaseCommon::PLATFORM_WEB:
        //            case BaseCommon::PLATFORM_WEB_PERSON:
        //                break;
        //            case BaseCommon::PLATFORM_WEB_COMPANY:
        //                break;
        //            case BaseCommon::PLATFORM_H5:
        //                break;
        //            case BaseCommon::PLATFORM_MINI:
        //                break;
        //            case BaseCommon::PLATFORM_APP:
        //                break;
        //            default:
        //                break;
        //        }

        return $result_info;
    }

    /**
     * 获取职位详情页推荐职位
     * @param array $whereData    根据规则传入需要数据
     * @param int   $platformType 来源平台
     * @return array
     * @throws Exception
     */
    public static function getDetailRecommendService($whereData, $platformType)
    {
        //判断参数合法性
        if (empty($whereData) || !in_array($platformType, BaseCommon::PLATFORM_TYPE_LIST)) {
            throw new Exception('参数错误');
        }
        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
            ->leftJoin(['t' => BaseJobTopConfig::tableName()],
                't.job_id = j.id and t.status = ' . BaseJobTopConfig::STATUS_ACTIVE)
            ->andWhere(['j.status' => BaseJob::STATUS_ACTIVE])
            ->select([
                'j.id as job_id',
                'j.status',
                'j.name as job_name',
                'j.company_id',
                'j.min_wage',
                'j.max_wage',
                'j.wage_type',
                'j.experience_type',
                'j.education_type',
                'j.amount',
                'j.job_category_id',
                'j.province_id',
                'j.city_id',
                'c.full_name as company_name',
                'c.type as company_type',
                'c.nature as company_nature',
                'j.announcement_id',
                'a.title as announcement_name',
                'j.major_id',
                'j.refresh_time',
            ]);
        //各端有特殊处理的地方
        switch ($platformType) {
            case BaseCommon::PLATFORM_MINI:
                $max_recommend_number = 18;
                //定义一下规则
                //1、优先展示该单位最新发布、与该职位类型相同的在线职位，置顶职位优先，最多6条。
                //2、展示与该职位职位类型相同、工作地点相同的，会员类型为高级会员的合作单位，最新发布的在线职位，置顶职位优先，最多18条。
                //3、展示与该职位职位类型相同、工作地点相同的，最新发布的在线职位，置顶职位优先。
                $rule = [
                    'rule_1' => [
                        'where' => [
                            'j.job_category_id' => $whereData['job_category_id'],
                            'j.company_id'      => $whereData['company_id'],
                        ],
                        'order' => [
                            't.status'       => SORT_DESC,
                            'j.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 6,
                    ],
                    'rule_2' => [
                        'where' => [
                            'j.job_category_id' => $whereData['job_category_id'],
                            'j.city_id'         => $whereData['city_id'],
                            'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
                            'c.package_type'    => BaseCompany::PACKAGE_TYPE_SENIOR,
                        ],
                        'order' => [
                            't.status'       => SORT_DESC,
                            'j.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 18,
                    ],
                    'rule_3' => [
                        'where' => [
                            'j.job_category_id' => $whereData['job_category_id'],
                            'j.city_id'         => $whereData['city_id'],
                        ],
                        'order' => [
                            't.status'       => SORT_DESC,
                            'j.refresh_time' => SORT_DESC,
                        ],
                        'limit' => 18,
                    ],
                ];
                break;
            default:
                return [];
                break;
        }
        //如果规则空就直接返回空
        if (empty($rule)) {
            return [];
        }

        //定义一下获取职位ID数组及列表
        $job_id_arr = [];
        $list       = [];
        //根据循环去获取符合规则的职位ID push到职位ID数组中去
        foreach ($rule as $value) {
            $limit      = $max_recommend_number - count($job_id_arr);
            $limit      = $limit < 0 ? $max_recommend_number : $limit;
            $limit      = $limit > $value['limit'] ? $value['limit'] : $limit;
            $query_item = clone $query;
            //$job_id_arr 不为空则剔除
            if (!empty($job_id_arr)) {
                $query_item->andWhere([
                    'not in',
                    'j.id',
                    $job_id_arr,
                ]);
            }
            $job_data_item = $query_item->andFilterWhere($value['where'])
                ->orderBy($value['order'])
                ->limit($limit)
                ->asArray()
                ->all();
            unset($query_item);
            $list         = array_merge($list, $job_data_item);
            $job_ids_item = array_column($job_data_item, 'job_id');
            $job_id_arr   = array_merge($job_id_arr, $job_ids_item);
            if (count($job_id_arr) >= $max_recommend_number) {
                break;
            }
        }
        foreach ($list as &$job) {
            $job['education_name']  = BaseDictionary::getEducationName($job['education_type']);
            $job['experience_name'] = BaseDictionary::getExperienceName($job['experience_type']);
            $job['city_name']       = BaseArea::getAreaName($job['city_id']);
            $job['refresh_time']    = TimeHelper::formatDateByYear($job['refresh_time']);
            //获取单位类型
            $job['company_type_name'] = BaseDictionary::getCompanyTypeName($job['company_type']);
            //获取单位性质
            $job['company_nature_name'] = BaseDictionary::getCompanyNatureName($job['company_nature']);
            //拼接工资
            if ($job['min_wage'] == 0 && $job['max_wage'] == 0) {
                $job['wage_name'] = '面议';
            } else {
                $job['wage_name'] = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
            }
            //这里可以处理一些端口特殊返回字段
        }

        return $list;
    }

    // 这个接口是给前端职位筛选面板的复合搜索使用
    public static function getMiniJobSearchParams()
    {
        $cacheKey = Cache::MINI_JOB_SEARCH_PARAMS_KEY;

        $data = Cache::get($cacheKey);

        if ($data) {
            return json_decode($data, true);
        }

        $config = [
            'educationType'   => [
                'name' => '学历要求',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getEducationList()),
            ],
            // 单位类型
            'companyType'     => [
                'name' => '单位类型',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getCompanyTypeList()),
            ],
            'companyNature'   => [
                'name' => '单位性质',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getCompanyNatureList()),
            ],
            'releaseTimeType' => [
                'name' => '发布时间',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getReleaseTimeList()),
            ],
            // 职称要求
            'titleType'       => [
                'name' => '职称要求',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getFirstTitleList()),
            ],
            // 工作性质
            'natureType'      => [
                'name' => '工作性质',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getNatureList()),
            ],
            // 经验要求
            'experienceType'  => [
                'name' => '经验要求',
                'type' => 'checkbox',
                'list' => ArrayHelper::obj2Arr(BaseDictionary::getExperienceList()),
            ],
        ];

        $data = [];

        // 不再需要了，让前端处理
        // $data[] = [
        //     'k'        => '',
        //     'v'        => '不限',
        //     'children' => [
        //         [
        //             'v' => '不限',
        //             'k' => '',
        //         ],
        //     ],
        // ];

        $data[] = [
            'k'        => '0',
            'v'        => '热门搜索',
            'children' => [
                [
                    'v' => '反馈快',
                    'k' => 'isFast_1',
                ],
                [
                    'v' => '应届生',
                    'k' => 'isFresh_1',
                ],
            ],
        ];
        $i      = 1;
        foreach ($config as $k => $item) {
            $data[] = [
                'k'        => (string)$i++,
                'v'        => $item['name'],
                'children' => self::setListKey($item['list'], $k),
            ];
        }

        // 缓存1天
        Cache::set($cacheKey, json_encode($data), 86400);

        return $data;
    }

    private static function setListKey($list, $key)
    {
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'k' => $key . '_' . $item['k'],
                'v' => $item['v'],
            ];
        }

        return $data;
    }

    public static function getDetailMiniCode($id)
    {
        // 先去缓存里面取
        $cacheKey = Cache::MINI_CODE_KEY . ':' . WxMiniApp::QRCODE_PATH_TYPE_JOB . ':' . $id;
        if ($url = Cache::get($cacheKey)) {
            return $url;
        }

        $mini = WxMiniApp::getInstance();
        $url  = $mini->createQrCodeByType(WxMiniApp::QRCODE_PATH_TYPE_JOB, $id);

        Cache::set($cacheKey, $url);

        return $url;
    }

    /**
     * 获取职位联系人
     * @param $jobId
     * @return array|\yii\db\ActiveRecord
     */
    public static function getJobContact($jobId)
    {
        $data = BaseJobContact::find()
            ->alias('jc')
            ->select([
                'jc.id',
                'jc.job_id',
                'jc.company_member_info_id',
                'cmi.member_id',
                'cmi.company_member_type',
                'cmi.contact',
                'cmi.department',
                'm.email',
                'm.mobile',
            ])
            ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.id = jc.company_member_info_id')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->where(['jc.job_id' => $jobId])
            ->asArray()
            ->one();
        //        if ($data) {
        //            $data['contact_department'] = $data['contact'];
        //            if ($data['department']) {
        //                $data['contact_department'] .= ' /'.$data['department'];
        //            }
        //        }

        return $data ?: [];
    }

    /**
     * 获取职位协同账号联系人
     * @param     $jobId
     * @param int $needPrimaryAccount
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getJobContactSynergy($jobId, int $needPrimaryAccount = 2): array
    {
        $data = BaseJobContactSynergy::find()
            ->alias('jc')
            ->select([
                'jc.id',
                'jc.job_id',
                'jc.company_member_info_id',
                'cmi.member_id',
                'cmi.company_member_type',
                'cmi.contact',
                'cmi.department',
                'm.email',
                'm.mobile',
            ])
            ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.id = jc.company_member_info_id')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
            ->where(['jc.job_id' => $jobId])
            ->asArray()
            ->all();

        //主/发账号信息
        if ($needPrimaryAccount == 1) {
            $memberId           = BaseJob::findOneVal(['id' => $jobId], 'member_id');
            $primaryAccountList = BaseCompanyMemberInfo::find()
                ->alias('cmi')
                ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
                ->select([
                    'cmi.id as company_member_info_id',
                    'cmi.member_id',
                    'cmi.company_member_type',
                    'cmi.contact',
                    'cmi.department',
                    'm.email',
                    'm.mobile',
                ])
                ->where(['cmi.member_id' => $memberId])
                ->asArray()
                ->one();
            array_unshift($data, $primaryAccountList);
        }

        //在列表中塞入一个is_contact字段,用来判断是否是主联系人
        $contactId = BaseJobContact::find()
            ->select(['company_member_info_id'])
            ->where(['job_id' => $jobId])
            ->scalar();
        foreach ($data as &$item) {
            $item['is_contact'] = 0;
            if ($item['company_member_info_id'] == $contactId) {
                $item['is_contact'] = 1;
            }
        }

        return $data;
    }

    /**
     * 设置公告下的职位审核状态
     * @throws Exception
     */
    public static function setJobAuditStatus($announcementId)
    {
        // 获取公告原职位并修改状态
        $jobData = BaseJob::find()
            ->select('id')
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->asArray()
            ->all();

        foreach ($jobData as $item) {
            $jobIdArr[] = $item['id'];
        }

        //修改职位状态
        $updateStatusArray = [
            'audit_status'     => self::AUDIT_STATUS_WAIT_AUDIT,
            'apply_audit_time' => CUR_DATETIME,
        ];
        if (!BaseJob::updateAll($updateStatusArray, [
            'in',
            'id',
            $jobIdArr,
        ])) {
            throw new Exception('职位审核状态修改失败');
        }
    }

    /**
     * 获取薪资文案
     * @param $jobId
     * @return string
     */
    public static function getWageText($jobId)
    {
        $jobInfo = self::findOne($jobId);
        if ($jobInfo->min_wage == 0 && $jobInfo->max_wage == 0) {
            return '面议';
        } else {
            return self::formatWage($jobInfo->min_wage, $jobInfo->max_wage, $jobInfo->wage_type);
        }
    }

    /**
     * 获取职位卡片信息
     * @param $jobId
     * @return array
     * @throws \Exception
     */
    public static function getJobCardInfo($jobId)
    {
        $jobInfo = self::findOne($jobId);

        if (!$jobInfo) {
            return [];
        }
        $card['jobName']  = $jobInfo->name;
        $card['wage']     = BaseJob::getWageText($jobId);
        $card['areaName'] = BaseArea::getAreaName($jobInfo->province_id) . '-' . BaseArea::getAreaName($jobInfo->city_id);
        $card['cityName'] = BaseArea::getAreaName($jobInfo->city_id);
        //获取学历水平
        $card['education'] = BaseDictionary::getEducationName($jobInfo->education_type);
        //招聘人数
        $card['amount'] = '招' . $jobInfo->amount . '人';
        //发布时间
        //处理发布时间
        $thisYearTime        = mktime(0, 0, 0, 1, 1, date('Y'));
        $refreshDate         = strtotime($jobInfo->refresh_date);
        $date                = $refreshDate > $thisYearTime ? date('m-d', $refreshDate) : date('Y-m-d', $refreshDate);
        $card['refreshDate'] = $date . '发布';
        //小程序发布时间
        $card['simpleRefreshDate'] = $date;

        $visitUrl    = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        $card['url'] = $visitUrl . '/job/detail/' . $jobId . '.html';
        //公告名称
        $card['announcementTitle'] = BaseAnnouncement::findOneVal(['id' => $jobInfo->announcement_id], 'title') ?: '';
        //单位名称
        $card['companyName'] = BaseCompany::findOneVal(['id' => $jobInfo->company_id], 'full_name');
        //获取经验要求
        $card['experience'] = BaseDictionary::getExperienceName($jobInfo->experience_type);

        return $card;
    }

    /**
     * 获取职位的投递方式
     * @param $jobId
     * @return string
     */
    public static function getDeliveryWay($jobId)
    {
        $jobInfo = self::findOne($jobId);
        if (empty($jobInfo->delivery_way)) {//职位没有就显示公告的投递类型
            if (!empty($jobInfo->announcement_id)) {
                $deliveryWay = BaseAnnouncement::findOneVal(['id' => $jobInfo->announcement_id], 'delivery_way');
                if (in_array($deliveryWay, BaseAnnouncement::DELIVERY_WAY)) {
                    return $deliveryWay;
                }
            }
        } else {//显示职位的投递类型
            if (in_array($jobInfo->delivery_way, BaseJob::DELIVERY_WAY)) {
                return $jobInfo->delivery_way;
            }
        }
    }

    // 获取投递方式文案
    public static function getDeliverTypeTxt($jobId)
    {
        $jobDeliveryWay = self::findOneVal(['id' => $jobId], 'delivery_way');
        $jobApplyType   = self::findOneVal(['id' => $jobId], 'apply_type');

        $applyTypeTxt = '';
        if ($jobDeliveryWay > 0) {
            if ($jobApplyType) {
                $applyTypeArr = explode(',', $jobApplyType);
                //如果是其他投递
                $applyTypeName = '';
                foreach ($applyTypeArr as $item) {
                    $applyTypeName .= BaseDictionary::getDataName(BaseDictionary::TYPE_SIGN_UP, $item) . '/';
                }
                $applyTypeTxt = trim($applyTypeName, '/');
            }
        } else {
            $announcementId          = self::findOneVal(['id' => $jobId], 'announcement_id');
            $announcementDeliveryWay = BaseAnnouncement::findOneVal(['id' => $announcementId], 'delivery_way');
            $jobApplyType            = BaseAnnouncement::findOneVal(['id' => $announcementId], 'apply_type');
            if ($announcementDeliveryWay > 0) {
                if ($jobApplyType) {
                    $applyTypeArr = explode(',', $jobApplyType);
                    //如果是其他投递
                    $applyTypeName = '';
                    foreach ($applyTypeArr as $item) {
                        $applyTypeName .= BaseDictionary::getDataName(BaseDictionary::TYPE_SIGN_UP, $item) . '/';
                    }
                    $applyTypeTxt = trim($applyTypeName, '/');
                }
            }
        }

        return $applyTypeTxt;
    }

    public static function updateCompanyJobSort($companyId)
    {
        // 更改单位下面所有职位的排序
        $sort = BaseCompany::findOneVal(['id' => $companyId], 'sort');
        BaseJob::updateAll([
            'company_sort' => $sort,
        ], ['company_id' => $companyId]);
    }

    /**
     * 获取职位投递热度类型
     * @param $jobId
     * @return int
     */
    public static function getApplyHeatType($jobId)
    {
        //3、职位热度查询：即该职位近90天的投递热度情况。
        //3.2 职位热度 判定规则：（同「竞争力分析报告-投递热度」模块的判断规则）
        //     获取在线职位近90天的投递人数y、招聘人数x（招聘人数为“若干”，按x=1处理）
        //      a. 不选中：即不筛选职位热度；
        //      b. 选中“一般”：筛选 y=0 的在线职位；
        //      c. 选中“较热”：筛选 0 < y ≤ 5x 的在线职位；
        //      d. 选中“火爆”：筛选 y > 5x 的在线职位；
        //PS：同「竞争力分析报告-投递热度」模块的判断规则

        // 当前职位90天内的投递人数
        $yNumber = BaseJobApplyRecord::find()
            ->select('id')
            ->where(['job_id' => $jobId])
            ->andWhere([
                'between',
                'add_time',
                date('Y-m-d', strtotime('-' . 90 . ' day')) . ' 00:00:00',
                date('Y-m-d', time()) . ' 23:59:59',
            ])
            ->count();

        $jobInfo = BaseJob::find()
            ->select([
                'amount',
            ])
            ->where([
                'id' => $jobId,
            ])
            ->asArray()
            ->one();

        if ($jobInfo['amount'] == '若干') {
            $xNumber = 1;
        } else {
            $xNumber = $jobInfo['amount'];
        }

        switch (true) {
            case $yNumber == 0:
                $applyHeatType = self::APPLY_HEAT_TYPE_PRIMARY;
                break;
            case 0 < $yNumber && $yNumber <= 5 * $xNumber:
                $applyHeatType = self::APPLY_HEAT_TYPE_HOT;
                break;
            case $yNumber > 5 * $xNumber:
                $applyHeatType = self::APPLY_HEAT_TYPE_EXPLOSIVE;
                break;
            default:
                $applyHeatType = 0;
                break;
        }

        return $applyHeatType;
    }

    /**
     * 获取职位列表里的广告卡片信息
     * @param $pageAmount
     * @param $memberId
     * @return array
     */
    public static function getListShowcaseInfo($pageAmount, $memberId, $showType)
    {
        $defaultInfo = [
            'position' => 0,
            'url'      => '',
            'img'      => '',
        ];
        if ($pageAmount == 0) {
            return $defaultInfo;
        }
        $showPosition = 0;

        switch ($showType) {
            case self::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST:
            case self::VIP_SHOWCASE_POSITION_TYPE_PC_PERSON:
                //一、在第15个职位下方，新增“信息流推荐卡片”（不受筛选条件影响）：
                //1、若列表职位数量≤5时，则不显示卡片；
                //    若 5<列表职位数量<15时，直接显示在最后一个职位的后面；
                //    若列表职位数量≥15时，显示在第15个职位的后面；
                switch ($pageAmount) {
                    case $pageAmount <= 5:
                        break 2;
                    case $pageAmount > 5 && $pageAmount < 15:
                        $showPosition = $pageAmount;
                        break 2;
                    case  $pageAmount >= 15:
                        $showPosition = 15;
                        break 2;
                }
            /////////////////////////////////     H5部分比较特殊，返回需要-1    /////////////////////////////////
            case self::VIP_SHOWCASE_POSITION_TYPE_H5_JOB_LIST:
                //在第7个职位下方（不含“为你推荐”模块），新增“信息流推荐卡片”：
                //1、若 列表职位数量<7时，直接显示在最后一个职位的后面；
                //    若列表职位数量=0时，则不显示卡片；
                switch ($pageAmount) {
                    case $pageAmount > 0 && $pageAmount < 7:
                        $showPosition = $pageAmount - 1;
                        break 2;
                    case $pageAmount >= 7:
                        $showPosition = 6;
                        break 2;
                }

            case self::VIP_SHOWCASE_POSITION_TYPE_H5_INDEX:
            case self::VIP_SHOWCASE_POSITION_TYPE_H5_FIRST_COLUMN:
            case self::VIP_SHOWCASE_POSITION_TYPE_H5_SECOND_COLUMN:
                //最新更新tab下，在第9个公告下方，新增“信息流推荐卡片”
                //1、若 列表公告数量<9时，直接显示在最后一个公告的后面；
                //    若列表公告数量=0时，则不显示卡片；
                //2、卡片内容（满足条件时，二选一随机展示）
                //（1）未登录，or已登录且无生效中VIP时，显示卡片1；
                //       点击跳转【VIP介绍页】；
                //       有生效中的VIP时，不显示；
                //（2）未登录，or已登录且无生效中的求职快时，显示卡片2；
                //       点击跳转【求职快介绍页】；
                //       有生效中的求职快套餐时，不显示；
                //首页
                switch ($pageAmount) {
                    case $pageAmount > 0 && $pageAmount < 9:
                        $showPosition = $pageAmount - 1;
                        break 2;
                    case $pageAmount >= 9:
                        $showPosition = 8;
                        break 2;
                }
            case self::VIP_SHOWCASE_POSITION_TYPE_MINI_JOB_LIST:
                //在第7个职位下方（不含“为你推荐”模块），新增“信息流推荐卡片”：
                //1、若 列表职位数量<7时，直接显示在最后一个职位的后面；
                //    若列表职位数量=0时，则不显示卡片；
                switch ($pageAmount) {
                    case $pageAmount > 0 && $pageAmount < 7:
                        $showPosition = $pageAmount;
                        break 2;
                    case $pageAmount >= 7:
                        $showPosition = 7;
                        break 2;
                }
            case self::VIP_SHOWCASE_POSITION_TYPE_MINI_ANNOUNCEMENT:
                switch ($pageAmount) {
                    case $pageAmount > 0 && $pageAmount < 9:
                        $showPosition = $pageAmount;
                        break 2;
                    case $pageAmount >= 9:
                        $showPosition = 9;
                        break 2;
                }
        }

        if ($showPosition == 0 && in_array($showType, self::VIP_SHOWCASE_BELONG_PLATFORM['pc'])) {
            //在移动端等于0，也要展示广告，在pc端等于0不用展示，这里做判断
            return $defaultInfo;
        }

        //2、卡片内容（满足条件时，二选一随机展示）
        //（1）未登录，or已登录且无生效中VIP时，显示卡片1；
        //       点击跳转【VIP介绍页】；
        //       有生效中的VIP时，不显示；
        //（2）未登录，or已登录且无生效中的求职快时，显示卡片2；
        //       点击跳转【求职快介绍页】；
        //       有生效中的求职快套餐时，不显示；
        //3、职位列表页的所有状态，同步新增；
        // （PS：不受筛选条件影响）
        if (empty($memberId)) {
            $randKey  = array_rand(self::VIP_SHOWCASE_INFO_LIST[$showType]);
            $cardInfo = self::VIP_SHOWCASE_INFO_LIST[$showType][$randKey];
        } else {
            $resumeId = BaseMember::getMainId($memberId);
            //登陆了，判断是否存在vip、求职快
            $isJobFast = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                $resumeId);
            $isVip     = BaseResume::checkVip($memberId);
            switch (true) {
                case $isJobFast && $isVip:
                    return $defaultInfo;
                case !$isJobFast && $isVip:
                    $cardInfo = self::VIP_SHOWCASE_INFO_LIST[$showType]['jobFast'];
                    break;
                case $isJobFast && !$isVip:
                    $cardInfo = self::VIP_SHOWCASE_INFO_LIST[$showType]['vip'];
                    break;
                case !$isJobFast && !$isVip:
                    $randKey  = array_rand(self::VIP_SHOWCASE_INFO_LIST[$showType]);
                    $cardInfo = self::VIP_SHOWCASE_INFO_LIST[$showType][$randKey];
            }
        }

        return [
            'position' => $showPosition ?: 0,
            'url'      => $cardInfo['url'] ?: '',
            'img'      => $cardInfo['img'] ?: '',
        ];
    }

    /**
     * 根据单位获取一条新闻
     * @param $companyId
     * @param $field
     * @param $orderBy
     */
    public static function getOneJobByCompanyId($companyId, $field, $orderBy = 'refresh_time desc, id desc')
    {
        return BaseJob::find()
            ->where(['company_id' => $companyId])
            ->select($field)
            ->orderBy($orderBy)
            ->asArray()
            ->one();
    }

    // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1137 博士后  特别研究助理
    public static function handelJobTypeUrlForDetail($jobCategoryId)
    {
        // 必须是pc端
        $platform = PLATFORM;

        if ($platform === 'PC' && in_array($jobCategoryId, [
                29,
                263,
            ])) {
            return UrlHelper::getBoShiHouHome();
        }

        return UrlHelper::createUrlParamsPath('/job', ['jobType' => $jobCategoryId]);
    }

    /**
     * @param $pageAmount
     * @param $memberId
     * @param $showType
     * 1.原展示逻辑暂不生效，改为：默认展示高才博士后，不受登录状态或购买权益影响（相当于直接替换现有卡片）
     *
     * 2.展示位置改为：在第10个职位下方展示（不受筛选条件影响），具体如下：
     *
     * 若列表职位数量≤5时，则不显示卡片；
     *
     * 若 5<列表职位数量≤10时，直接显示在最后一个职位的后面；
     *
     * 若列表职位数量>10时，显示在第10个职位的后面
     *
     *  img  "https://img.gaoxiaojob.com/uploads/static/showcase/job-quick.png?t=20240701"
     *  url
     *  position
     *
     *                 https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1187
    */
    public static function getBoshihouBanner($pageAmount, $memberId, $showType)
    {
        if ($pageAmount > 5 && $pageAmount <= 10) {
            $showPosition = $pageAmount;
        } elseif ($pageAmount > 10) {
            $showPosition = 10;
        } else {
            return [];
        }

        $url = UrlHelper::getBoShiHouHome();
        $img = 'https://img.gaoxiaojob.com/uploads/static/image/tmp/boshihou_job_banner.jpg';

        return [
            'position' => $showPosition,
            'url'      => $url,
            'img'      => $img,
        ];


    }

}