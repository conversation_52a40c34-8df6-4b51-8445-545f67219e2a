<?php

namespace common\service\downloadTask;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseTrade;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\Excel;
use common\models\Admin;
use common\models\CompanyContact;
use common\models\Member;
use Yii;

class MyCompanyListService
{

    public $params;

    public $adminId;

    public $adminCompanyList = [];

    public function setParams($params)
    {
        $this->params = $params;

        // 根据adminId去找是否是销售人员
        $admin         = BaseAdmin::findOne($params['adminId']);
        $this->adminId = $admin->id;
        // 找到全部自己的单位
        if ($this->adminId == 1) {
            $companyArray = BaseCompany::find()
                ->select('id')
                ->where([
                    'status'         => BaseCompany::STATUS_ACTIVE,
                    'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                ])
                ->asArray()
                ->column();
        } else {
            $companyArray = BaseCompany::find()
                ->select('id')
                ->where(['admin_id' => $admin->id])
                ->asArray()
                ->column();
        }

        // 如果是销售人员,则只能看到自己的单位
        $this->adminCompanyList = $companyArray;

        return $this;
    }

    public function run()
    {
        $select = [
            'c.id',
            'c.member_id',
            'c.full_name',
            'c.admin_id',
            'c.province_id',
            'c.city_id',
            'c.district_id',
            'c.nature',
            'c.type',
            'c.industry_id',
            'c.source_type',
            'c.admin_id',
            'c.create_admin_id',
            'c.is_cooperation',
            'c.status as statusCompany',
            'c.address',
            'c.package_type',
            'c.click',
            'm.status as statusMember',
            'm.username',
            'm.mobile',
            'm.add_time',
            'm.last_active_time',
            'm.last_login_time',
            'cc.name as contact',
            'cc.mobile as cMobile',
        ];

        $query = BaseCompany::find()
            ->alias('c')
            ->leftJoin(['cc' => CompanyContact::tableName()], 'cc.company_id = c.id')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id = c.member_id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = c.admin_id')
            ->where(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);

        if ($this->adminId) {
            $query->andWhere(['c.id' => $this->adminCompanyList]);
        }

        // 审核已通过单位
        $query->andFilterWhere([
            'c.status' => BaseCompany::STATUS_ACTIVE,
            'm.status' => [
                BaseMember::STATUS_ACTIVE,
                BaseMember::STATUS_ILLEGAL,
            ],
        ]);

        //所在地区

        //创建人帐号
        if (!empty($params['createUserName'])) {
            $createMemberId = Member::findOneVal(['username' => $params['createUserName']], 'id');
            $createAdminId  = Admin::findOneVal(['username' => $params['createUserName']], 'id');

            if ($createMemberId) {
                //自主创建
                $query->andFilterWhere([
                    'c.create_admin_id' => $createMemberId,
                ]);
            } else {
                //运营创建
                $query->andFilterWhere([
                    'c.create_admin_id' => $createAdminId,
                ]);
            }
        }

        $orderBy = 'm.id desc';

        $list = $query->select($select)
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        foreach ($list as &$firstItem) {
            $firstItem['packageNameTxt']  = BaseCompany::PACKAGE_TYPE_LIST[$firstItem['package_type']];
            $firstItem['uid']             = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $firstItem['id']);
            $firstItem['natureTxt']       = BaseDictionary::getCompanyNatureName($firstItem['nature']);
            $firstItem['industryTxt']     = BaseTrade::getIndustryName($firstItem['industry_id']);
            $firstItem['typeTxt']         = BaseDictionary::getCompanyTypeName($firstItem['type']);
            $firstItem['statusMemberTxt'] = BaseMember::COMPANY_ACCOUNTS_STATUS_LIST[$firstItem['statusMember']];
            $firstItem['sourceTypeName']  = BaseCompany::TYPE_SOURCE_LIST[$firstItem['source_type']];
            $firstItem['name']            = BaseAdmin::findOneVal(['id' => $firstItem['admin_id']], 'name') ?: '';
            $firstItem['address']         = str_replace(',', '',
                $areaCache[$firstItem['city_id']]['full_name'] . $firstItem['address']);

            if ($firstItem['source_type'] == BaseCompany::TYPE_SOURCE_ADD) {
                $firstItem['createUserName'] = Admin::findOneVal(['id' => $firstItem['create_admin_id']], 'name') ?: '';
            } else {
                $firstItem['createUserName'] = BaseMember::findOneVal(['id' => $firstItem['create_admin_id']],
                    'username') ?: '';
            }
            // 如果是邮箱注册回显联系号码
            $firstItem['mobile']      = $firstItem['cMobile'] ?: $firstItem['mMobile'];
            $firstItem['viewingRate'] = BaseCompany::statCompanyViewingRate($firstItem['id']);
        }

        $headers = [
            '单位名称',
            '联系人',
            '会员类型',
            '创建时间',
            '活跃时间',
            '最近登陆',
            '单位性质',
            '单位类型',
            '所属行业',
            '业务员',
            '简历查看率',
            '总投递量',
            '硕士总投递次数',
            '博士总投递次数',
            '邮件/平台投递量',
            '硕士邮件/平台投递次数',
            '博士邮件/平台投递次数',
            '网址投递量',
            '硕士网址投递次数',
            '博士网址投递次数',
            '登录次数',
            '简历下载点数',
            '查看简历数',
            '收藏人才数',
            '简历分享次数',
            '邀约投递数',
            '简历扣点下载数',
            '单位主页的收藏量',
            '单位主页的阅读量',
            '公告发布数量',
            '公告阅读次数',
            '公告收藏数量',
            '职位发布个数',
            '职位收藏量',
            '职位总阅读量',
            '职位刷新次数',
        ];

        $data = [];

        foreach ($list as $item) {
            // 剩余简历下载点数	查看简历数（去重，查看同一用户简历记为一次）	收藏人才数	简历分享次数	邀约投递数	简历扣点下载数
            $item = array_merge($item, BaseCompany::resumeLibraryData($item['id']));
            // 偷偷添加一些运营数据,站外投递,站内投递
            // 找到站外投递次数,站内投递次数
            $apply_data = BaseCompany::getCompanyApply($item['id']);

            $loginCount = BaseMemberActionLog::find()
                ->where([
                    'member_id' => $item['member_id'],
                    'is_login'  => BaseMemberActionLog::IS_LOGIN_YES,
                ])
                ->count();

            // 收藏量
            $collectCount = BaseCompanyCollect::find()
                ->where([
                    'company_id' => $item['id'],
                ])
                ->count();

            // 找到所有有审核通过历史的公告和职位
            $announcementIds = BaseAnnouncement::find()
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
                ->select('a.id,b.click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '<>',
                    'b.refresh_time',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            $announcementCollectCount = 0;
            $announcementClickCount   = 0;
            $jobCollectCount          = 0;
            $jobRefreshCount          = 0;
            $jobClickCount            = 0;

            // 找职位
            $jobIds = BaseJob::find()
                ->select('id,click')
                ->where([
                    'company_id' => $item['id'],
                ])
                ->andWhere([
                    '<>',
                    'refresh_time',
                    TimeHelper::ZERO_TIME,
                ])
                ->asArray()
                ->all();

            // 循环公告去找收藏量
            foreach ($announcementIds as $announcementId) {
                $announcementClickCount   += $announcementId['click'];
                $announcementCollectCount += BaseAnnouncementCollect::find()
                    ->where([
                        'announcement_id' => $announcementId['id'],
                    ])
                    ->count();
            }

            // 循环职位去找收藏量和刷新量
            foreach ($jobIds as $jobId) {
                $jobClickCount   += $jobId['click'];
                $jobCollectCount += BaseJobCollect::find()
                    ->where([
                        'job_id' => $jobId['id'],
                    ])
                    ->count();
                $jobRefreshCount += BaseJobHandleLog::find()
                    ->where([
                        'job_id'      => $jobId['id'],
                        'handle_type' => BaseJobHandleLog::HANDLE_TYPE_REFRESH,
                    ])
                    ->count();
            }

            $data[] = [
                $item['full_name'],
                $item['contact'],
                $item['packageNameTxt'],
                $item['add_time'],
                $item['last_active_time'],
                $item['last_login_time'],
                $item['natureTxt'],
                $item['typeTxt'],
                $item['industryTxt'],
                $item['name'],
                $item['viewingRate'],
                $apply_data['applyAmount'],
                $apply_data['masterApplyAmount'],
                $apply_data['doctorApplyAmount'],
                $apply_data['emailPlatApplyAmount'],
                $apply_data['emailPlatMasterApplyAmount'],
                $apply_data['emailPlatDoctorApplyAmount'],
                $apply_data['linkApplyAmount'],
                $apply_data['linkMasterApplyAmount'],
                $apply_data['linkDoctorApplyAmount'],
                $loginCount,
                $item['remainResumeDownloadPoint'],
                $item['resumeView'],
                $item['resumeCollect'],
                $item['resumeShare'],
                $item['resumeInvite'],
                $item['consumeResumeDownloadPoint'],
                $collectCount,
                $item['click'],
                count($announcementIds),
                $announcementClickCount,
                $announcementCollectCount,
                count($jobIds),
                $jobCollectCount,
                $jobClickCount,
                $jobRefreshCount,
            ];
        }

        $sheet1 = [
            'sheetName' => '单位',
            'data'      => $data,
            'headers'   => $headers,
        ];

        // 开始找公告来处理
        $announcementList = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id = a.article_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id')
            ->select('a.id,b.click,b.title,full_name')
            ->where([
                'company_id' => $this->adminCompanyList,
                'a.status'   => BaseAnnouncement::STATUS_RECRUIT_ONLINE,
            ])
            ->orderBy('c.id desc,refresh_time')
            ->asArray()
            ->all();

        // 公告ID	公告标题	所属单位	招聘人数	招聘职位	在线职位	邮箱/平台投递次数	面试邀约	招聘状态	点击量
        $headers = [
            '公告链接',
            '公告标题',
            '所属单位',
            '招聘人数',
            '在线职位',
            '邮箱/平台投递次数',
            '招聘状态',
            '点击量',
        ];

        $data = [];

        foreach ($announcementList as $item) {
            // 找公告的在线职位数量
            $onlineJobAmount = BaseJob::find()
                ->where([
                    'announcement_id' => $item['id'],
                    'status'          => BaseJob::STATUS_ACTIVE,
                ])
                ->count();

            // 找投递次数
            $applyAmount = BaseJobApply::find()
                ->innerJoin(['b' => BaseJob::tableName()], 'b.id = job_id')
                ->where([
                    'announcement_id' => $item['id'],
                ])
                ->count();

            // 找招聘人数
            $recruitAmount = BaseJob::getAnnouncementJobRecruitAmount($item['id']);

            $data[] = [
                'www.gaoxiaojob.com' . UrlHelper::createAnnouncementDetailPath($item['id']),
                $item['title'],
                $item['full_name'],
                $recruitAmount ?: '若干',
                $onlineJobAmount,
                $applyAmount,
                '在线',
                $item['click'],
            ];
        }

        $sheet2 = [
            'sheetName' => '公告',
            'data'      => $data,
            'headers'   => $headers,
        ];

        $excel = new Excel();
        $excel->multipleSheet([$sheet1, $sheet2]);

        $saveFile = $excel->saveFile;

        return $saveFile;

        // bb($sheet2);
        // $excel    = new Excel();
        // $this->export($data, $headers);

        // try {
        //     $wxWork = new WxWork();
        //     $wxWork->downLoadMessage($adminId, $fileName, '单位列表');
        // } catch (\Exception $e) {
        // }

        // return [
        //     'excelUrl' => $fileName,
        // ];
    }

}
