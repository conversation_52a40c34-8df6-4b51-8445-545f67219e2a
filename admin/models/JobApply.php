<?php

namespace admin\models;

use common\base\models\BaseCompanyInterview;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use Yii;

class JobApply extends BaseJobapply
{
    /**
     * 获取站内投递列表
     * @param $searchData
     * @return array
     * @throws \Exception
     */
    public static function getApplyList($searchData)
    {
        $query = self::find()
            ->alias('ja')
            ->leftJoin(['c' => Company::tableName()], 'c.id = ja.company_id')
            ->leftJoin(['j' => Job::tableName()], 'j.id = ja.job_id')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'ja.id = jar.apply_id')
            ->leftJoin(['a' => Announcement::tableName()], 'a.id = j.announcement_id')
            ->andWhere(['jar.delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE]);

        //搜索关键词的类型
        $keyWordType = $searchData['keywordType'];
        //        //职位id
        //        $query->andFilterWhere(['job_id' => $searchData['jobId']]);
        //投递方式
        $query->andFilterWhere(['jar.delivery_way' => $searchData['deliveryWay']]);
        //用户id
        $query->andFilterWhere(['ja.resume_member_id' => $searchData['memberId']]);
        //附件简历id
        $query->andFilterWhere(['ja.resume_attachment_id' => $searchData['resumeAttachmentId']]);
        // 投递端口
        $query->andFilterWhere(['jar.platform' => $searchData['platform']]);
        //搜索内容
        if ($keyWordType == 1) {
            //职位id
            $query->andFilterWhere([
                'like',
                'ja.job_id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 2) {
            //职位名称
            $query->andFilterWhere([
                'like',
                'ja.job_name',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 3) {
            //公告id
            $query->andFilterWhere([
                'like',
                'a.id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 4) {
            //公告名称
            $query->andFilterWhere([
                'like',
                'a.title',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 5) {
            //单位id
            $query->andFilterWhere([
                'like',
                'ja.company_id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 6) {
            //单位名称
            $query->andFilterWhere([
                'like',
                'c.full_name',
                $searchData['keyword'],
            ]);
        }

        //投递时间
        $query->andFilterWhere([
            'between',
            'ja.add_time',
            TimeHelper::dayToBeginTime($searchData['startApplyTime']),
            TimeHelper::dayToEndTime($searchData['endApplyTime']),
        ]);
        //投递方式
        $query->andFilterWhere(['ja.source' => $searchData['source']]);

        $count    = $query->count();
        $pageSize = $searchData['limit']?:Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->select([
                'ja.job_id as jobId',
                'ja.job_name as jobName',
                'ja.company_id as companyId',
                'ja.resume_attachment_id as resumeAttachmentId',
                'ja.add_time as addTime',
                'ja.source',
                'ja.status',
                'ja.is_check',
                'jar.delivery_way',
                'ja.id',
                'a.title as announcementName',
                'a.id as announcementId',
                'c.full_name as companyName',
                'jar.platform',

            ])
            ->orderBy('ja.add_time desc')
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            //获取公告名称
            $record['resumeAttachmentName'] = ResumeAttachment::getNameById($record['resumeAttachmentId']) ?: '-';
            $record['source']               = self::SOURCE_LIST[$record['source']];
            $record['delivery_way_txt']     = BaseJobApplyRecord::DELIVERY_WAY_NAME[$record['delivery_way']];
            //获取投递端口名称
            $record['platformText']         = BaseJobApplyRecord::PLATFORM_LIST[$record['platform']];

            if ($record['status'] == self::STATUS_HANDLE_WAIT && $record['is_check'] == self::IS_CHECK_YES) {
                //当前状态为已查看
                $record['applyStatusTxt'] = self::PERSON_STATUS_LIST[self::STATUS_IS_CHECK];       //状态名称
            } else {
                $record['applyStatusTxt'] = self::PERSON_STATUS_LIST[$record['status']];           //状态名称
            }

            if (!empty($record['jobId']) && !empty($record['jobName'])) {
                //名称后面拼接uid
                $jobUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $record['jobId']);
                $record['jobName'] = $record['jobName'] . '(' . $jobUid . ')';
            }

            if (!empty($record['announcementId']) && !empty($record['announcementName'])) {
                $announcementUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT,
                    $record['announcementId']);
                $record['announcementName'] = $record['announcementName'] . '(' . $announcementUid . ')';
            }

            if (!empty($record['companyId']) && !empty($record['companyName'])) {
                $companyUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $record['companyId']);
                $record['companyName'] = $record['companyName'] . '(' . $companyUid . ')';
            }

            if ($record['status'] == self::STATUS_SEND_INVITATION) {
                //邀请面试状态，获取面试信息
                $record['interviewInfo'] = BaseCompanyInterview::find()
                    ->where(['job_apply_id' => $record['id']])
                    ->select([
                        'job_name',
                        'contact',
                        'telephone',
                        'interview_time',
                        'content',
                        'address',
                    ])
                    ->asArray()
                    ->one();
            }
        }
        $data = [
            'list' => $list,
            'page' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $searchData['page'],
            ],
        ];

        return $data;
    }

}
