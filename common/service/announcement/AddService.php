<?php

namespace common\service\announcement;

use admin\models\Company;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseFile;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseJob;

use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobTemp;
use common\base\models\BaseOffSiteJobApply;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use common\libs\BadWordCheck;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\models\HwActivityAnnouncement;
use common\models\HwActivityCompany;
use common\service\company\PushEditMessageService;
use common\service\zhaoPinHuiColumn\SpecialActivityService;
use Faker\Provider\Base;
use queue\Producer;
use TencentCloud\Cwp\*********\Models\Place;
use yii\base\Exception;
use Yii;
use yii\db\conditions\AndCondition;

class AddService extends BaseService
{
    public $saveType;
    public $oldContent = '-';
    public $newContent = '';
    public $fileIds    = '';
    public $oldFileIds = '';
    public $editorType = 9;
    public $postJobIds;

    public $isEditArticle = 0;
    public $isEditJob     = 0;
    public $isSaveJob     = 0;
    public $isAddJob      = 0;
    public $isFileIds     = 0;
    public $isContent     = 0;

    public $isAuditContent = 1; // 公告是否修改了需审核字段
    public $isAuditJob     = 0; // 有修改了需审核字段

    // 保存
    const SAVE_TYPE_STAGING = 1;
    // 提交审核
    const SAVE_TYPE_AUDIT = 2;

    /**
     * 执行添加
     * @throws Exception
     */
    public function run()
    {
        $this->beforeRun();
        switch ($this->saveType) {
            case self::SAVE_TYPE_STAGING:
                $this->staging();
                break;
            case self::SAVE_TYPE_AUDIT:
                $this->audit();
                break;
            default:
                throw new Exception('添加类型错误');
        }

        $this->afterRun();
        if (PLATFORM != 'ADMIN') {
            (new PushEditMessageService())->publish(['announcementId' => $this->announcementId]);
        }
    }

    /**
     * 设置发布
     * @return $this
     */
    public function setStaging($type = 0): AddService
    {
        $this->saveType = self::SAVE_TYPE_STAGING;
        if ($type == self::SAVE_TYPE_AUDIT && $this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            $this->actionType = self::ACTION_TYPE_ADD;
        }

        return $this;
    }

    /**
     * 设置发布编辑审核
     * @return $this
     */
    public function setAudit(): AddService
    {
        $this->saveType = self::SAVE_TYPE_AUDIT;
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            $this->actionType = self::ACTION_TYPE_EDIT;
        }

        return $this;
    }

    /**
     * 保存信息
     * @throws Exception
     */
    private function staging()
    {
        // 有两种情况,第一种是直接提交审核,第二种是保存后重新编辑
        if ($this->announcementId) {
            $this->handleType = BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT;
            $this->edit();
        } else {
            $this->handleType = BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE;
            $this->add();
        }
    }

    /**
     * 发布审核
     * @throws Exception
     */
    private function audit()
    {
        // 有两种情况,第一种是直接提交审核,第二种是从编辑页面提交审核
        if ($this->announcementId) {
            $this->handleType = BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT;
            $this->edit();
        } else {
            $this->handleType = BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE;
            $this->add();
        }
    }

    /**
     * 筛选职位id
     * @param $idsArr
     * @return array
     * @throws Exception
     */
    private function screenJobId($idsArr)
    {
        $jobIds = BaseJobTemp::find()
            ->where([
                'in',
                'id',
                $idsArr,
            ])
            ->andWhere(['announcement_id' => $this->announcementId])
            ->select('id')
            ->asArray()
            ->all();
        if (!$jobIds) {
            $jobIds = BaseJob::find()
                ->where([
                    'and',
                    [
                        '<>',
                        'status',
                        BaseJob::STATUS_DELETE,
                    ],
                    [
                        'in',
                        'id',
                        $idsArr,
                    ],
                    [
                        '=',
                        'announcement_id',
                        $this->announcementId,
                    ],
                ])
                ->select('id')
                ->asArray()
                ->all();
        }
        $ids = [];
        foreach ($jobIds as $id) {
            $ids[] = $id['id'];
        }

        return $ids;
    }

    private function screenJob($idsArr)
    {
        $jobData = BaseJobTemp::find()
            ->where([
                'in',
                'id',
                $idsArr,
            ])
            ->andWhere(['announcement_id' => $this->announcementId])
            ->asArray()
            ->all();

        if (!$jobData) {
            $jobData = BaseJob::find()
                ->where([
                    'and',
                    [
                        '<>',
                        'status',
                        BaseJob::STATUS_DELETE,
                    ],
                    [
                        'in',
                        'id',
                        $idsArr,
                    ],
                    [
                        '=',
                        'announcement_id',
                        $this->announcementId,
                    ],
                ])
                ->asArray()
                ->all();

            foreach ($jobData as &$v) {
                $v['job_id'] = $v['id'];
            }
        }

        return $jobData;
    }

    private function getOldArticleContent($articleId)
    {
        return BaseArticle::find()
            ->select('content')
            ->where([
                'id' => $articleId,
            ])
            ->andWhere([
                '<>',
                'is_delete',
                BaseAnnouncement::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();
    }

    private function getOldJobInfo()
    {
        return BaseJob::find()
            ->select('id,status,audit_status,duty,requirement,remark,first_release_time')
            ->where([
                'announcement_id' => $this->announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->asArray()
            ->all();
    }

    private function setDeleteJob()
    {
        // ==删除职位==
        // 计算职位id交集，原来职位ids：$this->jobIds，提交的职位ids：$this->postJobIds
        $intersecJobId = array_intersect($this->jobIds, $this->postJobIds);
        // 计算职位id差集，找出删除的职位id
        $diffJobId = array_merge(array_diff_assoc($this->jobIds, $intersecJobId)) ?: [];

        // 是否编辑职位
        $tempJob = BaseJobTemp::find()
            ->select('job_id')
            ->where(['job_id' => $diffJobId])
            ->asArray()
            ->all();
        if ($tempJob) {
            // 获取编辑的原职位id
            $tempJobId = [];
            foreach ($tempJob as $v) {
                $tempJobId[] = $v['job_id'];
            }

            // 如果是正式表的职位编辑产生的新id不作删除
            foreach ($tempJobId as $id) {
                foreach ($diffJobId as $k => $v) {
                    // 这里过滤正式表的编辑职位id
                    if ($id == $v) {
                        unset($diffJobId[$k]);
                    }
                }
            }
        }

        // 判断是否删除了职位
        if ($diffJobId) {
            // 遍历删除的职位id
            foreach ($diffJobId as $v) {
                //检查是否有投递数据(站外投递)
                $jobApply = BaseOffSiteJobApply::findOne(['job_id' => $v]);
                if ($jobApply) {
                    throw new Exception('职位id:' . $v . '已有投递数据，删除失败');
                }

                // 这里是判断删除了正式表没有上线过的职位，修改类型
                $oldJob           = BaseJob::findOne(['id' => $v]);
                $firstReleaseTime = $oldJob->first_release_time;

                // 合作单位有审核通过历史职位不可删除
                $isCooperation = BaseCompany::findOneVal(['id' => $oldJob->company_id], 'is_cooperation');
                if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES && $firstReleaseTime != TimeHelper::ZERO_TIME) {
                    throw new Exception('职位id:' . $v . '属于合作单位并且有审核通过历史，不可删除');
                }
            }

            if (!BaseJob::updateAll(['status' => BaseJob::STATUS_DELETE], [
                'in',
                'id',
                $diffJobId,
            ])) {
                throw new Exception('职位删除失败');
            }

            if ($this->articleModel->status == BaseArticle::STATUS_ONLINE) {
                // 统计在线的公告职位
                $oldJobCount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                        'status'          => BaseJob::STATUS_ONLINE,
                    ])
                    ->count();
                if ($oldJobCount < 1) {
                    throw new Exception('剩余最后一个在线职位，不支持删除');
                }
            }
            // 自动归属公告栏目规则
            //$this->autoClassifyRun();
        }
    }

    /**
     * 获取未审核通过的职位
     */
    private function getAuditJobList()
    {
        // 查找是否还有未审核通过的职位
        // 新增
        $autitAddJob = BaseJob::findOne([
            'announcement_id' => $this->announcementId,
            'status'          => BaseJob::STATUS_WAIT,
            'audit_status'    => BaseJob::STATUS_WAIT_AUDIT,
        ]);
        // 编辑
        $autitEditJob = BaseJob::findOne([
            'announcement_id' => $this->announcementId,
            'status'          => BaseJob::STATUS_ONLINE,
            'audit_status'    => BaseJob::STATUS_WAIT_AUDIT,
        ]);

        if ($autitAddJob) {
            $this->isAddJob = 1;
        }
        if ($autitEditJob) {
            $this->isEditJob = 1;
        }
    }

    /**
     * 新旧公告详情对比
     * @param $newsContent
     */
    private function checkChangeContent($newsContent, $fileIds)
    {
        $auditStatus = $this->announcementModel->audit_status;
        // 获取旧的公告内容
        $articleContent = $this->getOldArticleContent($this->articleId);

        // 获取旧的职位
        $oldJobInfo = $this->getOldJobInfo();
        $oldJobIds  = [];
        foreach ($oldJobInfo as $item) {
            $oldJobIds[] = $item['id'];
        }
        $this->jobIds  = $oldJobIds;
        $this->jobList = $oldJobInfo;

        // 新旧公告详情对比
        $this->oldContent = $articleContent['content'];
        // 公告职位附件对比
        $this->oldFileIds = BaseAnnouncement::findOneVal(['id' => $this->announcementId], 'file_ids');
        $fileIdsArr       = empty($fileIds) ? [] : explode(',', $fileIds);
        $oldFileIdsArr    = empty($this->oldFileIds) ? [] : explode(',', $this->oldFileIds);
        $diffFilesArr     = array_diff($fileIdsArr, $oldFileIdsArr);
        if (count_chars($this->oldContent) !== count_chars($newsContent) && $auditStatus != BaseAnnouncement::STATUS_AUDIT_STAGING) {
            // 修改内容
            $this->newContent    = $newsContent;
            $this->isEditArticle = 1;
            $this->isContent     = 1;
            if ($this->operatorType == self::OPERATOR_TYPE_COMPANY && count($diffFilesArr) > 0) {
                $this->fileIds   = $fileIds;
                $this->isFileIds = 1;
            }
        } elseif ($auditStatus != BaseAnnouncement::STATUS_AUDIT_STAGING && $this->operatorType == self::OPERATOR_TYPE_COMPANY && count($diffFilesArr) > 0) {
            // 修改职位附件
            $this->fileIds       = $fileIds;
            $this->isEditArticle = 1;
            $this->isFileIds     = 1;
        } else {
            // 没有修改需审核的字段
            $this->isAuditContent = 0;
        }
    }

    /**
     * 拒绝重新发布是否有新增编辑的职位
     * 场景：
     * 1、有审核通过历史修改职位+新增职位
     *  a、修改拒绝+新增，修改职位的非审核字段
     * 2、没有审核通过历史修改职位+新增职位
     *  a、修改拒绝+新增
     */
    private function checkChangeJob()
    {
        // 获取正式表的职位信息
        $oldJobInfo = BaseJob::find()
            ->select('id,status,audit_status,duty,requirement,remark,first_release_time')
            ->where([
                'announcement_id' => $this->announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->asArray()
            ->all();

        $oldJobIds = [];
        // 检查正式表的职位信息
        foreach ($oldJobInfo as $item) {
            // 没有审核通过历史，检查是否还有未审核职位
            if ($item['first_release_time'] == TimeHelper::ZERO_TIME && $item['status'] == BaseJob::STATUS_WAIT) {
                $this->isAddJob = 1;
            } else {
                $this->isAddJob = 0;
            }
            // 有审核通过历史，检查是否还有未审核职位
            if ($item['first_release_time'] != TimeHelper::ZERO_TIME && $item['status'] != BaseJob::STATUS_WAIT && ($item['audit_status'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT || $item['audit_status'] == BaseJob::AUDIT_STATUS_REFUSE_AUDIT)) {
                $this->isEditJob = 1;
            }

            $oldJobIds[] = $item['id'];
        }

        // 这里发布提交的职位id，检查临时表是否有编辑和新增数据
        foreach ($this->jobTempIds as $v) {
            $createTypeEdit = BaseJobTemp::findOne([
                'announcement_id' => $this->announcementId,
                'create_type'     => self::CREATE_TYPE_EDIT,
                'id'              => $v,
            ]);
            $createTypeAdd  = BaseJobTemp::findOne([
                'announcement_id' => $this->announcementId,
                'create_type'     => self::CREATE_TYPE_ADD,
                'id'              => $v,
            ]);

            $mainJob = '';
            if ($createTypeEdit->job_id) {
                // 获取编辑的正式表原职位状态
                $mainJob = BaseJob::findOne(['id' => $createTypeEdit->job_id]);
            }

            // 审核通过职位修改非审核字段直接生效
            $editField = [
                'duty'        => $createTypeEdit->duty,
                'requirement' => $createTypeEdit->requirement,
                'remark'      => $createTypeEdit->remark,
                'job_id'      => $createTypeEdit->job_id,
            ];
            // 这里判断无审核通过历史的职位编辑 or 没有修改审核字段(有编辑职位&没有修改审核字段)
            if ($createTypeEdit && (!$this->isAuditJobField($editField) || $mainJob->first_release_time == TimeHelper::ZERO_TIME)) {
                $this->isSaveJob = 1;
            }

            // 有审核通过历史，有编辑审核字段(有编辑职位&有审核通过历史&有修改审核字段)
            if ($createTypeEdit && $mainJob->first_release_time != TimeHelper::ZERO_TIME && $mainJob->status != BaseJob::STATUS_WAIT && $this->isAuditJobField($editField)) {
                $this->isEditJob = 1;
            }

            // 是否有新增职位
            if ($createTypeAdd) {
                $this->isAddJob = 1;
            }
        }

        $this->jobIds  = $oldJobIds;
        $this->jobList = $oldJobInfo;
    }

    /***
     * 参数公共验证部分
     * @param $data
     */
    private function commonParamVerify($data)
    {
        if (empty($data['content'])) {
            throw new Exception('公告详情不能为空');
        }

        // 检查内容是否有敏感词
        $this->getCheckContent($data);

        if (empty($data['jobIds'])) {
            throw new Exception('请添加职位');
        }
        if (empty($data['title'])) {
            throw new Exception('公告标题不能为空');
        }
        //检查职位附件记录
        if (!empty($data['fileIds'])) {
            $this->checkJobAppendix($data['fileIds']);
        }
        $this->setCompany($data['companyId']);
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO) {
            if (isset($data['deliveryWay'])) {
                //非合作单位去掉delivery_way字段 永远走else分支
                unset($data['deliveryWay']);
            }
            if ($data['extraNotifyAddress']) {
                throw new Exception('非合作单位不支持填写邮件通知地址');
            }
        }
        // 企业的
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            if (empty($data['periodDate'])) {
                throw new Exception('公告有效期不能为空');
            }
        }
        // 运营后台添加的
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            if ($data['comboAttribute'] || $data['overseasAttribute']) {
                $attribute     = array_unique(ArrayHelper::merge($data['comboAttribute'], $data['overseasAttribute']));
                $attributeData = [
                    'attribute'                => $attribute,
                    'indexTopEndTime'          => $data['indexTopEndTime'] ?: '',
                    'columnTopEndTime'         => $data['columnTopEndTime'] ?: '',
                    'doctorPushEndTime'        => $data['doctorPushEndTime'] ?: '',
                    'overseasIndexTopEndTime'  => $data['overseasIndexTopEndTime'] ?: '',
                    'overseasColumnTopEndTime' => $data['overseasColumnTopEndTime'] ?: '',
                ];
                $attributeSet  = BaseArticleAttribute::formatAttributeList($attributeData);
            }
            /**
             * 勾选置顶属性结束时间不得大于公告截止日期
             */
            if (!empty($data['periodDate']) && strtotime($data['periodDate']) > 0 && !empty($data['comboAttribute'])) {
                $this->checkAttributeTime($attributeSet, $data['periodDate']);
            }

            if (empty($data['homeColumnId'])) {
                throw new Exception('所属栏目不能为空');
            }
            if (empty($data['companyId']) && $this->articleModel->status != BaseArticle::STATUS_ONLINE) {
                throw new Exception('请选择单位');
            }
        }
        ///非合作单位报名方式必填
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO || (isset($data['deliveryWay']) && in_array($data['deliveryWay'],
                    BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK_LIST))) {
            if (empty($data['applyType']) || empty($data['applyAddress'])) {
                throw new Exception('报名方式没有勾选或者投递地址为空');
            }
        }
        //报名方式与通知地址不可同时填写
        if ($data['applyType'] && $data['extraNotifyAddress']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        if (isset($data['deliveryWay']) && $data['deliveryWay'] > 0) {
            if ($data['deliveryWay'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($data['applyType'])) {
                    throw new Exception('报名方式没有勾选');
                }
                $applyTypeArr = explode(',', $data['applyType']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['applyAddress']);
                } else {
                    if (!ValidateHelper::isUrl($data['applyAddress'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['applyAddress']);
                if ($isEmail) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                }
                $data['extraNotifyAddress'] = '';
            } else {//delivery_way=1
                $data['applyType']    = '';
                $data['applyAddress'] = '';
            }
        } else {
            if ($data['applyType']) {
                $applyTypeArr = explode(',', $data['applyType']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if (empty($data['applyAddress'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['applyAddress']);
                } else {
                    if (!ValidateHelper::isUrl($data['applyAddress'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['applyAddress']);
                //判断投递方式
                if ($isEmail) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                }
                $data['extraNotifyAddress'] = '';
            } else {
                $data['applyType']    = '';
                $data['applyAddress'] = '';
                //如果是单位端就给默认平台投递 如果是运营后台就默认成0跟随
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_PLATFORM;
                } else {
                    $data['deliveryWay'] = 0;
                }
            }
        }
        //检查通知邮箱的格式
        if ($data['extraNotifyAddress']) {
            BaseJob::checkEmailApplyAddress($data['extraNotifyAddress']);
        }
        //处理投递类型；选择投递类型那就站外投递，没有就站内投递
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['deliveryWay'] == 0) {
                $data['deliveryType'] = 0;//跟公告
            } elseif ($data['deliveryWay'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTER;
            } else {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        } else {
            if ($data['deliveryWay'] == 0) {
                $data['deliveryType'] = 0;
            } else {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTER;
            }
        }

        return $data;
    }

    private function getCheckContent($data)
    {
        // 首先找到公告的内容
        $announcementContent = $data['content'];
        // 去掉html内容
        $announcementContent = strip_tags($announcementContent);
        // 然后找到职位的内容

        // 这里有可能是编辑,也有可能是新增,如果是编辑就会有公告id
        $announcementId = $data['announcementId'] ?? 0;
        if ($announcementId) {
            // 先去临时表里面找到这个
            $tmpJobList = BaseJobTemp::find()
                ->select('id,duty,requirement,remark')
                ->where([
                    'id'              => explode(',', $data['jobIds']),
                    'announcement_id' => $announcementId,
                ])
                ->asArray()
                ->all();
        } else {
            // 找到职位,有两种情况,一种是临时职位
            $tmpJobList = BaseJobTemp::find()
                ->select('id,duty,requirement,remark')
                ->where([
                    'id' => explode(',', $data['jobIds']),
                ])
                ->asArray()
                ->all();
        }

        $content = $announcementContent;
        // 上面的内容一起塞到content里面
        foreach ($tmpJobList as $job) {
            $content .= $job['duty'] . $job['requirement'] . $job['remark'];
        }

        // 去掉空格换行
        $content = str_replace([
            "\r\n",
            "\r",
            "\n",
            " ",
            PHP_EOL,
        ], "", $content);

        // 检查内容
        $checkModel = new BadWordCheck();
        $checkModel->check($content);

        $badWord = $checkModel->badWord;

        $message = '';
        if (count($badWord) > 0) {
            // 循环去查询实际上是属于哪字段
            foreach ($badWord as $item) {
                $match                    = false;
                $matchAnnouncementContent = str_replace([
                    "\r\n",
                    "\r",
                    "\n",
                    " ",
                    PHP_EOL,
                ], "", $announcementContent);
                if (strpos($matchAnnouncementContent, $item) !== false) {
                    $message .= '公告内容中含有敏感词:' . $item . ',';
                    $match   = true;
                } else {
                    foreach ($tmpJobList as $job) {
                        $matchJobDuty        = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['duty']);
                        $matchJobRequirement = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['requirement']);
                        $matchJobRemark      = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['remark']);

                        if (strpos($matchJobDuty, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '岗位职责中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                        if (strpos($matchJobRequirement, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '任职要求中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                        if (strpos($matchJobRemark, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '其他说明中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                    }
                }

                if (!$match) {
                    $message .= '内容中含有敏感词:' . $item . ',';
                }
            }

            // 去掉最后一个逗号
            $message = substr($message, 0, -1);

            throw new Exception($message);
        }
    }

    /**
     * 设置要新增的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setAddData($data): AddService
    {
        //使用公共部分验证参数
        $data = $this->commonParamVerify($data);
        // 提交的公告职位id
        $this->postJobIds  = explode(',', $data['jobIds']);
        $this->jobTempIds  = $this->postJobIds;
        $this->jobTempData = BaseJobTemp::find()
            ->where([
                'in',
                'id',
                $this->jobTempIds,
            ])
            ->asArray()
            ->all();
        if (!$this->jobTempData) {
            throw new Exception('公告职位数据不存在');
        }

        foreach ($this->jobTempData as &$apply) {
            if ($this->operatorType == self::OPERATOR_TYPE_ADMIN && $this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES && empty($data['deliveryWay']) && empty($apply['delivery_way'])) {
                throw new Exception('公告与职位ID：' . $apply['id'] . '投递方式必须配置一个');
            }
            // 公告和职位都需要是合法的日期
            //这一块放到这个foreach最后面去实现---不要在后面加逻辑
            if (!$data['periodDate']) {
                continue;
            }
            if ($data['periodDate'] == TimeHelper::ZERO_TIME) {
                continue;
            }
            $periodDate = substr($apply['period_date'], 0, 10);
            if ($periodDate > $data['periodDate']) {
                $apply['period_date'] = $data['periodDate'] ?: TimeHelper::ZERO_TIME;
            } else {
                $apply['period_date'] = $apply['period_date'] ?: TimeHelper::ZERO_TIME;
            }
            //            if (isset($data['templateId']) && $data['templateId'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
            //                if (($data['applyType'] && !in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, explode(',',
            //                            $data['applyType']))) || ($apply['apply_type'] && !in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL,
            //                            explode(',', $apply['apply_type'])))) {
            //                    throw new Exception('双会活动公告不能填写网址投递');
            //                }
            //            }
        }
        $this->baseData = $data;
        $this->fileIds  = $data['fileIds'] ?: '';

        return $this;
    }

    /**
     * 发布审核
     * @throws Exception
     */
    private function add()
    {
        $data = $this->baseData;
        $this->setCompany($data['companyId']);
        $announcementModel = new BaseAnnouncement();
        $articleModel      = new BaseArticle();

        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 企业的
            $announcementModel->create_type = $announcementModel::CREATE_TYPE_COMPANY;
            $announcementModel->creator_id  = $this->companyMemberModel->id;
            if ($data['submitType'] == self::SAVE_TYPE_AUDIT) {
                $announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_YES;
            } else {
                $announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_NO;
            }
        }

        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $announcementModel->create_type        = $announcementModel::CREATE_TYPE_ADMIN;
            $announcementModel->creator_id         = $this->operatorId;
            $announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_NO;
        }

        $articleModel->tag_ids             = $data['tagIds'] ?: '';
        $articleModel->recommend_ids       = $data['recommendIds'] ?: '';
        $articleModel->cover_thumb         = $data['coverThumb'] ?: '';
        $articleModel->seo_description     = $data['seoDescription'] ?: '';
        $articleModel->seo_keywords        = $data['seoKeywords'] ?: '';
        $articleModel->title               = $data['title'] ?: '';
        $articleModel->content             = $data['content'] ?: '';
        $articleModel->home_column_id      = $data['homeColumnId'];
        $articleModel->home_sub_column_ids = $data['homeSubColumnIds'] ?: '';
        $articleModel->is_delete           = BaseArticle::STATUS_DELETE;
        $articleModel->status              = BaseArticle::STATUS_STAGING;
        $articleModel->is_show             = BaseArticle::IS_SHOW_YES;
        $articleModel->type                = BaseArticle::TYPE_ANNOUNCEMENT;
        if ($data['submitType'] == self::SAVE_TYPE_AUDIT) {
            // 提交发布才更新申请审核时间
            $articleModel->apply_audit_time = CUR_DATETIME;
        }
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }

        $majorIds                                    = implode(',', $data['majorIds']);
        $announcementModel->major_ids                = $majorIds ?: '';
        $announcementModel->article_id               = $articleModel->id;
        $announcementModel->relation_company_ids     = $data['relationCompanyIds'] ?: '';
        $announcementModel->apply_type               = $data['applyType'] ?: '';
        $announcementModel->apply_address            = $data['applyAddress'] ?: '';
        $announcementModel->template_id              = $data['templateId'] ?: BaseAnnouncement::TEMPLATE_ORDINARY;
        $announcementModel->title                    = $data['title'] ?: '';
        $announcementModel->company_id               = $data['companyId'] ?: 0;
        $announcementModel->member_id                = $this->companyMemberModel->id;
        $announcementModel->period_date              = $this->isPeriodDate($data['periodDate']);
        $announcementModel->file_ids                 = $this->fileIds;
        $announcementModel->delivery_type            = $data['deliveryType'] ?: 0;
        $announcementModel->delivery_way             = $data['deliveryWay'] ?: 0;
        $announcementModel->extra_notify_address     = $data['extraNotifyAddress'] ?: '';
        $announcementModel->is_attachment_notice     = $data['isAttachmentNotice'] ?: BaseAnnouncement::IS_ATTACHMENT_NOTICE_NO;
        $announcementModel->sub_title                = $data['subTitle'] ?: '';
        $announcementModel->highlights_describe      = $data['highlightsDescribe'] ?: '';
        $announcementModel->background_img_file_id   = $data['backgroundImgFileId'] ?: '';
        $announcementModel->background_img_file_id_2 = $data['backgroundImgFileId2'] ?: '';
        $announcementModel->background_img_file_id_3 = $data['backgroundImgFileId3'] ?: '';
        $announcementModel->background_img_file_type = $data['backgroundImgFileType'] ?: BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_DEFAULT;
        $announcementModel->activity_job_content     = $data['activityJobContent'] ?: '';

        //判断是否非合作单位，处理地址隐藏
        $isCooperation = BaseCompany::findOneVal(['id' => $data['companyId']], 'is_cooperation');
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $announcementModel->address_hide_status = BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        } else {
            $announcementModel->address_hide_status = $data['addressHideStatus'] ?: BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        }

        if ($data['submitType'] == self::SAVE_TYPE_STAGING) {
            $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_STAGING;
        } else {
            $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
        }
        if (!$announcementModel->save()) {
            throw new Exception($announcementModel->getFirstErrorsMessage());
        }
        $this->announcementId = $announcementModel->id;

        // 职位插入主表
        $this->createJob();

        $this->announcementData  = $announcementModel;
        $this->announcementModel = $announcementModel;
        $this->articleData       = $articleModel;
        $this->announcementId    = $announcementModel->id;
        $this->articleId         = $announcementModel->article_id;

        // 运营添加
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            //获取栏目id
            $columnIdsArr = [
                'homeColumnId'     => $data['homeColumnId'],
                'homeSubColumnIds' => $data['homeSubColumnIds'],
            ];
            //拼凑栏目属性和时间数组，进行添加
            $formatData = [];

            if ($data['comboAttribute'] || $data['overseasAttribute']) {
                $attribute     = array_unique(ArrayHelper::merge($data['comboAttribute'], $data['overseasAttribute']));
                $attributeData = [
                    'attribute'                => $attribute,
                    'indexTopEndTime'          => $data['indexTopEndTime'] ?: '',
                    'columnTopEndTime'         => $data['columnTopEndTime'] ?: '',
                    'doctorPushEndTime'        => $data['doctorPushEndTime'] ?: '',
                    'overseasIndexTopEndTime'  => $data['overseasIndexTopEndTime'] ?: '',
                    'overseasColumnTopEndTime' => $data['overseasColumnTopEndTime'] ?: '',
                ];
                $formatData    = BaseArticleAttribute::formatAttributeList($attributeData);
            }
            //海外栏目属性

            $this->setArticleColumnAttribute($columnIdsArr, $formatData);
        }
        //增加公告附属表数据
        BaseAnnouncementExtra::insertData([
            'announcement_id' => $announcementModel->id,
            'company_id'      => $data['companyId'],
        ]);

        // 更新公告关联的活动
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            SpecialActivityService::multipleActivityRelationSingleAnnouncement($announcementModel->company_id,
                $data['activityAnnouncement'] ?? '', $announcementModel->id);
        }
    }

    /**
     * 设置要编辑的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setEditData($data): AddService
    {
        if (!$data['announcementId']) {
            throw new Exception('缺失公告id');
        }
        $this->setAnnouncement($data['announcementId']);
        if ($this->articleModel->status == BaseArticle::STATUS_OFFLINE) {
            throw new Exception('公告下线状态不允许编辑');
        }
        //使用公共部分验证参数
        $data = $this->commonParamVerify($data);
        // 提交的公告职位id,包含(复制新增，修改，删除后的)，分为有审核通过历史/没有审核通过历史
        $this->postJobIds  = explode(',', $data['jobIds']);
        $this->jobTempIds  = $this->screenJobId($this->postJobIds);
        $this->jobTempData = $this->screenJob($this->jobTempIds);
        if (!$this->jobTempData) {
            throw new Exception('公告职位数据不存在');
        }
        //如果为空则判断职位投递类型是否为空
        foreach ($this->jobTempData as &$apply) {
            if ($this->operatorType == self::OPERATOR_TYPE_ADMIN && $this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES && empty($data['deliveryWay']) && empty($apply['delivery_way'])) {
                throw new Exception('公告与职位ID：' . $apply['id'] . '投递方式必须配置一个');
            }
            if (isset($apply['is_temp']) && $apply['is_temp'] == BaseJobTemp::IS_TEMP_YES && $apply['delivery_type'] == 0) {
                //这时候是一个临时职位,且投递类型为0,则验证公告与原职位投递类型是否一致
                //先拿到原职位数据
                if ($apply['job_id'] > 0) {
                    $oldJobInfo = BaseJob::findOne($apply['job_id']);
                    //比对投递类型 且就旧职位投递类型不为0
                    if ($oldJobInfo->delivery_type != $data['deliveryType'] && $oldJobInfo->delivery_type != 0) {
                        throw new Exception('公告投递类型与原职位ID:' . $oldJobInfo->id . '投递类型不一致,临时职位ID:' . $apply['id'] . '取消了职位投递类型');
                    }
                }
            }
            if (!$data['periodDate']) {
                continue;
            }
            if ($data['periodDate'] == TimeHelper::ZERO_TIME) {
                continue;
            }
            $periodDate = substr($apply['period_date'], 0, 10);
            if ($periodDate > $data['periodDate']) {
                $apply['period_date'] = $data['periodDate'] ?: TimeHelper::ZERO_TIME;
            } else {
                $apply['period_date'] = $apply['period_date'] ?: TimeHelper::ZERO_TIME;
            }
            //            if (isset($data['templateId']) && $data['templateId'] == BaseAnnouncement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
            //                if (($data['applyType'] && !in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, explode(',',
            //                            $data['applyType']))) || ($apply['apply_type'] && !in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL,
            //                            explode(',', $apply['apply_type'])))) {
            //                    throw new Exception('双会活动公告不能填写网址投递');
            //                }
            //            }
        }
        // 公告是否已在线状态
        if ($this->articleModel->status == BaseArticle::STATUS_ONLINE && !empty($data['announcementId'])) {
            if ($this->announcementModel->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
                throw new Exception('待审核状态无法操作编辑');
            }

            // 新旧公告详情对比
            $this->checkChangeContent($data['content'], $data['fileIds']);
            // ==删除职位==
            $this->setDeleteJob();
            // 获取旧的职位
            $this->checkChangeJob();
            // 查找是否还有未审核通过的职位
            $this->getAuditJobList();

            // 判断修改类型
            if ($this->isEditArticle && $this->isEditJob && $this->isAddJob) {
                // ==修改了公告+修改了职位+新增了职位==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB;
            } elseif ($this->isEditJob && $this->isAddJob) {
                // ==修改了职位+新增了职位==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_ADD_JOB;
            } elseif ($this->isEditArticle && $this->isAddJob) {
                // ==修改了公告+新增了职位==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB;
            } elseif ($this->isEditArticle && $this->isEditJob) {
                // ==修改了公告+修改了职位==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB;
            } elseif ($this->isAddJob) {
                // ==新增了职位==
                $this->editorType = BaseAnnouncement::TYPE_ADD_JOB;
            } elseif ($this->isEditJob) {
                // ==修改了职位==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_JOB;
            } elseif ($this->isEditArticle) {
                // ==修改了公告==
                $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT;
            }
        } elseif ($this->articleModel->status == BaseArticle::STATUS_STAGING && !empty($data['announcementId'])) {
            // ===保存的===

            // 是否改变了职位
            $this->checkChangeJob();

            // 删除职位
            $this->setDeleteJob();
        }

        $this->baseData = $data;

        return $this;
    }

    /**
     * 编辑发布
     */
    private function edit()
    {
        $announcementId    = $this->announcementId;
        $announcementModel = $this->announcementModel;
        $articleModel      = $this->articleModel;
        $data              = $this->baseData;
        // 校验一下招聘状态下是允许修改的
        if ($articleModel['status'] == BaseArticle::STATUS_OFFLINE) {
            throw new Exception('该公告已经下线,不能修改');
        } elseif ($articleModel['is_delete'] == BaseArticle::STATUS_ACTIVE) {
            throw new Exception('该公告已经删除,不能修改');
        } elseif ($articleModel['status'] == BaseArticle::STATUS_ONLINE) {
            // 在线状态,允许修改,但是会去到待审核---只有后台才有公告修改报名方式这个一说
            if ($this->operatorType == self::OPERATOR_TYPE_ADMIN && $announcementModel->delivery_type != $data['deliveryType'] && $announcementModel->delivery_type > 0) {
                throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
            }
        } elseif ($articleModel['status'] == BaseArticle::STATUS_STAGING) {
            // — 状态,允许修改,但是会去到待审核
        } else {
            throw new Exception('该公告状态不正确,不能修改');
        }

        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 企业的
            $announcementModel->create_type = $announcementModel::CREATE_TYPE_COMPANY;
            $announcementModel->creator_id  = $this->companyMemberModel->id;
        }
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $announcementModel->create_type = $announcementModel::CREATE_TYPE_ADMIN;
            $announcementModel->creator_id  = $this->operatorId;
        }

        // 修改公告基本信息
        $articleModel->tag_ids                   = $data['tagIds'] ?: '';
        $articleModel->recommend_ids             = $data['recommendIds'] ?: '';
        $articleModel->cover_thumb               = $data['coverThumb'] ?: '';
        $articleModel->seo_description           = $data['seoDescription'] ?: '';
        $articleModel->seo_keywords              = $data['seoKeywords'] ?: '';
        $articleModel->home_column_id            = $data['homeColumnId'];
        $articleModel->home_sub_column_ids       = $data['homeSubColumnIds'] ?: '';
        $articleModel->apply_audit_time          = CUR_DATETIME;
        $articleModel->title                     = $data['title'] ?: '';
        $announcementModel->article_id           = $articleModel->id;
        $announcementModel->period_date          = $this->isPeriodDate($data['periodDate']);
        $announcementModel->relation_company_ids = $data['relationCompanyIds'] ?: '';
        $announcementModel->apply_type           = $data['applyType'] ?: '';
        $announcementModel->apply_address        = $data['applyAddress'] ?: '';
        $announcementModel->template_id          = $data['templateId'] ?: BaseAnnouncement::TEMPLATE_ORDINARY;
        $announcementModel->title                = $data['title'] ?: '';
        $announcementModel->is_attachment_notice = $data['isAttachmentNotice'] ?: BaseAnnouncement::IS_ATTACHMENT_NOTICE_NO;
        //        $announcementModel->file_ids             = $data['fileIds'] ?: '';
        if ($this->isFileIds == 0) {//直接写主表
            $announcementModel->file_ids = $data['fileIds'] ?: '';
        }
        $announcementModel->delivery_type            = $data['deliveryType'] ?: 0;
        $announcementModel->delivery_way             = $data['deliveryWay'] ?: 0;
        $announcementModel->extra_notify_address     = $data['extraNotifyAddress'] ?: '';
        $announcementModel->sub_title                = $data['subTitle'] ?: '';
        $announcementModel->highlights_describe      = $data['highlightsDescribe'] ?: '';
        $announcementModel->background_img_file_id   = $data['backgroundImgFileId'] ?: '';
        $announcementModel->background_img_file_id_2 = $data['backgroundImgFileId2'] ?: '';
        $announcementModel->background_img_file_id_3 = $data['backgroundImgFileId3'] ?: '';
        $announcementModel->background_img_file_type = $data['backgroundImgFileType'] ?: BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_DEFAULT;
        $announcementModel->activity_job_content     = $data['activityJobContent'] ?: '';

        //判断是否非合作单位，处理地址隐藏
        $isCooperation = BaseCompany::findOneVal(['id' => $data['companyId']], 'is_cooperation');
        if ($isCooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $announcementModel->address_hide_status = BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        } else {
            $announcementModel->address_hide_status = $data['addressHideStatus'] ?: BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        }

        // 编辑时，并且公告没有上线过可修改单位
        if (!empty($announcementId) && ($articleModel->status != BaseArticle::STATUS_ONLINE)) {
            $announcementModel->company_id = $data['companyId'];
            $announcementModel->member_id  = $this->companyMemberModel->id;
            // 自动归属公告栏目规则
            //            $this->autoClassifyRun();
        }
        // 这个公告现在是在线的,修改是不允许直接生效的,所以必须要先
        if ($articleModel->status == BaseArticle::STATUS_ONLINE) {
            switch ($this->editorType) {
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB:
                    $this->editorAnnouncement();
                    $this->editorJob();
                    $this->createJob();
                    break;
                case BaseAnnouncement::TYPE_EDITOR_ADD_JOB:
                    $this->editorJob();
                    $this->createJob();
                    break;
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB:
                    $this->editorAnnouncement();
                    $this->createJob();
                    break;
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB:
                    $this->editorAnnouncement();
                    $this->editorJob();
                    break;
                case BaseAnnouncement::TYPE_ADD_JOB:
                    $this->createJob();
                    break;
                case BaseAnnouncement::TYPE_EDITOR_JOB:
                    $this->editorJob();
                    break;
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT:
                    $this->editorAnnouncement();
                    break;
                default:
                    // 只操作了删除职位（审核通过或者拒绝的）
                    if ($announcementModel->audit_status == BaseAnnouncement::STATUS_AUDIT_REFUSE) {
                        $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_PASS;
                    }
                    break;
            }
            // 这里是职位没有审核通过历史，职位新增了审核拒绝后的再对新增职位修改，直接生效修改，并且修改类型是新增职位
            if ($this->isSaveJob) {
                $this->updateJob();
            }

            // 判断是否修改需审核的字段或审核拒绝，是则修改公告审核状态
            if ($this->isAuditContent || $this->isAuditJob || $announcementModel->audit_status == BaseAnnouncement::STATUS_AUDIT_REFUSE) {
                // 修改审核状态
                $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                // 变更公告没有审核通过的职位审核状态
                self::setJobAuditStatus();
            }
            // 更新公告信息
            if (!$announcementModel->save()) {
                throw new Exception($announcementModel->getFirstErrorsMessage());
            }
            if (!$articleModel->save()) {
                throw new Exception($articleModel->getFirstErrorsMessage());
            }

            // 更新公告关联的活动
            if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                SpecialActivityService::multipleActivityRelationSingleAnnouncement($announcementModel->company_id,
                    $data['activityAnnouncement'] ?? '', $announcementModel->id);
            }

            // 更新属性排序时间
            //            $this->updateSortTime();
        } else {
            // TODO 非在线，修改announcement表
            // 不是操作保存
            if ($data['submitType'] != self::SAVE_TYPE_STAGING) {
                $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
            }

            $announcementModel->file_ids = $data['fileIds'] ?: '';
            if (!$announcementModel->save()) {
                throw new Exception($announcementModel->getFirstErrorsMessage());
            }

            $articleModel->content = $data['content'];
            if (!$articleModel->save()) {
                throw new Exception($articleModel->getFirstErrorsMessage());
            }

            if ($this->isSaveJob) {
                // 直接生效
                $this->updateJob();
            } else {
                $this->createJob();
            }

            // 变更公告没有审核通过的职位审核状态
            self::setJobAuditStatus();
        }
        $jobList = BaseJob::findAll(['announcement_id' => $data['announcementId']]);
        // 检查公告有效日期与公告下的所有职位有效日期
        foreach ($jobList as $item) {
            $jobData = BaseJob::findOne(['id' => $item['id']]);

            if (!$data['periodDate']) {
                continue;
            }

            if ($data['periodDate'] == TimeHelper::ZERO_TIME) {
                continue;
            }
            $periodDate = substr($item['period_date'], 0, 10);
            if ($periodDate > $data['periodDate']) {
                $jobData->period_date = $data['periodDate'] ?: TimeHelper::ZERO_TIME;
            } else {
                $jobData->period_date = $item['period_date'] ?: TimeHelper::ZERO_TIME;
            }
            if (!$jobData->save()) {
                throw new Exception($jobData->getFirstErrorsMessage());
            }
        }
        // 运营添加
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            //获取栏目id
            $columnIdsArr = [
                'homeColumnId'     => $data['homeColumnId'],
                'homeSubColumnIds' => $data['homeSubColumnIds'],
            ];
            $formatData   = [];
            if ($data['comboAttribute'] || $data['overseasAttribute']) {
                $attribute     = array_unique(ArrayHelper::merge($data['comboAttribute'], $data['overseasAttribute']));
                $attributeData = [
                    'attribute'                => $attribute,
                    'indexTopEndTime'          => $data['indexTopEndTime'] ?: '',
                    'columnTopEndTime'         => $data['columnTopEndTime'] ?: '',
                    'doctorPushEndTime'        => $data['doctorPushEndTime'] ?: '',
                    'overseasIndexTopEndTime'  => $data['overseasIndexTopEndTime'] ?: '',
                    'overseasColumnTopEndTime' => $data['overseasColumnTopEndTime'] ?: '',
                ];
                $formatData    = BaseArticleAttribute::formatAttributeList($attributeData);
            }
            $this->setArticleColumnAttribute($columnIdsArr, $formatData);
        }

        $this->createAnnouncementHandleLog();
    }

    /**
     * 设置公告下的职位审核状态
     * @throws Exception
     */
    private function setJobAuditStatus()
    {
        $baseData = $this->baseData;
        // 获取公告原职位并修改状态
        $jobData = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->andWhere([
                'audit_status' => [
                    BaseJob::AUDIT_STATUS_WAIT_AUDIT,
                    BaseJob::AUDIT_STATUS_REFUSE_AUDIT,
                    BaseJob::AUDIT_STATUS_WAIT,
                ],
            ])
            ->asArray()
            ->all();

        foreach ($jobData as $item) {
            $jobModel = BaseJob::findOne(['id' => $item['id']]);
            // 检查提交类型
            if ($baseData['submitType'] == self::SAVE_TYPE_STAGING) {
                $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT;
            } else {
                $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            }

            if (!$jobModel->save()) {
                throw new Exception($jobModel->getFirstErrorsMessage());
            }
        }
    }

    private function updateSortTime()
    {
        $articleAttributeModel = BaseArticleAttribute::findAll(['article_id' => $this->articleId]);
        if ($articleAttributeModel) {
            if (!BaseArticleAttribute::updateAll(['sort_time' => CUR_DATETIME], ['article_id' => $this->articleId])) {
                throw new Exception('属性排序字段更新失败');
            }
        }
    }

    private function editorAnnouncement()
    {
        if (!empty($this->newContent) && !empty($this->fileIds)) {
            $editData = [
                'content'  => $this->newContent,
                'file_ids' => $this->fileIds,
            ];
        } elseif (empty($this->newContent) && !empty($this->fileIds)) {
            $editData = [
                'file_ids' => $this->fileIds,
            ];
        } else {
            $editData = [
                'content' => $this->newContent,
            ];
        }

        // 如果之前就有在编辑中的内容,那么是要直接编辑的
        $announcementEditModel                  = BaseAnnouncementEdit::findOne(['announcement_id' => $this->announcementId]) ?: new BaseAnnouncementEdit();
        $announcementEditModel->status          = BaseJobEdit::STATUS_ONLINE;
        $announcementEditModel->announcement_id = $this->announcementId;
        $announcementEditModel->edit_content    = json_encode($editData);
        $announcementEditModel->editor          = $this->operatorUserName ?: '';
        $announcementEditModel->editor_type     = BaseAnnouncementEdit::EDITOR_TYPE_PLATFORM;
        $announcementEditModel->editor_id       = $this->operatorId;
        if (!$announcementEditModel->save()) {
            throw new Exception($announcementEditModel->getFirstErrorsMessage());
        }
    }

    /**
     * 职位临时表同步到职位到主表
     */
    private function createJob()
    {
        // 获取临时职位数据
        $data     = $this->jobTempData;
        $baseData = $this->baseData;

        foreach ($data as $item) {
            if ($item['create_type'] == self::CREATE_TYPE_ADD && $item['is_temp'] == BaseJobTemp::IS_TEMP_YES) {
                $jobModel = new BaseJob();
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    // 企业的
                    $jobModel->creator     = $this->companyMemberModel->username ?: '';
                    $jobModel->create_type = BaseJob::CREATE_TYPE_SELF;
                }
                if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                    // 运营后台添加的
                    $jobModel->creator     = $this->adminModel->username ?: '';
                    $jobModel->create_type = BaseJob::CREATE_TYPE_AGENT;
                }

                $jobModel->announcement_id      = $this->announcementId;
                $jobModel->member_id            = $this->companyMemberModel->id;
                $jobModel->company_id           = $this->companyId;
                $jobModel->create_id            = $this->operatorId;
                $jobModel->is_article           = BaseJob::IS_ARTICLE_WAIT;
                $jobModel->name                 = $item['name'];
                $jobModel->code                 = $item['code'];
                $jobModel->job_category_id      = $item['job_category_id'];
                $jobModel->education_type       = $item['education_type'];
                $jobModel->major_id             = $item['major_id'] ?: '';
                $jobModel->nature_type          = $item['nature_type'];
                $jobModel->wage_type            = $item['wage_type'];
                $jobModel->is_negotiable        = $item['is_negotiable'];
                $jobModel->apply_type           = $item['apply_type'] ?: '';
                $jobModel->apply_address        = $item['apply_address'];
                $jobModel->min_wage             = $item['min_wage'];
                $jobModel->max_wage             = $item['max_wage'];
                $jobModel->experience_type      = $item['experience_type'];
                $jobModel->age_type             = $item['age_type'];
                $jobModel->title_type           = $item['title_type'];
                $jobModel->political_type       = $item['political_type'];
                $jobModel->abroad_type          = $item['abroad_type'];
                $jobModel->amount               = $item['amount'];
                $jobModel->department           = $item['department'];
                $jobModel->province_id          = $item['province_id'];
                $jobModel->city_id              = $item['city_id'];
                $jobModel->district_id          = $item['district_id'];
                $jobModel->address              = $item['address'] ?: '';
                $jobModel->welfare_tag          = $item['welfare_tag'];
                $jobModel->is_show              = BaseJob::IS_SHOW_YES;
                $jobModel->apply_audit_time     = CUR_DATETIME;
                $jobModel->status               = BaseJob::STATUS_WAIT;
                $jobModel->duty                 = $item['duty'];
                $jobModel->requirement          = $item['requirement'];
                $jobModel->remark               = $item['remark'];
                $jobModel->period_date          = $this->isPeriodDate($item['period_date']);
                $jobModel->delivery_limit_type  = $item['delivery_limit_type'] ?: '';
                $jobModel->delivery_type        = $item['delivery_type'] ?: 0;
                $jobModel->delivery_way         = $item['delivery_way'];
                $jobModel->extra_notify_address = $item['extra_notify_address'] ?: '';
                //判断操作平台为运营平台
                if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                    $jobModel->establishment_type = $item['establishment_type'] ?: '';
                    $jobModel->is_establishment   = !empty($item['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
                } else {
                    $jobModel->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
                }
                if ($baseData['submitType'] == self::SAVE_TYPE_STAGING) {
                    $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT;
                } else {
                    $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
                }
                if (!$jobModel->save()) {
                    throw new Exception($jobModel->getFirstErrorsMessage());
                }
                //合作单位
                if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
                    //处理一下职位联系人与职位协同人
                    //写入职位联系人与职位协同
                    $params_data = [
                        'job_id'                  => $jobModel->id,
                        'company_id'              => $this->companyId,
                        'announcement_id'         => $this->announcementId,
                        'job_contact_id'          => $item['contact_id'],
                        'job_contact_synergy_ids' => $item['contact_synergy_id'] ? explode(',',
                            $item['contact_synergy_id']) : [],
                    ];
                    $this->contact($params_data);
                }
                //写一条职位附属表逻辑
                BaseJobExtra::insertData([
                    'job_id'          => $jobModel->id,
                    'announcement_id' => $this->announcementId,
                    'company_id'      => $this->companyId,
                ]);
            }
        }

        // 职位插入主表后删除临时职位数据
        BaseJobTemp::deleteAll([
            'in',
            'id',
            $this->jobTempIds,
        ]);
        // 有修改需审核的字段
        $this->isAuditJob = 1;
        // 自动归属公告栏目规则
        //        $this->autoClassifyRun();
    }

    /**
     * 更新职位到主表(没有审核通过历史)
     */
    private function updateJob()
    {
        // 获取临时职位数据
        $data     = $this->jobTempData;
        $baseData = $this->baseData;
        foreach ($data as $item) {
            if ($item['create_type'] == self::CREATE_TYPE_EDIT && $item['is_temp'] == BaseJobTemp::IS_TEMP_YES) {
                $jobModel = BaseJob::findOne(['id' => $item['job_id']]);

                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    // 企业的
                    $jobModel->creator     = $this->companyMemberModel->username ?: '';
                    $jobModel->create_type = BaseJob::CREATE_TYPE_SELF;
                }
                if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                    // 运营后台添加的
                    $jobModel->creator     = $this->adminModel->username ?: '';
                    $jobModel->create_type = BaseJob::CREATE_TYPE_AGENT;
                }

                // 检查有无修改审核字段
                $editField = [
                    'duty'        => $item['duty'],
                    'requirement' => $item['requirement'],
                    'remark'      => $item['remark'],
                    'job_id'      => $item['job_id'],
                ];
                // 这里是如果没有审核通过历史或没有修改审核字段的职位直接修改生效
                if ($jobModel->status == BaseJob::STATUS_WAIT || !$this->isAuditJobField($editField)) {
                    $jobModel->announcement_id      = $this->announcementId;
                    $jobModel->member_id            = $this->companyMemberModel->id;
                    $jobModel->company_id           = $this->companyId;
                    $jobModel->create_id            = $this->operatorId;
                    $jobModel->name                 = $item['name'];
                    $jobModel->code                 = $item['code'];
                    $jobModel->job_category_id      = $item['job_category_id'];
                    $jobModel->education_type       = $item['education_type'];
                    $jobModel->major_id             = $item['major_id'] ?: '';
                    $jobModel->nature_type          = $item['nature_type'];
                    $jobModel->wage_type            = $item['wage_type'];
                    $jobModel->is_negotiable        = $item['is_negotiable'];
                    $jobModel->apply_type           = $item['apply_type'] ?: '';
                    $jobModel->apply_address        = $item['apply_address'];
                    $jobModel->min_wage             = $item['min_wage'];
                    $jobModel->max_wage             = $item['max_wage'];
                    $jobModel->experience_type      = $item['experience_type'];
                    $jobModel->age_type             = $item['age_type'];
                    $jobModel->title_type           = $item['title_type'];
                    $jobModel->political_type       = $item['political_type'];
                    $jobModel->abroad_type          = $item['abroad_type'];
                    $jobModel->amount               = $item['amount'];
                    $jobModel->department           = $item['department'];
                    $jobModel->province_id          = $item['province_id'];
                    $jobModel->city_id              = $item['city_id'];
                    $jobModel->district_id          = $item['district_id'];
                    $jobModel->address              = $item['address'] ?: '';
                    $jobModel->welfare_tag          = $item['welfare_tag'];
                    $jobModel->is_show              = BaseJob::IS_SHOW_YES;
                    $jobModel->apply_audit_time     = CUR_DATETIME;
                    $jobModel->duty                 = $item['duty'];
                    $jobModel->requirement          = $item['requirement'];
                    $jobModel->remark               = $item['remark'];
                    $jobModel->period_date          = $this->isPeriodDate($item['period_date']);
                    $jobModel->delivery_limit_type  = $item['delivery_limit_type'] ?: '';
                    $jobModel->delivery_way         = $item['delivery_way'] ?: '';
                    $jobModel->delivery_type        = $item['delivery_type'] ?: '';
                    $jobModel->extra_notify_address = $item['extra_notify_address'] ?: '';
                    if ($baseData['submitType'] == self::SAVE_TYPE_STAGING) {
                        $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT;
                    } elseif ($baseData['submitType'] == self::SAVE_TYPE_AUDIT && $this->isAuditJobField($editField)) {
                        $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
                    }
                    $jobModel->establishment_type = $item['establishment_type'] ?: '';
                    $jobModel->is_establishment   = $item['is_establishment'];
                    if (!$jobModel->save()) {
                        throw new Exception($jobModel->getFirstErrorsMessage());
                    }
                    //合作单位
                    if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
                        //处理一下职位联系人与职位协同人
                        //写入职位联系人与职位协同
                        $params_data = [
                            'job_id'                  => $jobModel->id,
                            'company_id'              => $this->companyId,
                            'announcement_id'         => $this->announcementId,
                            'job_contact_id'          => $item['contact_id'],
                            'job_contact_synergy_ids' => $item['contact_synergy_id'] ? explode(',',
                                $item['contact_synergy_id']) : [],
                        ];
                        $this->contact($params_data);
                    }
                }
            }
        }
        // 职位插入主表后删除临时职位数据
        BaseJobTemp::deleteAll([
            'in',
            'id',
            $this->jobTempIds,
        ]);

        // 自动归属公告栏目规则
        //$this->autoClassifyRun();
    }

    /**
     * 编辑职位（有审核通过历史）
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private function editorJob()
    {
        foreach ($this->jobTempData as $item) {
            // 编辑的职位状态
            $jobStatus = BaseJob::findOneVal(['id' => $item['job_id']], 'status');

            // 编辑的职位临时数据
            if ($item['create_type'] == self::CREATE_TYPE_EDIT && $item['is_temp'] == BaseJobTemp::IS_TEMP_YES && $jobStatus == BaseJob::STATUS_ONLINE) {
                $editList = [
                    'duty'        => $item['duty'],
                    'requirement' => $item['requirement'],
                    'remark'      => $item['remark'],
                ];
                $select   = [
                    'duty',
                    'requirement',
                    'remark',
                ];

                $jobInfo          = BaseJob::selectInfo(['id' => $item['job_id']], $select);
                $modifyAfterList  = array_diff_assoc($editList, $jobInfo);
                $modifyBeforeList = [];
                foreach ($modifyAfterList as $k => $list) {
                    $modifyBeforeList[$k] = $jobInfo[$k];
                }

                // 这里判断是否修改了duty，requirement，remark
                if (empty($modifyAfterList)) {
                    //这里修改了除duty，requirement，remark其他字段无需审核直接生效
                    $model                       = BaseJob::findOne(['id' => $item['job_id']]);
                    $model->name                 = $item['name'];
                    $model->code                 = $item['code'];
                    $model->job_category_id      = $item['job_category_id'];
                    $model->education_type       = $item['education_type'];
                    $model->major_id             = $item['major_id'] ?: '';
                    $model->nature_type          = $item['nature_type'];
                    $model->wage_type            = $item['wage_type'];
                    $model->is_negotiable        = $item['is_negotiable'];
                    $model->apply_type           = $item['apply_type'] ?: '';
                    $model->apply_address        = $item['apply_address'];
                    $model->min_wage             = $item['min_wage'];
                    $model->max_wage             = $item['max_wage'];
                    $model->experience_type      = $item['experience_type'];
                    $model->age_type             = $item['age_type'];
                    $model->title_type           = $item['title_type'];
                    $model->political_type       = $item['political_type'];
                    $model->abroad_type          = $item['abroad_type'];
                    $model->amount               = $item['amount'];
                    $model->department           = $item['department'];
                    $model->province_id          = $item['province_id'];
                    $model->city_id              = $item['city_id'];
                    $model->district_id          = $item['district_id'];
                    $model->address              = $item['address'] ?: '';
                    $model->welfare_tag          = $item['welfare_tag'];
                    $model->period_date          = $this->isPeriodDate($item['period_date']);
                    $model->delivery_limit_type  = $item['delivery_limit_type'] ?: '';
                    $model->delivery_type        = $item['delivery_type'] ?: '';
                    $model->delivery_way         = $item['delivery_way'] ?: '';
                    $model->extra_notify_address = $item['extra_notify_address'] ?: '';
                    //判断操作平台为运营平台
                    if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                        $model->establishment_type = $item['establishment_type'] ?: '';
                        $model->is_establishment   = !empty($item['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
                    }
                    if (!$model->save()) {
                        throw new Exception($model->getFirstErrorsMessage());
                    }
                } else {
                    $editContent = json_encode($modifyAfterList);
                    $list        = [
                        'job_id'          => $item['job_id'],
                        'add_time'        => CUR_DATETIME,
                        'status'          => BaseJobEdit::STATUS_ONLINE,
                        'edit_content'    => $editContent,
                        'editor_id'       => $this->operatorId ?: 0,
                        'editor_type'     => $this->operatorType,
                        'editor'          => $this->operatorUserName ?: '',
                        'announcement_id' => $this->announcementId,
                    ];

                    // 检测之前是否有职位编辑内容，有就覆盖，没有就新建
                    $jobEditInfo = BaseJobEdit::selectInfo(['job_id' => $item['job_id']], ['id']);
                    if ($jobEditInfo['id']) {
                        $condition = ['id' => $jobEditInfo['id']];
                        BaseJobEdit::updateAll($list, $condition);
                    } else {
                        BaseJobEdit::createInfo($list);
                    }

                    //这里存职位操作表
                    $changeModifyBeforeList = [];
                    $changeModifyAfterList  = [];
                    // 修改前数据
                    foreach ($modifyBeforeList as $k => $v) {
                        switch ($k) {
                            case 'duty':
                                $changeModifyBeforeList['duty'] = $v;
                                break;
                            case 'requirement':
                                $changeModifyBeforeList['requirement'] = $v;
                                break;
                            case 'remark':
                                $changeModifyBeforeList['remark'] = $v;
                                break;
                        }
                    }

                    // 修改后数据
                    foreach ($modifyAfterList as $k => $v) {
                        switch ($k) {
                            case 'duty':
                                $changeModifyAfterList['duty'] = $v;
                                break;
                            case 'requirement':
                                $changeModifyAfterList['requirement'] = $v;
                                break;
                            case 'remark':
                                $changeModifyAfterList['remark'] = $v;
                                break;
                        }
                    }

                    $handleBefore = json_encode($changeModifyBeforeList);
                    $handleAfter  = json_encode($changeModifyAfterList);
                    $jobHandleLog = [
                        'add_time'      => CUR_DATETIME,
                        'job_id'        => $item['job_id'],
                        'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_EDIT,
                        'handler_type'  => $this->operatorType,
                        'handler_id'    => $this->operatorId,
                        'handler_name'  => $this->operatorUserName ?: '',
                        'handle_before' => $handleBefore,
                        'handle_after'  => $handleAfter,
                        'ip'            => IpHelper::getIpInt(),
                    ];
                    BaseJobHandleLog::createInfo($jobHandleLog);

                    $jobModel = BaseJob::findOne(['id' => $item['job_id']]);
                    if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                        // 企业的
                        $jobModel->creator = $this->companyMemberModel->username ?: '';
                    }

                    if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                        // 运营后台添加的
                        $jobModel->creator = $this->adminModel->username ?: '';
                    }
                    $jobModel->announcement_id      = $this->announcementId;
                    $jobModel->create_type          = BaseJob::CREATE_TYPE_AGENT;
                    $jobModel->member_id            = $this->companyMemberModel->id;
                    $jobModel->company_id           = $this->companyId;
                    $jobModel->create_id            = $this->operatorId;
                    $jobModel->name                 = $item['name'];
                    $jobModel->code                 = $item['code'];
                    $jobModel->job_category_id      = $item['job_category_id'];
                    $jobModel->education_type       = $item['education_type'];
                    $jobModel->major_id             = $item['major_id'] ?: '';
                    $jobModel->nature_type          = $item['nature_type'];
                    $jobModel->wage_type            = $item['wage_type'];
                    $jobModel->is_negotiable        = $item['is_negotiable'];
                    $jobModel->apply_type           = $item['apply_type'] ?: '';
                    $jobModel->apply_address        = $item['apply_address'];
                    $jobModel->min_wage             = $item['min_wage'];
                    $jobModel->max_wage             = $item['max_wage'];
                    $jobModel->experience_type      = $item['experience_type'];
                    $jobModel->age_type             = $item['age_type'];
                    $jobModel->title_type           = $item['title_type'];
                    $jobModel->political_type       = $item['political_type'];
                    $jobModel->abroad_type          = $item['abroad_type'];
                    $jobModel->amount               = $item['amount'];
                    $jobModel->department           = $item['department'];
                    $jobModel->province_id          = $item['province_id'];
                    $jobModel->city_id              = $item['city_id'];
                    $jobModel->district_id          = $item['district_id'];
                    $jobModel->address              = $item['address'] ?: '';
                    $jobModel->welfare_tag          = $item['welfare_tag'];
                    $jobModel->period_date          = $this->isPeriodDate($item['period_date']);
                    $jobModel->audit_status         = $item['audit_status'];
                    $jobModel->apply_audit_time     = CUR_DATETIME;
                    $jobModel->delivery_limit_type  = $item['delivery_limit_type'] ?: '';
                    $jobModel->delivery_type        = $item['delivery_type'] ?: '';
                    $jobModel->delivery_way         = $item['delivery_way'] ?: '';
                    $jobModel->extra_notify_address = $item['extra_notify_address'] ?: '';
                    if (!$jobModel->save()) {
                        throw new Exception($jobModel->getFirstErrorsMessage());
                    }

                    // 有修改需审核的字段
                    $this->isAuditJob = 1;
                }

                //合作单位
                if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
                    //处理一下职位联系人与职位协同人
                    //写入职位联系人与职位协同
                    $params_data = [
                        'job_id'                  => $item['job_id'],
                        'company_id'              => $this->companyId,
                        'announcement_id'         => $this->announcementId,
                        'job_contact_id'          => $item['contact_id'],
                        'job_contact_synergy_ids' => $item['contact_synergy_id'] ? explode(',',
                            $item['contact_synergy_id']) : [],
                    ];
                    $this->contact($params_data);
                }
            }
        }

        // 职位插入主表后删除临时职位数据
        BaseJobTemp::deleteAll([
            'in',
            'id',
            $this->jobTempIds,
        ]);

        // 自动归属公告栏目规则
        //        $this->autoClassifyRun();
    }

    private function isAuditJobField($field)
    {
        $editList = [
            'duty'        => $field['duty'],
            'requirement' => $field['requirement'],
            'remark'      => $field['remark'],
        ];
        $select   = [
            'duty',
            'requirement',
            'remark',
        ];
        $jobInfo  = BaseJob::selectInfo(['id' => $field['job_id']], $select);
        // 检查是否有修改审核字段
        $modifyAfterList = array_diff_assoc($editList, $jobInfo);

        if (!empty($modifyAfterList)) {
            return true;
        } else {
            return false;
        }
    }

    private function setArticleColumnAttribute($columnIdsArr = [], $comboAttribute = [])
    {
        // 添加栏目id
        // BaseArticleColumn::createColumnId($this->articleId, $columnIdsArr);
        // 添加公告属性
        BaseArticleAttribute::createAttribute($this->articleId, $comboAttribute);
    }

    private function isPeriodDate($periodDate)
    {
        if ($periodDate == '详见正文' || $periodDate == '详见公告' || $periodDate == '详见公告正文' || empty($periodDate) || $periodDate == TimeHelper::ZERO_TIME) {
            return TimeHelper::ZERO_TIME;
        } else {
            if (ValidateHelper::checkDateIsValid($periodDate)) {
                return $periodDate;
            } else {
                throw new Exception('日期不合法');
            }
        }
    }

    /**
     * 检查职位附件记录是否存在
     * @param $fileIds
     * @throws Exception
     */
    private function checkJobAppendix($fileIds)
    {
        $fileIdArr = explode(',', $fileIds);
        $ids       = BaseFile::find()
            ->select('id')
            ->where(['id' => $fileIdArr])
            ->asArray()
            ->column();
        if (!$ids) {
            throw new Exception('职位附件记录不存在');
        }
    }

    /**
     * 创建公告操作日志
     * @param $announcementId
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function createAnnouncementHandleLog()
    {
        //操作动作入表
        $handleBefore = [
            'action_type' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[$this->handleType],
        ];
        $handleAfter  = [
            'action_type' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[$this->handleType],
        ];
        if ($this->isContent == 1) {
            $handleBefore['content'] = $this->oldContent;
            $handleAfter['content']  = $this->newContent;
        }
        if ($this->isFileIds == 1) {
            $handleBefore['file_ids'] = $this->oldFileIds;
            $handleAfter['file_ids']  = $this->fileIds;
        }
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)$this->handleType,
            'editor_type'     => $this->editorType,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

    /**
     * 对职位的联系人与协同人进行处理
     * @param $params
     */
    private function contact($params)
    {
        //写入职位联系人与职位协同
        $contact_insert                           = [
            'job_id'          => $params['job_id'],
            'company_id'      => $params['company_id'],
            'announcement_id' => $params['announcement_id'],
        ];
        $contact_synergy_insert                   = $contact_insert;
        $contact_insert['company_member_info_id'] = $params['job_contact_id'];

        BaseJobContact::add($contact_insert);
        $contact_synergy_insert['company_member_info_id'] = $params['job_contact_synergy_ids'];
        BaseJobContactSynergy::addBatch($contact_synergy_insert);
    }
}
