<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_auto_classify_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $status 状态
 * @property int $job_id 职位id
 * @property string $before_home_column_ids 自动分配前所属栏目ids
 * @property string $after_home_column_ids 自动分配后所属栏目ids
 * @property string $remark 职位分配的过程记录
 */
class JobAutoClassifyLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_auto_classify_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status', 'job_id'], 'integer'],
            [['remark'], 'string'],
            [['before_home_column_ids'], 'string', 'max' => 512],
            [['after_home_column_ids'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'job_id' => 'Job ID',
            'before_home_column_ids' => 'Before Home Column Ids',
            'after_home_column_ids' => 'After Home Column Ids',
            'remark' => 'Remark',
        ];
    }
}
