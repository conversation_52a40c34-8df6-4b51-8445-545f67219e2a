<?php

namespace common\service\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\service\jobTop\JobTopApplication;
use yii\base\Exception;

class OnlineOfflineService extends BaseService
{
    const ACTION_TYPE_ONLINE  = 1;
    const ACTION_TYPE_OFFLINE = 2;

    public $onlineOfflineType;
    public $jobHandleLog;
    public $data;

    /**
     * 执行
     * @throws Exception
     */
    public function run()
    {
        switch ($this->onlineOfflineType) {
            case self::ACTION_TYPE_ONLINE:
                $this->online();
                break;
            case self::ACTION_TYPE_OFFLINE:
                $this->offline();
                break;
            default:
                throw new Exception('操作类型错误');
        }
    }

    /**
     * 设置职位再发布
     * @return $this
     */
    public function setOnline(): OnlineOfflineService
    {
        $this->onlineOfflineType = self::ACTION_TYPE_ONLINE;

        return $this;
    }

    /**
     * 设置职位下线
     * @return $this
     */
    public function setOffline(): OnlineOfflineService
    {
        $this->onlineOfflineType = self::ACTION_TYPE_OFFLINE;
        $this->handleType        = BaseJobHandleLog::HANDLE_TYPE_OFFLINE;

        return $this;
    }

    /**
     * 下线
     * @throws Exception
     */
    private function offline()
    {
        $this->offlineRun();
    }

    /**
     * 再发布
     * @throws Exception
     */
    private function online()
    {
        $this->onlineRun();
    }

    /**
     * 设置好要处理的再发布数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data, $jobHandleLog): OnlineOfflineService
    {
        $this->jobId        = $data['id'];
        $this->data         = $data;
        $this->jobHandleLog = $jobHandleLog;

        return $this;
    }

    /**
     * 操作再发布
     */
    private function onlineRun()
    {
        $this->afterRun();
    }

    /**
     * 操作下线
     * @throws Exception
     */
    private function offlineRun()
    {
        $model = BaseJob::findOne(['id' => $this->jobId]);

        $model->status       = $this->data['status'];
        $model->audit_status = $this->data['auditStatus'];
        $model->offline_type = $this->data['offlineType'];
        $model->offline_time = date('Y-m-d H:i:s');
        // 不应该修改到期时间
        // $model->period_date    = $this->data['periodDate'];
        $model->offline_reason = $this->data['offlineReason'];

        if (!$model->save()) {
            throw new Exception('操作下线失败');
        }

        //更新置顶状态
        $app = JobTopApplication::getInstance();
        $app->updateStatusByJobStatus($this->jobId);

        $this->afterRun();
    }

    /**
     * 在执行后
     * @throws Exception
     */
    protected function afterRun()
    {
        $this->createJobHandleLog();
    }

    /**
     * 职位操作日志
     * @throws Exception
     */
    private function createJobHandleLog()
    {
        BaseJobHandleLog::createInfo($this->jobHandleLog);
    }

    private function checkPermission()
    {
        return true;
    }

}
