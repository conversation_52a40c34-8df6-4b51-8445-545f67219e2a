<?php
namespace common\libs;

use Exception;

class ImapMailClient
{
    private $mailbox;
    private $username;
    private $password;
    private $inbox;

    public function __construct($host, $username, $password, $port = 993, $ssl = true)
    {
        $this->username = $username;
        $this->password = $password;

        $flag          = $ssl ? '/imap/ssl/novalidate-cert' : '/imap/notls';
        $this->mailbox = "{" . $host . ":" . $port . "$flag}INBOX";

        $this->inbox = @imap_open($this->mailbox, $this->username, $this->password);
        if (!$this->inbox) {
            throw new Exception("无法连接邮箱：" . imap_last_error());
        }
    }

    public function __destruct()
    {
        if ($this->inbox) {
            imap_close($this->inbox);
        }
    }

    public function getEmails($criteria = 'ALL')
    {
        $emails = imap_search($this->inbox, $criteria, SE_UID);
        if (!$emails) {
            return [];
        }

        rsort($emails);
        $results = [];
        foreach ($emails as $uid) {
            $overview  = imap_fetch_overview($this->inbox, $uid, FT_UID)[0];
            $results[] = [
                'uid'     => $uid,
                'subject' => $this->decodeMimeStr($overview->subject ?? ''),
                'from'    => $this->decodeMimeStr($overview->from ?? ''),
                'to'      => $this->decodeMimeStr($overview->to ?? ''),
                'date'    => $overview->date ?? '',
                'body'    => $this->getBody($uid),
                'seen'    => isset($overview->seen) && $overview->seen ? true : false,
            ];
        }

        return $results;
    }

    private function getBody($uid)
    {
        $structure = imap_fetchstructure($this->inbox, $uid, FT_UID);
        $body      = '';

        if ($structure && isset($structure->parts)) {
            foreach ($structure->parts as $partNum => $part) {
                if ($part->type == 0) {
                    $bodyPart = imap_fetchbody($this->inbox, $uid, $partNum + 1, FT_UID);
                    $body     .= $this->decodeBody($bodyPart, $part->encoding, $part->parameters ?? []);
                    break;
                }
            }
        } else {
            $bodyPart = imap_fetchbody($this->inbox, $uid, 1, FT_UID);
            $body     .= $this->decodeBody($bodyPart, $structure->encoding ?? 0, $structure->parameters ?? []);
        }

        return $body;
    }

    private function decodeBody($body, $encoding, $parameters = [])
    {
        switch ((int)$encoding) {
            case 3:
                $body = base64_decode($body);
                break;
            case 4:
                $body = quoted_printable_decode($body);
                break;
        }

        $charset = 'UTF-8';
        foreach ($parameters as $param) {
            if (strtolower($param->attribute ?? '') === 'charset') {
                $charset = strtoupper($param->value);
                break;
            }
        }

        if ($charset !== 'UTF-8') {
            $body = @mb_convert_encoding($body, 'UTF-8', $charset);
        }

        return $body;
    }

    private function decodeMimeStr($str)
    {
        $elements = imap_mime_header_decode($str);
        $result   = '';
        foreach ($elements as $el) {
            $charset = strtoupper($el->charset);
            $text    = $el->text;
            if ($charset !== 'DEFAULT' && $charset !== 'UTF-8') {
                $text = @mb_convert_encoding($text, 'UTF-8', $charset);
            }
            $result .= $text;
        }

        return $result;
    }

    /**
     * 标记邮件为已读
     */
    public function markAsRead($uid)
    {
        imap_setflag_full($this->inbox, $uid, "\\Seen", ST_UID);
    }

    /**
     * 删除邮件
     */
    public function deleteEmail($uid)
    {
        imap_delete($this->inbox, $uid, FT_UID);
        imap_expunge($this->inbox);
    }

    /**
     * 下载附件到指定目录
     */
    public function downloadAttachments($uid, $savePath = './attachments')
    {
        $structure   = imap_fetchstructure($this->inbox, $uid, FT_UID);
        $attachments = [];

        if (isset($structure->parts) && count($structure->parts)) {
            foreach ($structure->parts as $i => $part) {
                $isAttachment = false;
                $filename     = '';

                if (isset($part->disposition) && strtolower($part->disposition) === 'attachment') {
                    $isAttachment = true;
                }

                if (isset($part->dparameters)) {
                    foreach ($part->dparameters as $obj) {
                        if (strtolower($obj->attribute) === 'filename') {
                            $filename     = $this->decodeMimeStr($obj->value);
                            $isAttachment = true;
                            break;
                        }
                    }
                }

                if ($isAttachment && $filename) {
                    $body = imap_fetchbody($this->inbox, $uid, $i + 1, FT_UID);
                    if ($part->encoding == 3) {
                        $body = base64_decode($body);
                    } elseif ($part->encoding == 4) {
                        $body = quoted_printable_decode($body);
                    }

                    if (!is_dir($savePath)) {
                        mkdir($savePath, 0777, true);
                    }

                    $filepath = rtrim($savePath, '/') . '/' . $filename;
                    file_put_contents($filepath, $body);
                    $attachments[] = $filepath;
                }
            }
        }

        return $attachments;
    }

    /**
     * 自定义保存逻辑（比如写入数据库）
     * 回调参数：每封邮件的数组结构
     */
    public function saveToDatabase(callable $callback, $criteria = 'ALL')
    {
        $emails = $this->getEmails($criteria);
        foreach ($emails as $mail) {
            $callback($mail);
        }
    }

    /**
     * 按日期范围获取邮件
     * @param string $startDate 开始日期 (Y-m-d 格式)
     * @param string $endDate 结束日期 (Y-m-d 格式)
     * @return array
     */
    public function getEmailsByDateRange($startDate, $endDate)
    {
        $criteria = "SINCE \"$startDate\" BEFORE \"" . date('Y-m-d', strtotime($endDate . ' +1 day')) . "\"";
        return $this->getEmails($criteria);
    }

    /**
     * 获取今天的邮件
     * @return array
     */
    public function getTodayEmails()
    {
        $today = date('Y-m-d');
        return $this->getEmailsByDateRange($today, $today);
    }

    /**
     * 获取昨天的邮件
     * @return array
     */
    public function getYesterdayEmails()
    {
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        return $this->getEmailsByDateRange($yesterday, $yesterday);
    }

    /**
     * 获取本周的邮件
     * @return array
     */
    public function getThisWeekEmails()
    {
        $startOfWeek = date('Y-m-d', strtotime('monday this week'));
        $endOfWeek = date('Y-m-d', strtotime('sunday this week'));
        return $this->getEmailsByDateRange($startOfWeek, $endOfWeek);
    }

    /**
     * 获取本月的邮件
     * @return array
     */
    public function getThisMonthEmails()
    {
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        return $this->getEmailsByDateRange($startOfMonth, $endOfMonth);
    }

    /**
     * 获取最近N天的邮件
     * @param int $days 天数
     * @return array
     */
    public function getRecentEmails($days = 7)
    {
        $startDate = date('Y-m-d', strtotime("-$days days"));
        $endDate = date('Y-m-d');
        return $this->getEmailsByDateRange($startDate, $endDate);
    }

    /**
     * 获取未读邮件
     * @return array
     */
    public function getUnreadEmails()
    {
        return $this->getEmails('UNSEEN');
    }

    /**
     * 获取已读邮件
     * @return array
     */
    public function getReadEmails()
    {
        return $this->getEmails('SEEN');
    }

    /**
     * 按发件人获取邮件
     * @param string $sender 发件人邮箱或名称
     * @return array
     */
    public function getEmailsBySender($sender)
    {
        $criteria = "FROM \"$sender\"";
        return $this->getEmails($criteria);
    }

    /**
     * 按收件人获取邮件
     * @param string $recipient 收件人邮箱
     * @return array
     */
    public function getEmailsByRecipient($recipient)
    {
        $criteria = "TO \"$recipient\"";
        return $this->getEmails($criteria);
    }

    /**
     * 按主题关键词获取邮件
     * @param string $keyword 关键词
     * @return array
     */
    public function getEmailsBySubject($keyword)
    {
        $criteria = "SUBJECT \"$keyword\"";
        return $this->getEmails($criteria);
    }

    /**
     * 按邮件内容关键词获取邮件
     * @param string $keyword 关键词
     * @return array
     */
    public function getEmailsByContent($keyword)
    {
        $criteria = "BODY \"$keyword\"";
        return $this->getEmails($criteria);
    }

    /**
     * 组合条件获取邮件
     * @param array $conditions 条件数组
     * 支持的条件：
     * - 'from' => '发件人'
     * - 'to' => '收件人'
     * - 'subject' => '主题关键词'
     * - 'body' => '内容关键词'
     * - 'since' => '开始日期(Y-m-d)'
     * - 'before' => '结束日期(Y-m-d)'
     * - 'unseen' => true (未读)
     * - 'seen' => true (已读)
     * @return array
     */
    public function getEmailsByConditions($conditions)
    {
        $criteria = [];

        if (isset($conditions['from'])) {
            $criteria[] = "FROM \"{$conditions['from']}\"";
        }

        if (isset($conditions['to'])) {
            $criteria[] = "TO \"{$conditions['to']}\"";
        }

        if (isset($conditions['subject'])) {
            $criteria[] = "SUBJECT \"{$conditions['subject']}\"";
        }

        if (isset($conditions['body'])) {
            $criteria[] = "BODY \"{$conditions['body']}\"";
        }

        if (isset($conditions['since'])) {
            $criteria[] = "SINCE \"{$conditions['since']}\"";
        }

        if (isset($conditions['before'])) {
            $criteria[] = "BEFORE \"{$conditions['before']}\"";
        }

        if (isset($conditions['unseen']) && $conditions['unseen']) {
            $criteria[] = "UNSEEN";
        }

        if (isset($conditions['seen']) && $conditions['seen']) {
            $criteria[] = "SEEN";
        }

        $searchCriteria = empty($criteria) ? 'ALL' : implode(' ', $criteria);
        return $this->getEmails($searchCriteria);
    }

    /**
     * 获取邮件统计信息
     * @return array
     */
    public function getEmailStats()
    {
        $totalEmails = imap_num_msg($this->inbox);
        $unreadEmails = count($this->getUnreadEmails());
        $todayEmails = count($this->getTodayEmails());
        $thisWeekEmails = count($this->getThisWeekEmails());
        $thisMonthEmails = count($this->getThisMonthEmails());

        return [
            'total' => $totalEmails,
            'unread' => $unreadEmails,
            'read' => $totalEmails - $unreadEmails,
            'today' => $todayEmails,
            'this_week' => $thisWeekEmails,
            'this_month' => $thisMonthEmails,
        ];
    }

    /**
     * 批量标记邮件为已读
     * @param array $uids UID数组
     */
    public function markMultipleAsRead($uids)
    {
        if (empty($uids)) {
            return;
        }

        $uidList = implode(',', $uids);
        imap_setflag_full($this->inbox, $uidList, "\\Seen", ST_UID);
    }

    /**
     * 批量删除邮件
     * @param array $uids UID数组
     */
    public function deleteMultipleEmails($uids)
    {
        if (empty($uids)) {
            return;
        }

        foreach ($uids as $uid) {
            imap_delete($this->inbox, $uid, FT_UID);
        }
        imap_expunge($this->inbox);
    }

    /**
     * 获取邮箱文件夹列表
     * @return array
     */
    public function getFolders()
    {
        $folders = imap_list($this->inbox, $this->mailbox, "*");
        $result = [];

        if ($folders) {
            foreach ($folders as $folder) {
                $result[] = str_replace($this->mailbox, '', $folder);
            }
        }

        return $result;
    }

    /**
     * 移动邮件到指定文件夹
     * @param int $uid 邮件UID
     * @param string $folder 目标文件夹
     */
    public function moveEmailToFolder($uid, $folder)
    {
        $targetFolder = str_replace('INBOX', $folder, $this->mailbox);
        imap_mail_move($this->inbox, $uid, $targetFolder, CP_UID);
        imap_expunge($this->inbox);
    }
}