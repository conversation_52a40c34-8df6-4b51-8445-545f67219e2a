<?php
/**
 * create user：shannon
 * create time：2024/4/19 09:06
 */
namespace common\service\companyAuth;

use common\service\CommonService;

class BaseButtonService extends CommonService
{
    /** 按钮样式类型 */
    const BUTTON_TYPE_DEFAULT = '';
    const BUTTON_TYPE_PRIMARY = 'primary';
    const BUTTON_TYPE_DANGER  = 'danger';
    const BUTTON_TYPE_SUCCESS = 'success';
    const BUTTON_TYPE_INFO    = 'info';
    const BUTTON_TYPE_WARNING = 'warning';

    /** 类型定义 */
    const TIPS_TYPE_EQUITY_COMPARE              = 1;//权益对比
    const TIPS_TYPE_EQUITY_SINGLE_REFRESH       = 2;//单项权益-刷新
    const TIPS_TYPE_EQUITY_SINGLE_TOP           = 3;//单项权益-置顶
    const TIPS_TYPE_EQUITY_SINGLE_CHAT          = 4;//单项权益-直聊
    const TIPS_TYPE_EQUITY_SINGLE_INVITE        = 5;//单项权益-邀约
    const TIPS_TYPE_TOAST_NON_SENIOR            = 6;//全局-非高级会员提醒-toast
    const TIPS_TYPE_TOAST_UPGRADE_ACCOUNT       = 7;//全局-升级账号权限提醒-toast
    const TIPS_TYPE_TOAST_ACCOUNT_NO_PERMISSION = 8;//全局-VIP账号无权限提醒-toast
    const TIPS_TYPE_TOAST_RESUME_VIEW_LIMIT     = 9;//全局-简历查看次数限制-toast

    /** 权益 */
    const TIPS_TYPE_EQUITY = [
        self::TIPS_TYPE_EQUITY_COMPARE,
        self::TIPS_TYPE_EQUITY_SINGLE_REFRESH,
        self::TIPS_TYPE_EQUITY_SINGLE_TOP,
        self::TIPS_TYPE_EQUITY_SINGLE_CHAT,
        self::TIPS_TYPE_EQUITY_SINGLE_INVITE,
    ];

    /** 特殊的按钮触发单项权益 */
    const TIPS_TYPE_SPECIAL = [
        //刷新
        self::BUTTON_JOB_REFRESH                  => self::TIPS_TYPE_EQUITY_SINGLE_REFRESH,
        self::BUTTON_JOB_BATCH_REFRESH            => self::TIPS_TYPE_EQUITY_SINGLE_REFRESH,
        //直聊
        self::BUTTON_ONLINE_COMMUNICATION         => self::TIPS_TYPE_EQUITY_SINGLE_CHAT,
        //工作台-直聊
        self::BUTTON_ONLINE_COMMUNICATION_SPECIAL => self::TIPS_TYPE_EQUITY_SINGLE_CHAT,
        //邀约
        self::BUTTON_INVITE_DELIVERY              => self::TIPS_TYPE_EQUITY_SINGLE_INVITE,
        //工作台-邀约
        self::BUTTON_INVITE_DELIVERY_SPECIAL      => self::TIPS_TYPE_EQUITY_SINGLE_INVITE,
    ];

    /** 刷新 */
    const BUTTON_JOB_REFRESH       = 100001;
    const BUTTON_JOB_REFRESH_NAME  = '刷新';
    const BUTTON_JOB_REFRESH_EVENT = 'jobRefreshEvent';
    /** 批量刷新 */
    const BUTTON_JOB_BATCH_REFRESH       = 110002;
    const BUTTON_JOB_BATCH_REFRESH_NAME  = '批量刷新';
    const BUTTON_JOB_BATCH_REFRESH_EVENT = 'jobRefreshBatchEvent';
    /** 已刷新 */
    const BUTTON_JOB_REFRESHED       = 100002;
    const BUTTON_JOB_REFRESHED_NAME  = '已刷新';
    const BUTTON_JOB_REFRESHED_EVENT = 'jobRefreshedEvent';
    /** 编辑 */
    const BUTTON_JOB_EDIT       = 100003;
    const BUTTON_JOB_EDIT_NAME  = '编辑';
    const BUTTON_JOB_EDIT_EVENT = 'jobEditEvent';
    /** 复制 */
    const BUTTON_JOB_COPY       = 100004;
    const BUTTON_JOB_COPY_NAME  = '复制';
    const BUTTON_JOB_COPY_EVENT = 'jobCopyEvent';
    /** 下线 */
    const BUTTON_JOB_OFFLINE       = 100005;
    const BUTTON_JOB_OFFLINE_NAME  = '下线';
    const BUTTON_JOB_OFFLINE_EVENT = 'jobOfflineEvent';
    /** 再发布 */
    const BUTTON_JOB_PUBLISHING       = 100006;
    const BUTTON_JOB_PUBLISHING_NAME  = '再发布';
    const BUTTON_JOB_PUBLISHING_EVENT = 'jobPublishingEvent';
    /** 通过初筛 */
    const BUTTON_PASS_SCREENING       = 100007;
    const BUTTON_PASS_SCREENING_NAME  = '通过初筛';
    const BUTTON_PASS_SCREENING_EVENT = 'applyPassScreeningEvent';
    /** 邀请面试 */
    const BUTTON_INVITE_INTERVIEW       = 100008;
    const BUTTON_INVITE_INTERVIEW_NAME  = '邀请面试';
    const BUTTON_INVITE_INTERVIEW_EVENT = 'applyInviteInterviewEvent';
    /** 再次邀面 */
    const BUTTON_REINVITE_INTERVIEW       = 100009;
    const BUTTON_REINVITE_INTERVIEW_NAME  = '再次邀面';
    const BUTTON_REINVITE_INTERVIEW_EVENT = 'applyReInviteInterviewEvent';
    /** 待录用 */
    const BUTTON_WAIT_EMPLOY       = 100010;
    const BUTTON_WAIT_EMPLOY_NAME  = '待录用';
    const BUTTON_WAIT_EMPLOY_EVENT = 'applyWaitEmployEvent';
    /** 已录用 */
    const BUTTON_EMPLOYED       = 100011;
    const BUTTON_EMPLOYED_NAME  = '已录用';
    const BUTTON_EMPLOYED_EVENT = 'applyEmployedEvent';
    /** 已入职 */
    const BUTTON_ONBOARD       = 100012;
    const BUTTON_ONBOARD_NAME  = '已入职';
    const BUTTON_ONBOARD_EVENT = 'applyOnboardEvent';
    /** 未入职 */
    const BUTTON_NOT_ONBOARD       = 100013;
    const BUTTON_NOT_ONBOARD_NAME  = '未入职';
    const BUTTON_NOT_ONBOARD_EVENT = 'applyNotOnboardEvent';
    /** 撤销录用 */
    const BUTTON_CANCEL_EMPLOY       = 100014;
    const BUTTON_CANCEL_EMPLOY_NAME  = '撤销录用';
    const BUTTON_CANCEL_EMPLOY_EVENT = 'applyCancelEmployEvent';
    /** 未接通 */
    const BUTTON_NOT_CONNECTED       = 100015;
    const BUTTON_NOT_CONNECTED_NAME  = '未接通';
    const BUTTON_NOT_CONNECTED_EVENT = 'applyNotConnectedEvent';
    /** 无意向 */
    const BUTTON_NO_INTENTION       = 100016;
    const BUTTON_NO_INTENTION_NAME  = '无意向';
    const BUTTON_NO_INTENTION_EVENT = 'applyNoIntentionEvent';
    /** 未到面 */
    const BUTTON_NOT_INTERVIEW       = 100017;
    const BUTTON_NOT_INTERVIEW_NAME  = '未到面';
    const BUTTON_NOT_INTERVIEW_EVENT = 'applyNotInterviewEvent';
    /** 不合适 */
    const BUTTON_NOT_SUITABLE       = 100018;
    const BUTTON_NOT_SUITABLE_NAME  = '不合适';
    const BUTTON_NOT_SUITABLE_EVENT = 'applyNotSuitableEvent';
    /** 分享 */
    const BUTTON_SHARE                              = 100019;
    const BUTTON_SHARE_NAME                         = '分享';
    const BUTTON_SHARE_EVENT                        = 'shareEvent';//人才库
    const BUTTON_RESUME_LIBRARY_SHARE               = 110019;
    const BUTTON_RESUME_LIBRARY_SHARE_EVENT         = 'shareResumeLibraryEvent';//人才库
    const BUTTON_COMPANY_RESUME_LIBRARY_SHARE       = 120019;
    const BUTTON_COMPANY_RESUME_LIBRARY_SHARE_EVENT = 'shareCompanyResumeLibraryEvent';//单位简历库
    /** 撤销不合适 */
    const BUTTON_CANCEL_NOT_SUITABLE       = 100020;
    const BUTTON_CANCEL_NOT_SUITABLE_NAME  = '撤销不合适';
    const BUTTON_CANCEL_NOT_SUITABLE_EVENT = 'applyCancelNotSuitableEvent';
    /** 邀约投递 */
    const BUTTON_INVITE_DELIVERY       = 100021;
    const BUTTON_INVITE_DELIVERY_NAME  = '邀约投递';
    const BUTTON_INVITE_DELIVERY_EVENT = 'inviteDeliveryEvent';
    /** 收藏 */
    const BUTTON_COLLECT       = 100022;
    const BUTTON_COLLECT_NAME  = '收藏';
    const BUTTON_COLLECT_EVENT = 'collectEvent';
    /** 取消收藏 */
    const BUTTON_CANCEL_COLLECT       = 100023;
    const BUTTON_CANCEL_COLLECT_NAME  = '取消收藏';
    const BUTTON_CANCEL_COLLECT_EVENT = 'cancelCollectEvent';
    /** 在线沟通 */
    const BUTTON_ONLINE_COMMUNICATION       = 100024;
    const BUTTON_ONLINE_COMMUNICATION_NAME  = '在线沟通';
    const BUTTON_ONLINE_COMMUNICATION_EVENT = 'onlineCommunicationEvent';
    /** 查看 */
    const BUTTON_VIEW       = 100025;
    const BUTTON_VIEW_NAME  = '查看';
    const BUTTON_VIEW_EVENT = 'viewEvent';
    /** 下载 */
    const BUTTON_DOWNLOAD                              = 100026;
    const BUTTON_DOWNLOAD_NAME                         = '下载';
    const BUTTON_DOWNLOAD_EVENT                        = 'downloadEvent';
    const BUTTON_RESUME_LIBRARY_DOWNLOAD               = 110026;
    const BUTTON_RESUME_LIBRARY_DOWNLOAD_EVENT         = 'downloadResumeLibraryEvent';//人才库
    const BUTTON_COMPANY_RESUME_LIBRARY_DOWNLOAD       = 120026;
    const BUTTON_COMPANY_RESUME_LIBRARY_DOWNLOAD_EVENT = 'downloadCompanyResumeLibraryEvent';//单位简历库
    /** 撤销已入职 */
    const BUTTON_CANCEL_ONBOARD       = 100027;
    const BUTTON_CANCEL_ONBOARD_NAME  = '撤销已入职';
    const BUTTON_CANCEL_ONBOARD_EVENT = 'applyCancelOnboardEvent';
    /** 撤销未入职 */
    const BUTTON_CANCEL_NOT_ONBOARD       = 100028;
    const BUTTON_CANCEL_NOT_ONBOARD_NAME  = '撤销未入职';
    const BUTTON_CANCEL_NOT_ONBOARD_EVENT = 'applyCancelNotOnboardEvent';
    /** 公告-编辑 */
    const BUTTON_ANNOUNCEMENT_EDIT       = 100029;
    const BUTTON_ANNOUNCEMENT_EDIT_NAME  = '编辑';
    const BUTTON_ANNOUNCEMENT_EDIT_EVENT = 'announcementEditEvent';
    /** 公告-下线 */
    const BUTTON_ANNOUNCEMENT_OFFLINE       = 100030;
    const BUTTON_ANNOUNCEMENT_OFFLINE_NAME  = '下线';
    const BUTTON_ANNOUNCEMENT_OFFLINE_EVENT = 'announcementOfflineEvent';
    /** 公告-再发布 */
    const BUTTON_ANNOUNCEMENT_PUBLISHING       = 100031;
    const BUTTON_ANNOUNCEMENT_PUBLISHING_NAME  = '再发布';
    const BUTTON_ANNOUNCEMENT_PUBLISHING_EVENT = 'announcementPublishingEvent';

    ////=========================特殊按钮==========
    /** 在线沟通 */
    const BUTTON_ONLINE_COMMUNICATION_SPECIAL       = 880001;
    const BUTTON_ONLINE_COMMUNICATION_SPECIAL_NAME  = '在线沟通';
    const BUTTON_ONLINE_COMMUNICATION_SPECIAL_EVENT = 'onlineCommunicationSpecialEvent';
    /** 邀约投递 */
    const BUTTON_INVITE_DELIVERY_SPECIAL       = 880002;
    const BUTTON_INVITE_DELIVERY_SPECIAL_NAME  = '邀约投递';
    const BUTTON_INVITE_DELIVERY_SPECIAL_EVENT = 'inviteDeliverySpecialEvent';
    /** 分页 */
    const BUTTON_INVITE_RESUME_LIMITE_PAGE_SPECIAL       = 880003;
    const BUTTON_INVITE_RESUME_LIMITE_PAGE_SPECIAL_NAME  = '分页';
    const BUTTON_INVITE_RESUME_LIMITE_PAGE_SPECIAL_EVENT = 'resumeLimitPageEvent';

    /** 发布职位 */
    const BUTTON_JOB_PUBLISH       = 910001;
    const BUTTON_JOB_PUBLISH_NAME  = '发布职位';
    const BUTTON_JOB_PUBLISH_EVENT = 'jobPublishEvent';
    /** 发布公告&简章 */
    const BUTTON_ANNOUNCEMENT_PUBLISH       = 920002;
    const BUTTON_ANNOUNCEMENT_PUBLISH_NAME  = '发布公告&简章';
    const BUTTON_ANNOUNCEMENT_PUBLISH_EVENT = 'announcementPublishEvent';

    /**
     * 按钮组结构
     */
    const BUTTON_GROUP = [
        self::BUTTON_JOB_REFRESH        => [
            'id'         => self::BUTTON_JOB_REFRESH,
            'name'       => self::BUTTON_JOB_REFRESH_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_REFRESH_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
            0,
        ],
        self::BUTTON_JOB_BATCH_REFRESH  => [
            'id'         => self::BUTTON_JOB_BATCH_REFRESH,
            'name'       => self::BUTTON_JOB_BATCH_REFRESH_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_BATCH_REFRESH_EVENT,
            'icon'       => '',
            'type'       => '',
        ],
        self::BUTTON_JOB_REFRESHED      => [
            'id'         => self::BUTTON_JOB_REFRESHED,
            'name'       => self::BUTTON_JOB_REFRESHED_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_REFRESHED_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
        ],
        self::BUTTON_JOB_EDIT           => [
            'id'         => self::BUTTON_JOB_EDIT,
            'name'       => self::BUTTON_JOB_EDIT_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_EDIT_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_JOB_COPY           => [
            'id'         => self::BUTTON_JOB_COPY,
            'name'       => self::BUTTON_JOB_COPY_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_COPY_EVENT,
            'icon'       => 'el-icon-copy-document',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_JOB_OFFLINE        => [
            'id'         => self::BUTTON_JOB_OFFLINE,
            'name'       => self::BUTTON_JOB_OFFLINE_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_OFFLINE_EVENT,
            'icon'       => 'el-icon-download',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_JOB_PUBLISHING     => [
            'id'         => self::BUTTON_JOB_PUBLISHING,
            'name'       => self::BUTTON_JOB_PUBLISHING_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_PUBLISHING_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
        ],
        self::BUTTON_PASS_SCREENING     => [
            'id'         => self::BUTTON_PASS_SCREENING,
            'name'       => self::BUTTON_PASS_SCREENING_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_PASS_SCREENING_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_INVITE_INTERVIEW   => [
            'id'         => self::BUTTON_INVITE_INTERVIEW,
            'name'       => self::BUTTON_INVITE_INTERVIEW_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_INVITE_INTERVIEW_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_REINVITE_INTERVIEW => [
            'id'         => self::BUTTON_REINVITE_INTERVIEW,
            'name'       => self::BUTTON_REINVITE_INTERVIEW_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_REINVITE_INTERVIEW_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_WAIT_EMPLOY        => [
            'id'         => self::BUTTON_WAIT_EMPLOY,
            'name'       => self::BUTTON_WAIT_EMPLOY_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_WAIT_EMPLOY_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_EMPLOYED           => [
            'id'         => self::BUTTON_EMPLOYED,
            'name'       => self::BUTTON_EMPLOYED_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_EMPLOYED_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ONBOARD            => [
            'id'         => self::BUTTON_ONBOARD,
            'name'       => self::BUTTON_ONBOARD_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ONBOARD_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_NOT_ONBOARD        => [
            'id'         => self::BUTTON_NOT_ONBOARD,
            'name'       => self::BUTTON_NOT_ONBOARD_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_NOT_ONBOARD_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_CANCEL_EMPLOY      => [
            'id'         => self::BUTTON_CANCEL_EMPLOY,
            'name'       => self::BUTTON_CANCEL_EMPLOY_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_CANCEL_EMPLOY_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_NOT_CONNECTED      => [
            'id'         => self::BUTTON_NOT_CONNECTED,
            'name'       => self::BUTTON_NOT_CONNECTED_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_NOT_CONNECTED_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_NO_INTENTION       => [
            'id'         => self::BUTTON_NO_INTENTION,
            'name'       => self::BUTTON_NO_INTENTION_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_NO_INTENTION_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_NOT_INTERVIEW      => [
            'id'         => self::BUTTON_NOT_INTERVIEW,
            'name'       => self::BUTTON_NOT_INTERVIEW_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_NOT_INTERVIEW_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_NOT_SUITABLE       => [
            'id'         => self::BUTTON_NOT_SUITABLE,
            'name'       => self::BUTTON_NOT_SUITABLE_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_NOT_SUITABLE_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_SHARE              => [
            'id'         => self::BUTTON_SHARE,
            'name'       => self::BUTTON_SHARE_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_SHARE_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],

        self::BUTTON_CANCEL_NOT_SUITABLE          => [
            'id'         => self::BUTTON_CANCEL_NOT_SUITABLE,
            'name'       => self::BUTTON_CANCEL_NOT_SUITABLE_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_CANCEL_NOT_SUITABLE_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_INVITE_DELIVERY              => [
            'id'         => self::BUTTON_INVITE_DELIVERY,
            'name'       => self::BUTTON_INVITE_DELIVERY_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_INVITE_DELIVERY_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_COLLECT                      => [
            'id'         => self::BUTTON_COLLECT,
            'name'       => self::BUTTON_COLLECT_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_COLLECT_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_CANCEL_COLLECT               => [
            'id'         => self::BUTTON_CANCEL_COLLECT,
            'name'       => self::BUTTON_CANCEL_COLLECT_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_CANCEL_COLLECT_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ONLINE_COMMUNICATION         => [
            'id'         => self::BUTTON_ONLINE_COMMUNICATION,
            'name'       => self::BUTTON_ONLINE_COMMUNICATION_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ONLINE_COMMUNICATION_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
        ],
        self::BUTTON_VIEW                         => [
            'id'         => self::BUTTON_VIEW,
            'name'       => self::BUTTON_VIEW_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_VIEW_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_DOWNLOAD                     => [
            'id'         => self::BUTTON_DOWNLOAD,
            'name'       => self::BUTTON_DOWNLOAD_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_DOWNLOAD_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_CANCEL_ONBOARD               => [
            'id'         => self::BUTTON_CANCEL_ONBOARD,
            'name'       => self::BUTTON_CANCEL_ONBOARD_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_CANCEL_ONBOARD_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_CANCEL_NOT_ONBOARD           => [
            'id'         => self::BUTTON_CANCEL_NOT_ONBOARD,
            'name'       => self::BUTTON_CANCEL_NOT_ONBOARD_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_CANCEL_NOT_ONBOARD_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ANNOUNCEMENT_EDIT            => [
            'id'         => self::BUTTON_ANNOUNCEMENT_EDIT,
            'name'       => self::BUTTON_ANNOUNCEMENT_EDIT_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ANNOUNCEMENT_EDIT_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ANNOUNCEMENT_OFFLINE         => [
            'id'         => self::BUTTON_ANNOUNCEMENT_OFFLINE,
            'name'       => self::BUTTON_ANNOUNCEMENT_OFFLINE_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ANNOUNCEMENT_OFFLINE_EVENT,
            'icon'       => 'el-icon-download',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ANNOUNCEMENT_PUBLISHING      => [
            'id'         => self::BUTTON_ANNOUNCEMENT_PUBLISHING,
            'name'       => self::BUTTON_ANNOUNCEMENT_PUBLISHING_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ANNOUNCEMENT_PUBLISHING_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
        ],

        //特殊的按钮
        self::BUTTON_ONLINE_COMMUNICATION_SPECIAL => [
            'id'         => self::BUTTON_ONLINE_COMMUNICATION_SPECIAL,
            'name'       => self::BUTTON_ONLINE_COMMUNICATION_SPECIAL_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ONLINE_COMMUNICATION_SPECIAL_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_INVITE_DELIVERY_SPECIAL      => [
            'id'         => self::BUTTON_INVITE_DELIVERY_SPECIAL,
            'name'       => self::BUTTON_INVITE_DELIVERY_SPECIAL_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_INVITE_DELIVERY_SPECIAL_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_PRIMARY,
        ],
        self::BUTTON_JOB_PUBLISH                  => [
            'id'         => self::BUTTON_JOB_PUBLISH,
            'name'       => self::BUTTON_JOB_PUBLISH_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_JOB_PUBLISH_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
        self::BUTTON_ANNOUNCEMENT_PUBLISH         => [
            'id'         => self::BUTTON_ANNOUNCEMENT_PUBLISH,
            'name'       => self::BUTTON_ANNOUNCEMENT_PUBLISH_NAME,
            'isShow'     => false,
            'isDisabled' => false,
            'event'      => self::BUTTON_ANNOUNCEMENT_PUBLISH_EVENT,
            'icon'       => '',
            'type'       => self::BUTTON_TYPE_DEFAULT,
        ],
    ];

    /**
     * 由于前端时间驱动所以这里定义一下事件换ID
     */
    const BUTTON_EVENT_TO_ID = [
        self::BUTTON_JOB_REFRESH_EVENT                       => self::BUTTON_JOB_REFRESH,
        self::BUTTON_JOB_REFRESHED_EVENT                     => self::BUTTON_JOB_REFRESHED,
        self::BUTTON_JOB_EDIT_EVENT                          => self::BUTTON_JOB_EDIT,
        self::BUTTON_JOB_COPY_EVENT                          => self::BUTTON_JOB_COPY,
        self::BUTTON_JOB_OFFLINE_EVENT                       => self::BUTTON_JOB_OFFLINE,
        self::BUTTON_JOB_PUBLISHING_EVENT                    => self::BUTTON_JOB_PUBLISHING,
        self::BUTTON_PASS_SCREENING_EVENT                    => self::BUTTON_PASS_SCREENING,
        self::BUTTON_INVITE_INTERVIEW_EVENT                  => self::BUTTON_INVITE_INTERVIEW,
        self::BUTTON_REINVITE_INTERVIEW_EVENT                => self::BUTTON_REINVITE_INTERVIEW,
        self::BUTTON_WAIT_EMPLOY_EVENT                       => self::BUTTON_WAIT_EMPLOY,
        self::BUTTON_EMPLOYED_EVENT                          => self::BUTTON_EMPLOYED,
        self::BUTTON_ONBOARD_EVENT                           => self::BUTTON_ONBOARD,
        self::BUTTON_NOT_ONBOARD_EVENT                       => self::BUTTON_NOT_ONBOARD,
        self::BUTTON_CANCEL_EMPLOY_EVENT                     => self::BUTTON_CANCEL_EMPLOY,
        self::BUTTON_NOT_CONNECTED_EVENT                     => self::BUTTON_NOT_CONNECTED,
        self::BUTTON_NO_INTENTION_EVENT                      => self::BUTTON_NO_INTENTION,
        self::BUTTON_NOT_INTERVIEW_EVENT                     => self::BUTTON_NOT_INTERVIEW,
        self::BUTTON_NOT_SUITABLE_EVENT                      => self::BUTTON_NOT_SUITABLE,
        //        self::BUTTON_SHARE_EVENT                           => self::BUTTON_SHARE,
        self::BUTTON_RESUME_LIBRARY_SHARE_EVENT              => self::BUTTON_RESUME_LIBRARY_SHARE,
        self::BUTTON_COMPANY_RESUME_LIBRARY_SHARE_EVENT      => self::BUTTON_COMPANY_RESUME_LIBRARY_SHARE,
        self::BUTTON_CANCEL_NOT_SUITABLE_EVENT               => self::BUTTON_CANCEL_NOT_SUITABLE,
        self::BUTTON_INVITE_DELIVERY_EVENT                   => self::BUTTON_INVITE_DELIVERY,
        self::BUTTON_COLLECT_EVENT                           => self::BUTTON_COLLECT,
        self::BUTTON_CANCEL_COLLECT_EVENT                    => self::BUTTON_CANCEL_COLLECT,
        self::BUTTON_ONLINE_COMMUNICATION_EVENT              => self::BUTTON_ONLINE_COMMUNICATION,
        self::BUTTON_VIEW_EVENT                              => self::BUTTON_VIEW,
        //        self::BUTTON_DOWNLOAD_EVENT                        => self::BUTTON_DOWNLOAD,
        self::BUTTON_RESUME_LIBRARY_DOWNLOAD_EVENT           => self::BUTTON_RESUME_LIBRARY_DOWNLOAD,
        self::BUTTON_COMPANY_RESUME_LIBRARY_DOWNLOAD_EVENT   => self::BUTTON_COMPANY_RESUME_LIBRARY_DOWNLOAD,
        self::BUTTON_CANCEL_ONBOARD_EVENT                    => self::BUTTON_CANCEL_ONBOARD,
        self::BUTTON_CANCEL_NOT_ONBOARD_EVENT                => self::BUTTON_CANCEL_NOT_ONBOARD,
        self::BUTTON_ANNOUNCEMENT_EDIT_EVENT                 => self::BUTTON_ANNOUNCEMENT_EDIT,
        self::BUTTON_ANNOUNCEMENT_OFFLINE_EVENT              => self::BUTTON_ANNOUNCEMENT_OFFLINE,
        self::BUTTON_ANNOUNCEMENT_PUBLISHING_EVENT           => self::BUTTON_ANNOUNCEMENT_PUBLISHING,
        self::BUTTON_ONLINE_COMMUNICATION_SPECIAL_EVENT      => self::BUTTON_ONLINE_COMMUNICATION_SPECIAL,
        self::BUTTON_INVITE_DELIVERY_SPECIAL_EVENT           => self::BUTTON_INVITE_DELIVERY_SPECIAL,
        self::BUTTON_JOB_PUBLISH_EVENT                       => self::BUTTON_JOB_PUBLISH,
        self::BUTTON_ANNOUNCEMENT_PUBLISH_EVENT              => self::BUTTON_ANNOUNCEMENT_PUBLISH,
        self::BUTTON_INVITE_RESUME_LIMITE_PAGE_SPECIAL_EVENT => self::BUTTON_INVITE_RESUME_LIMITE_PAGE_SPECIAL,
    ];
}