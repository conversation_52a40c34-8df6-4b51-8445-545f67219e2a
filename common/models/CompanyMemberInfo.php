<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_member_info".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $member_id 账号ID
 * @property int $company_id 单位id(隶属单位ID)
 * @property string $contact 联系人
 * @property string $department 所在部门
 * @property int $member_rule 账号权限  1普通权限 2VIP权限 9超管权限
 * @property int $company_member_type 账号类型 0主账号 1子账号
 * @property int $create_id 创建人ID
 * @property int $source_type 创建来源 1自主 2运营
 * @property int $is_wx_bind 是否绑定微信0未绑定 1绑定
 * @property int $is_remember_sms_chat 是否记住直聊短信发送
 * @property int $is_remember_sms_invite 是否记住投递短信发送
 */
class CompanyMemberInfo extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_member_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['member_id', 'company_id', 'member_rule', 'company_member_type', 'create_id', 'source_type', 'is_wx_bind', 'is_remember_sms_chat', 'is_remember_sms_invite'], 'integer'],
            [['member_rule', 'company_member_type'], 'required'],
            [['contact'], 'string', 'max' => 64],
            [['department'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'contact' => 'Contact',
            'department' => 'Department',
            'member_rule' => 'Member Rule',
            'company_member_type' => 'Company Member Type',
            'create_id' => 'Create ID',
            'source_type' => 'Source Type',
            'is_wx_bind' => 'Is Wx Bind',
            'is_remember_sms_chat' => 'Is Remember Sms Chat',
            'is_remember_sms_invite' => 'Is Remember Sms Invite',
        ];
    }
}
