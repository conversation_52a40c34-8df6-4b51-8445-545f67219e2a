<?php

namespace common\service\payment;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageDetail;
use common\base\models\BaseResumeEquityPackageRelationSetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeOrder;
use common\base\models\BaseResumeOrderSnapshot;
use common\helpers\DebugHelper;
use common\libs\Cache;
use common\libs\SmsQueue;
use common\libs\WxWork;
use common\service\payment\ResumeServer as ResumePaymentServer;
use common\service\payTransformBuriedPoint\OderLogService;
use frontendPc\models\ResumeEquityPackageRelationSetting;
use frontendPc\models\ResumeEquitySetting;
use frontendPc\models\ResumeOrder;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 求职者支付
 */
class ResumeServer extends BaseService
{
    /**
     * 执行
     */
    public function run()
    {
        try {
            switch ($this->operation) {
                case self::PAY_ORDER:
                    $res = $this->order();
                    break;
                case self::PAY_NOTIFY:
                    $res = $this->notify();
                    break;
                case self::PAY_QUERY:
                    $res = $this->query();
                    break;
                case self::PAY_UPDATEORDER:
                    $res = $this->updateOrder($this->params['orderNo'], $this->params['tradeNo'],
                        $this->params['notify']);
                    break;
                default:
                    throw new Exception('operation error');
            }

            return $res;
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 支付拉起
     */
    private function order()
    {
        $resumeId        = $this->params['resumeId'];
        $equityPackageId = $this->params['equityPackageId'];
        $openId          = $this->params['openId'] ?? '';
        // 校验权益组合
        $equityPackageRow = BaseResumeEquityPackageSetting::findOne($equityPackageId);

        // 不同的端需要不同的参数，这里有一个数值需要保留下来
        $realAmount = $equityPackageRow['real_amount'];

        if ($equityPackageRow['status'] != BaseResumeEquityPackageSetting::STATUS_ONLINE) {
            throw new Exception('当前权益包无法购买');
        }

        // 预创建平台订单号
        $orderNo = $this->getOrderNo();
        // 订单号是否重复
        if (BaseResumeOrder::find()
            ->where(['order_no' => $orderNo])
            ->exists()) {
            // 重新创建
            $orderNo = $this->getOrderNo();
        }

        //这里判断一下当前人的会员类型
        //如果是钻石会员，且想开通的是黄金会员就提示不能开通
        ///获取简历信息
        $resumeInfo = BaseResume::findOne($resumeId);
        if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND) {
            if ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP) {
                throw new Exception('尊敬的钻石VIP会员，您当前已拥有最高会员权益，请于钻石VIP套餐过期后再购买黄金VIP套餐。');
            }
        }
        //这里判断一下是会员就不让开洞察
        if ($resumeInfo->vip_type == BaseResume::VIP_TYPE_ACTIVE && $equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
            $vip_name = $resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND ? BaseResume::VIP_LEVEL_DIAMOND_TEXT : BaseResume::VIP_LEVEL_GOLD_TEXT;
            throw new Exception('当前VIP套餐已含竞争力洞察相关权益，无须重复购买');
        }

        // 活动检查
        $memberId  = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
        $checkData = [
            'resumeId'        => $resumeId,
            'equityPackageId' => $equityPackageId,
            'memberId'        => $memberId,
        ];
        if ($equityPackageRow->equity_package_type === BaseResumeEquityPackageSetting::EQUITY_PACKAGE_TYPE_ACTIVITY) {
            $this->checkActivityNew($checkData);
        }

        // 缓存限制1分钟只能创建60个预支付订单
        if ($createOrderNumLimit = Yii::$app->params['createOrderNumLimit']) {
            if ($limit = Cache::get(Cache::ALL_RESUME_ORDER_CREATE_LIMIT_KEY . ':' . $resumeId)) {
                if ($limit >= $createOrderNumLimit) {
                    throw new Exception('请勿频繁操作');
                } else {
                    Cache::incr(Cache::ALL_RESUME_ORDER_CREATE_LIMIT_KEY . ':' . $resumeId);
                }
            } else {
                Cache::set(Cache::ALL_RESUME_ORDER_CREATE_LIMIT_KEY . ':' . $resumeId, 1, 60);
            }
        }
        // 校验金额
        if ($equityPackageRow['real_amount'] < 0.01) {
            throw new Exception('真实金额不合法');
        }
        if ($equityPackageRow['original_amount'] < 0.01) {
            throw new Exception('原始金额不合法');
        }
        //这里判断一下是否是黄金会员升级成为钻石会员，进行价格折算
        //获取简历信息
        $vipUpgrade          = false;
        $snapshotConvertData = [];
        $resumeInfo          = BaseResume::findOne($resumeId);
        if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD && $equityPackageRow['equity_package_category_id'] == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
            //这时候需要进行价格折算
            $convertData = BaseResumeEquityPackageSetting::goldConvertDiamond($resumeId, $equityPackageId);
            if (is_array($convertData) && !empty($convertData)) {
                $snapshotConvertData             = $convertData;
                $vipUpgrade                      = true;
                $equityPackageRow['real_amount'] = $convertData['real_amount'];
            }
        }

        // 下单数据
        $resumeOrder                    = new BaseResumeOrder;
        $resumeOrder->status            = BaseResumeOrder::STATUS_CREATED;
        $resumeOrder->equity_status     = BaseResumeOrder::STATUS_EQUITY_FAIL;
        $resumeOrder->resume_id         = $resumeId;
        $resumeOrder->equity_package_id = $equityPackageRow['id'];
        $resumeOrder->payway            = $this->payway;
        $resumeOrder->platform          = $this->platform;
        $resumeOrder->pay_channel       = $this->payChannel;
        try {
            $ip = Yii::$app->request->userIP;
        } catch (\Exception $e) {
            $ip = '';
        }
        $resumeOrder->ip                  = $ip;
        $resumeOrder->original_amount     = $equityPackageRow['original_amount'];
        $resumeOrder->real_amount         = $this->platform != BaseResumeOrder::PLATFORM_ADMIN ? $equityPackageRow['real_amount'] : $this->params['realAmount'];
        $resumeOrder->order_no            = $orderNo;
        $resumeOrder->equity_package_type = $equityPackageRow->equity_package_type;

        if (!$resumeOrder->save()) {
            throw new Exception($resumeOrder->getFirstErrorsMessage());
        }

        //保存用户付费转化埋点日志
        $payLogApp = new OderLogService();
        $payLogApp->setOparetion(ResumePaymentServer::PAY_ORDER)
            ->setPlatform($this->platform)
            ->setData([
                'uuid'            => $this->params['uuid'],
                'orderNo'         => $orderNo,
                'equityPackageId' => $equityPackageId,
            ])
            ->addLog();

        //保存快照记录
        $this->saveSnapshot($resumeOrder->id, $vipUpgrade, $snapshotConvertData);

        // 拉起支付
        switch ($this->payway) {
            case BaseResumeOrder::PAYWAY_WXPAY:
                $server           = new WxPayServer();
                $server->orderId  = $resumeOrder->id;
                $server->platform = $resumeOrder->platform;
                $server->orderNo  = $orderNo;
                // 测试环境区分名称
                $server->body = Yii::$app->params['environment'] != 'prod' ? '测:' . $equityPackageRow['name'] : $equityPackageRow['name'];
                // 测试环境支付0.01元
                // $server->amount     = Yii::$app->params['environment'] != 'prod' ? $equityPackageRow['real_amount'] : $equityPackageRow['real_amount'];
                $server->amount     = Yii::$app->params['environment'] != 'prod' ? 0.01 : $equityPackageRow['real_amount'];
                $server->returnUrl  = $this->returnUrl;
                $server->notifyUrl  = $this->notifyUrl;
                $server->payChannel = $this->payChannel;
                $server->openId     = $openId;
                $res                = $server->run();
                break;
            case BaseResumeOrder::PAYWAY_DAIFU:
            case BaseResumeOrder::PAYWAY_SHARE:
                //直接跳过
                $res['orderId'] = $resumeOrder->id;
                break;
            default:
                throw new Exception('payway error');
        }

        $res['price'] = $equityPackageRow['real_amount'];
        //实际抵扣金额
        $res['convertPrice'] = $convertData['convert_price'] ?? 0;
        //真实能抵扣的金额
        $res['realOfferPrice'] = $convertData['real_offer_price'] ?? 0;
        // 下单的商品名称
        $res['equityPackageName'] = $equityPackageRow['name'];

        // 如果是小程序订单，金额全部改为小数点后两位
        if ($this->platform == BaseResumeOrder::PLATFORM_MINI) {
            $res['price']        = number_format($res['price'], 2, '.', '');
            $res['convertPrice'] = number_format($res['convertPrice'], 2, '.', '');
            // 原价
            $res['realAmount']     = number_format($realAmount, 2, '.', '');
            $res['realOfferPrice'] = number_format($res['realOfferPrice'], 2, '.', '');
        }

        //这里做一下处理---新的订单生成将会处理当前用户前面所有订单实现失效---将订单状态改成取消
        $cancelOrder = BaseResumeOrder::find()
            ->select([
                'id',
                'resume_id',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeOrder::STATUS_CREATED,
            ])
            ->andWhere([
                '<',
                'id',
                $resumeOrder->id,
            ])
            ->asArray()
            ->all();
        foreach ($cancelOrder as $order) {
            $model         = BaseResumeOrder::findOne($order['id']);
            $model->status = BaseResumeOrder::STATUS_CANCELED;
            if ($model->save()) {
                //删除缓存
                $orderKey = Cache::ALL_RESUME_ORDER_KEY . ':' . $order['resume_id'] . ':' . $order['id'];
                Cache::delete($orderKey);
                //设置缓存
                $item_info                 = $model->toArray();
                $item_info['error_status'] = -1;
                Cache::set($orderKey, json_encode($item_info), 60 * 30);
            }
        }

        return $res;
    }

    /**
     * 支付查询
     */
    private function query()
    {
        $resumeId = $this->params['resumeId'];
        $orderId  = $this->params['orderId'];

        $orderKey = Cache::ALL_RESUME_ORDER_KEY . ':' . $resumeId . ':' . $orderId;

        // 先查询缓存
        if (Cache::get($orderKey)) {
            // 读缓存
            $orderRow = json_decode(Cache::get($orderKey), true);
        } else {
            // 订单数据
            $orderRow = BaseResumeOrder::findOne($orderId);
            // 缓存30分钟
            Cache::set($orderKey, json_encode($orderRow ? $orderRow->toArray() : []), 60 * 30);
        }

        if (empty($orderRow)) {
            throw new Exception('订单不存在');
        }

        //校验订单
        if ($orderRow['resume_id'] != $resumeId) {
            throw new Exception('订单不存在');
        }

        // 订单状态
        $jobResourcesInfo = [];
        if ($orderRow['status'] == BaseResumeOrder::STATUS_PAID) {
            // 查询对应权益组合名称
            $equityPackageName = BaseResumeEquityPackageSetting::findOneVal(['id' => $orderRow['equity_package_id']],
                'name');

            $tips            = "您已成功购买{$equityPackageName}";
            $equityPackageId = ResumeOrder::findOneVal(['id' => $orderId], 'equity_package_id');
            // 查询权益组合下面的权益
            $equityIds = BaseResumeEquityPackageRelationSetting::getEquityIdsByPackageId($equityPackageId,
                BaseResumeEquitySetting::STATUS_ONLINE);
            // 查询权益id是否包含求职资源
            if (in_array(ResumeEquitySetting::ID_JOB_RESOURCES, $equityIds)) {
                // 获取权益组合信息
                $equityPackageRow = BaseResumeEquityPackageSetting::findOne($equityPackageId);
                // 获取求职资源二维码链接
                // $info['url']      = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHI8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyaDc1a2RVZjlmR0QxRVFtVWhBY18AAgS0W69kAwSAOgkA';
                $content1      = '套餐内所包含的“求职资料包”权益，需您扫码关注【高才-高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！';
                $content2      = '（开通“钻石VIP”套餐 或 “黄金VIP·180天”套餐的会员用户，需回复“会员课程”，领取“高才优课”课程学习。）';
                $info          = BaseResumeEquity::getJobResources($resumeId);
                $info['title'] = "已为您开通“{$equityPackageRow['subname']}”，服务时长为{$equityPackageRow['days']}天";
                // 小程序端
                if ($this->platform == BaseResumeOrder::PLATFORM_MINI) {
                    // 需要对参数做一次处理
                    $jobResourcesInfo = [
                        'title'       => '下单成功',
                        'content'     => $info['title'],
                        'useTitle'    => $content1,
                        'useSubtitle' => $content2,
                        'url'         => $info['url'],
                    ];
                } else {
                    $info['successContent'] = $content1 . $content2;
                    $jobResourcesInfo       = $info;
                }
            } else {
                // 根据不同的端拿不同的icon
                $pcVipSuccessIcon              = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/vip.png';
                $h5VipSuccessIcon              = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/h5/vip.png';
                $pcJobFastSuccessIcon          = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/job_fast.png';
                $h5JobFastSuccessIcon          = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/h5/job_fast.png';
                $pcCompetitivenessAnalysisIcon = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/competitiveness_analysis.png';
                $h5CompetitivenessAnalysisIcon = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/success/h5/competitiveness_analysis.png';

                if (PLATFORM == 'PC') {
                    $vipIcon                     = $pcVipSuccessIcon;
                    $jobFastIcon                 = $pcJobFastSuccessIcon;
                    $competitivenessAnalysisIcon = $pcCompetitivenessAnalysisIcon;
                } else {
                    $vipIcon                     = $h5VipSuccessIcon;
                    $jobFastIcon                 = $h5JobFastSuccessIcon;
                    $competitivenessAnalysisIcon = $h5CompetitivenessAnalysisIcon;
                }

                // 这里小程序再重置一下
                if ($this->platform == BaseResumeOrder::PLATFORM_MINI) {
                    $equityPackageRow = BaseResumeEquityPackageSetting::findOne($equityPackageId);
                    $jobResourcesInfo = [
                        'title'       => '下单成功',
                        'content'     => "已为您开通“{$equityPackageRow['subname']}”，服务时长为{$equityPackageRow['days']}天",
                        'useTitle'    => '',
                        'useSubtitle' => '',
                        'url'         => '',
                    ];

                    // 1.9 版本新增逻辑
                    // 不同的类型，不同的链接和icon
                    if ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
                        $jobResourcesInfo['contentList'] = [
                            [
                                'icon'      => $vipIcon,
                                'title'     => '高才VIP',
                                'subTitle'  => '11+求职特权，',
                                'url'       => BaseResume::BUY_URL_VIP,
                                'linkLabel' => '立即解锁 >',
                            ],
                            [
                                'icon'      => $jobFastIcon,
                                'title'     => '求职快',
                                'subTitle'  => '想加快求职进程？',
                                'url'       => BaseResume::BUY_URL_JOB_FAST,
                                'linkLabel' => '去置顶投递 >',
                            ],
                        ];
                    } elseif ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_JOB_FAST) {
                        $jobResourcesInfo['contentList'] = [
                            [
                                'icon'      => $vipIcon,
                                'title'     => '高才VIP',
                                'subTitle'  => '11+求职特权，',
                                'url'       => BaseResume::BUY_URL_VIP,
                                'linkLabel' => '立即解锁 >',
                            ],
                            [
                                'icon'      => $competitivenessAnalysisIcon,
                                'title'     => '竞争力分析',
                                'subTitle'  => '你符合招聘要求吗？',
                                'url'       => BaseResume::BUY_URL_COMPETITIVE_POWER,
                                'linkLabel' => '立即检测 >',
                            ],
                        ];
                    }
                }

                // PC端的情况下(1.9版本pc端新增的需求)
                if ($this->platform == BaseResumeOrder::PLATFORM_WEB || $this->platform == BaseResumeOrder::PLATFORM_H5) {
                    // 求职快或者竞争力洞察
                    $equityPackageRow = BaseResumeEquityPackageSetting::findOne($equityPackageId);
                    if ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_INSIGHT || $equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_JOB_FAST) {
                        // 成功信息面板
                        $noticeCard = [
                            'title'    => '下单成功',
                            'subTitle' => "已为你开通“{$equityPackageName}”",
                            'lineTxt'  => '推荐搭配以下服务，获得更多职场机会',
                        ];
                    }

                    // 不同的类型，不同的链接和icon
                    if ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
                        $noticeCard['contentList'] = [
                            [
                                'icon'      => $vipIcon,
                                'title'     => '高才VIP',
                                'subTitle'  => '11+求职特权，',
                                'url'       => BaseResume::BUY_URL_VIP,
                                'linkLabel' => '立即解锁 >',
                            ],
                            [
                                'icon'      => $jobFastIcon,
                                'title'     => '求职快',
                                'subTitle'  => '想加快求职进程？',
                                'url'       => BaseResume::BUY_URL_JOB_FAST,
                                'linkLabel' => '去置顶投递 >',
                            ],
                        ];
                    } elseif ($equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_JOB_FAST) {
                        $noticeCard['contentList'] = [
                            [
                                'icon'      => $vipIcon,
                                'title'     => '高才VIP',
                                'subTitle'  => '11+求职特权，',
                                'url'       => BaseResume::BUY_URL_VIP,
                                'linkLabel' => '立即解锁 >',
                            ],
                            [
                                'icon'      => $competitivenessAnalysisIcon,
                                'title'     => '竞争力洞察',
                                'subTitle'  => '你符合招聘要求吗？',
                                'url'       => BaseResume::BUY_URL_COMPETITIVE_POWER,
                                'linkLabel' => '立即检测 >',
                            ],
                        ];
                    }
                }
            }
            // 在这里处理一下不同端进来的逻辑
        } elseif ($orderRow['status'] == BaseResumeOrder::STATUS_CREATED) {
            $tips = '您的订单尚未支付，请继续操作！';
        } elseif ($orderRow['status'] == BaseResumeOrder::STATUS_CANCELED) {
            $tips = '您的订单已取消，请重新下单！';
        } else {
            throw new Exception('订单状态异常');
        }

        $return = [
            'status'       => $orderRow['status'],
            'error_status' => $orderRow['error_status'] ?? 0,
            'tips'         => $tips,
        ];

        // 这里还做一层判断，如果有noticeCard的话 , 就不会有 jobResourcesInfo
        if (!empty($noticeCard)) {
            $return['noticeCard'] = $noticeCard;
        } else {
            $return['jobResourcesInfo'] = $jobResourcesInfo;
        }

        return $return;
    }

    /**
     * 支付回调
     */
    private function notify()
    {
        $payway = $this->payway;

        switch ($payway) {
            case BaseResumeOrder::PAYWAY_WXPAY:
                $this->notifyWxPay();
                break;
            default:
                throw new Exception('payway error');
        }

        return true;
    }

    /**
     * 支付回调
     * 微信
     */
    private function notifyWxPay()
    {
        // 读取原始流数据
        $postStr = file_get_contents('php://input');
        $notify  = (array)simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
        // 打印源数据
        DebugHelper::payNotifyLog('微信支付回调-源数据:' . json_encode($notify));

        // 平台订单号
        $orderNo = $notify['out_trade_no'] ?? '';
        // 交易订单号
        $tradeNo = $notify['transaction_id'] ?? '';

        //验签
        if ($notify && (new WxPayServer())->verifySign($notify, $notify['sign'] ?? '') === false) {
            $this->logWrite('微信支付回调-验签失败', $orderNo, $notify, BaseMember::TYPE_PERSON);
            exit('fail');
        }

        if ($notify && $notify['return_code'] == 'SUCCESS' && $notify['result_code'] == 'SUCCESS') {
            //验证通过
            $res = $this->updateOrder($orderNo, $tradeNo, $notify);
            if ($res !== false) {
                exit('success');
            } else {
                exit('fail');
            }
        } else {
            $this->logWrite('微信支付回调-数据异常', $orderNo, $notify, BaseMember::TYPE_PERSON);
            exit('fail');
        }
    }

    /**
     * 订单状态修改
     */
    private function updateOrder($orderNo, $tradeNo, $notify)
    {
        $payTime = CUR_DATETIME;
        // 查询订单状态
        $orderRow = BaseResumeOrder::findOne([
            'order_no' => $orderNo,
            'status'   => BaseResumeOrder::STATUS_CREATED,
        ]);
        DebugHelper::payNotifyLog('微信支付回调-查询订单状态:' . $orderRow->status);

        if (empty($orderRow)) {
            $this->logWrite('订单回调异常-订单不存在', $orderNo, $notify, BaseMember::TYPE_PERSON);
            if (Yii::$app->params['environment'] == 'prod') {
                // 给企业微信发一个
                $wxWorkApp = WxWork::getInstance();
                // 价格权益和人
                $orderRowCancel = BaseResumeOrder::findOne([
                    'order_no' => $orderNo,
                ]);
                $wxWorkApp->robotMessageToPaySuccess("相关同事注意：订单支付成功但权益未开通成功！订单号为：{$orderRowCancel->order_no}，简历ID：{$orderRowCancel->resume_id}，支付金额：{$orderRowCancel->real_amount},平台订单状态为：【" . BaseResumeOrder::STATUS_LIST[$orderRowCancel->status] . "】({$orderRowCancel->status})。");
            }

            //这里暂时改为true---告诉微信我们收到了支付成功过的通知
            //return false;
            return true;
        }

        // 修改为已支付
        $orderRow->status   = BaseResumeOrder::STATUS_PAID;
        $orderRow->trade_no = $tradeNo;
        $orderRow->pay_time = $payTime;
        try {
            $res = $orderRow->save();
            if (!$res) {
                $this->logWrite('订单回调异常-订单修改失败，停止执行！', $orderNo, $notify, BaseMember::TYPE_PERSON);

                return false;
            }
        } catch (\Exception $e) {
            $this->logWrite('订单回调异常-订单状态修改失败-' . $e->getMessage(), $orderNo, $notify,
                BaseMember::TYPE_PERSON);

            return false;
        }

        // 支付状态修改成功,删除缓存
        $orderKey = Cache::ALL_RESUME_ORDER_KEY . ':' . $orderRow['resume_id'] . ':' . $orderRow['id'];
        Cache::delete($orderKey);
        DebugHelper::payNotifyLog('微信支付回调-看看订单信息:' . json_encode($orderRow->getAttributes()));
        // 回调支付成功，新增埋点日志
        //保存用户付费转化埋点日志

        $payLogApp = new OderLogService();
        $payLogApp->setOparetion(ResumePaymentServer::PAY_NOTIFY)
            ->setPlatform($orderRow->platform)
            ->setData([
                'orderNo'         => $orderNo,
                'equityPackageId' => $orderRow->equity_package_id,
            ])
            ->addLog();

        // 修改权益
        return $this->updateEquity($orderNo, $notify, $orderRow);
    }

    /**
     * 求职者权益日志添加
     * 求职者益记录添加/修改
     * 求职者会员状态修改
     */
    private function updateEquity($orderNo, $notify, $orderRow)
    {
        $payTime      = $orderRow['pay_time'];
        $payTimeStamp = strtotime($payTime);
        // 创建事务修改权益及会员状态
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 订单主键id
            $orderId = $orderRow['id'];
            // 简历id
            $resumeId = $orderRow['resume_id'];
            //获取简历信息
            $resumeInfo = BaseResume::findOne($resumeId);
            // 权益组合信息
            $equityPackageRow = BaseResumeEquityPackageSetting::findOne($orderRow['equity_package_id']);
            // 权益组合id
            $equityPackageId = $equityPackageRow['id'];
            // 权益组合类型id
            $equityPackageCategoryId = $equityPackageRow['equity_package_category_id'];
            // 权益组合配置（有效天数）
            $days = $equityPackageRow['days'];
            // 有效期时间
            $daysTimes = 24 * 3600 * $days;
            // 有效期时间，改规则，下单当天支付成功时间到23:59:59属于订单赠送时间，第二天开始算有效期天数；
            // 即下单时间到当天结束时间
            $daysTimesCurrent = (strtotime(date('Y-m-d 23:59:59', $payTimeStamp)) - $payTimeStamp);

            // 权益ids(多个)
            $equityIds = BaseResumeEquityPackageRelationSetting::getEquityIdsByPackageId($equityPackageId,
                BaseResumeEquitySetting::STATUS_ONLINE);
            //是否是升级
            $isUpgrade = false;
            //在处理新的权益前先看是不是黄金VIP升级钻石VIP
            if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD && $equityPackageRow->equity_package_category_id == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                //这里说明正在进行升级
                //先看一下支付金额是否是与优惠之后一致
                $convertData = BaseResumeEquityPackageSetting::goldConvertDiamond($resumeId,
                    $orderRow['equity_package_id']);
                if ($convertData['real_amount'] != $orderRow['real_amount']) {
                    //这里是属于升级时候金额支付异常，但是属于正常显现记录一下日志即可
                    $this->logWrite('订单回调异常-黄金VIP升级钻石VIP差额折扣支付金额差异；实付金额：' . $orderRow['real_amount'] . ',折扣后金额：' . $convertData['real_amount'],
                        $orderNo, $notify, BaseMember::TYPE_PERSON);
                }
                //这里需要把之前的黄金VIP权益全部失效
                $this->goldVipExpire($resumeId);
                $isUpgrade = true;
            }
            DebugHelper::payNotifyLog('微信支付回调-处理权益的IDS:' . json_encode($equityIds));

            // 求职者权益写入
            // 1.1 当前权益已拥有
            $equityHaveRows = BaseResumeEquity::find()
                ->where([
                    'resume_id' => $resumeId,
                    'equity_id' => $equityIds,
                ])
                ->all();
            $equityHaveIds  = [];
            if ($equityHaveRows) {
                foreach ($equityHaveRows as $row) {
                    $equityHaveIds[] = $row['equity_id'];
                    // 交给after去处理更新就好了
                    // $expireTimeStamp = strtotime($row['expire_time']);
                    // // 未过期,过期时间+有效期天数
                    // if ($expireTimeStamp > $payTimeStamp) {
                    //     $row->expire_time = date('Y-m-d H:i:s', $expireTimeStamp + $daysTimes);
                    // } else {
                    //     // 已过期,支付时间+有效期天数
                    //     $row->expire_time = date('Y-m-d H:i:s', $payTimeStamp + $daysTimes);
                    //     // 已过期,修改生效时间为当前支付时间
                    //     $row->begin_time = $payTime;
                    // }
                    // $row->expire_status = BaseResumeEquity::STATUS_EXPIRE;
                    // $row->save();
                }
            }

            // 1.2 当前权益未拥有(当前权益与已拥有权益的差级)
            $equityNotHaveIds = array_diff($equityIds, $equityHaveIds);
            if ($equityNotHaveIds) {
                $equityInserts = [];
                foreach ($equityNotHaveIds as $equityId) {
                    $equityInserts[] = [
                        $payTime,
                        $resumeId,
                        $equityId,
                        $payTime,
                        date('Y-m-d H:i:s', $payTimeStamp + $daysTimes),
                        BaseResumeEquity::STATUS_EXPIRE,
                    ];
                }
                // 批量写入
                Yii::$app->db->createCommand()
                    ->batchInsert(BaseResumeEquity::tableName(), [
                        'add_time',
                        'resume_id',
                        'equity_id',
                        'begin_time',
                        'expire_time',
                        'expire_status',
                    ], $equityInserts)
                    ->execute();
            }

            // 2.1 当前权益已拥有
            $beforeAmount          = [];
            $afterAmount           = [];
            $equityPackageHaveRows = BaseResumeEquityPackage::find()
                ->where([
                    'resume_id'           => $resumeId,
                    'package_category_id' => $equityPackageCategoryId,
                    'equity_id'           => $equityIds,
                ])
                ->all();
            $equityPackageHaveIds  = [];
            if ($equityPackageHaveRows) {
                foreach ($equityPackageHaveRows as $packageRow) {
                    /** @var BaseResumeEquityPackage $packageRow */
                    $equityPackageHaveIds[] = $packageRow['equity_id'];
                    $expireTimeStamp        = strtotime($packageRow['expire_time']);
                    // 未过期,过期时间+有效期天数
                    if ($expireTimeStamp > $payTimeStamp) {
                        $packageRow->expire_time = date('Y-m-d H:i:s', $expireTimeStamp + $daysTimes);
                        if (in_array($packageRow['equity_id'], BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES)) {
                            $amount                                 = $packageRow['amount'] + BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[$packageRow['equity_id']][$equityPackageRow['days']];
                            $beforeAmount[$packageRow['equity_id']] = $packageRow['amount'];
                            $afterAmount[$packageRow['equity_id']]  = $amount;
                            $packageRow->amount                     = $amount;
                        } else {
                            $beforeAmount[$packageRow['equity_id']] = 0;
                            $afterAmount[$packageRow['equity_id']]  = 0;
                        }
                    } else {
                        // 已过期,支付时间+有效期天数
                        $packageRow->expire_time = date('Y-m-d H:i:s', $payTimeStamp + $daysTimes + $daysTimesCurrent);
                        // 已过期,修改生效时间为当前支付时间
                        $packageRow->begin_time = $payTime;
                        if (in_array($packageRow['equity_id'], BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES)) {
                            $amount                                 = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[$packageRow['equity_id']][$equityPackageRow['days']];
                            $beforeAmount[$packageRow['equity_id']] = 0;
                            $afterAmount[$packageRow['equity_id']]  = $amount;
                            $packageRow->amount                     = $amount;
                        } else {
                            $beforeAmount[$packageRow['equity_id']] = 0;
                            $afterAmount[$packageRow['equity_id']]  = 0;
                        }
                    }
                    $packageRow->expire_status = BaseResumeEquity::STATUS_EXPIRE;
                    $packageRow->save();
                }
            }
            // 2.2 当前权益未拥有(当前权益与已拥有权益的差级)
            $equityPackageNotHaveIds = array_diff($equityIds, $equityPackageHaveIds);
            if ($equityPackageNotHaveIds) {
                $equityPackageInserts = [];
                foreach ($equityPackageNotHaveIds as $equityIdItem) {
                    $beforeAmount[$equityIdItem] = 0;
                    if (in_array($equityIdItem, BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES)) {
                        $amount                     = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[$equityIdItem][$equityPackageRow['days']];
                        $afterAmount[$equityIdItem] = $amount;
                    } else {
                        $amount                     = 0;
                        $afterAmount[$equityIdItem] = 0;
                    }
                    $equityPackageInserts[] = [
                        $payTime,
                        $resumeId,
                        $equityPackageCategoryId,
                        $equityIdItem,
                        $payTime,
                        date('Y-m-d H:i:s', $payTimeStamp + $daysTimes + $daysTimesCurrent),
                        BaseResumeEquity::STATUS_EXPIRE,
                        $amount,
                    ];
                }
                // 批量写入
                Yii::$app->db->createCommand()
                    ->batchInsert(BaseResumeEquityPackage::tableName(), [
                        'add_time',
                        'resume_id',
                        'package_category_id',
                        'equity_id',
                        'begin_time',
                        'expire_time',
                        'expire_status',
                        'amount',
                    ], $equityPackageInserts)
                    ->execute();
            }
            // 行为日志记录**********
            BaseResumeEquityActionRecord::saveActionRecord($payTime, $resumeId, $equityIds,
                BaseResumeEquityActionRecord::EQUITY_TYPE_ADD,
                BaseResumeEquityPackageCategorySetting::PACKAGE_EQUITY_CATEGORY_ACTION_TYPE_RELATION[$equityPackageCategoryId],
                $orderId, BaseResumeEquityActionRecord::TYPE_RELATION_REMARK_ORDER,
                BaseResumeEquityActionRecord::TYPE_OPERATION_RESUME, $resumeId, $daysTimes, $beforeAmount,
                $afterAmount);
            //对Resume_equity表的开始时间与结束时间进行更新
            $this->afterResumeEquity($resumeId, $equityIds);
            //3.1获取当前权益组合下的权益---当前数据有特殊性不要轻易使用（注意，注意，注意）
            $equityPackageDetailHaveRows = BaseResumeEquityPackageDetail::find()
                ->select([
                    'equity_id',
                    'expire_time',
                ])
                ->where([
                    'resume_id'           => $resumeId,
                    'package_category_id' => $equityPackageCategoryId,
                    'expire_status'       => BaseResumeEquityPackageDetail::STATUS_EXPIRE,
                ])
                ->orderBy('expire_time asc')
                ->indexBy('equity_id')
                ->asArray()
                ->all();
            $equityPackageDetailInserts  = [];
            foreach ($equityIds as $equityIdChildItem) {
                $begin_time                   = isset($equityPackageDetailHaveRows[$equityIdChildItem]) ? $equityPackageDetailHaveRows[$equityIdChildItem]['expire_time'] : $payTime;
                $expire_time                  = $begin_time == $payTime ? date('Y-m-d H:i:s',
                    strtotime($begin_time) + $daysTimes + $daysTimesCurrent) : date('Y-m-d H:i:s',
                    strtotime($begin_time) + $daysTimes);
                $equityPackageDetailInserts[] = [
                    $payTime,
                    $resumeId,
                    $equityPackageCategoryId,
                    $equityIdChildItem,
                    $begin_time,
                    $expire_time,
                    BaseResumeEquityPackageDetail::STATUS_EXPIRE,
                    in_array($equityIdChildItem,
                        BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES) ? BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[$equityIdChildItem][$equityPackageRow['days']] : 0,
                    $orderId,
                ];
            }
            // 批量写入
            Yii::$app->db->createCommand()
                ->batchInsert(BaseResumeEquityPackageDetail::tableName(), [
                    'add_time',
                    'resume_id',
                    'package_category_id',
                    'equity_id',
                    'begin_time',
                    'expire_time',
                    'expire_status',
                    'amount',
                    'order_id',
                ], $equityPackageDetailInserts)
                ->execute();

            // 修改简历主表会员标识
            if (in_array($equityPackageCategoryId,
                BaseResumeEquityPackageCategorySetting::PACKAGE_EQUITY_CATEGORY_VIP_IDS)) {
                $resumeRow           = BaseResume::findOne($resumeId);
                $vipExpireTimeStamp  = strtotime($resumeRow['vip_expire_time']);
                $resumeRow->vip_type = BaseResume::VIP_TYPE_ACTIVE;
                $before_vip_level    = $resumeRow->vip_level;
                switch ($equityPackageCategoryId) {
                    case BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP:
                        $resumeRow->vip_level = BaseResume::VIP_LEVEL_GOLD;
                        break;
                    case BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP:
                        $resumeRow->vip_level = BaseResume::VIP_LEVEL_DIAMOND;
                        break;
                }
                if ($vipExpireTimeStamp < $payTimeStamp || ($before_vip_level != $resumeRow->vip_level)) { // 之前不是会员或者已过期
                    $resumeRow->vip_expire_time = date('Y-m-d H:i:s', $payTimeStamp + $daysTimes + $daysTimesCurrent);
                    $resumeRow->vip_begin_time  = $payTime;
                } else { // 未过期
                    $resumeRow->vip_expire_time = date('Y-m-d H:i:s', $vipExpireTimeStamp + $daysTimes);
                }

                $resumeRow->save();
            }

            // 修改权益录入状态
            $orderRow->equity_status = BaseResumeOrder::STATUS_EQUITY_SUCCESS;
            $orderRow->save();

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->logWrite('订单回调异常-权益修改失败-' . $e->getMessage(), $orderNo, $notify,
                BaseMember::TYPE_PERSON);

            return false;
        }

        // 发送通知
        try {
            // 查询手机号
            $resumeId   = $orderRow['resume_id'];
            $memberId   = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
            $mobile     = BaseMember::findOneVal(['id' => $memberId], 'mobile');
            $mobileCode = BaseMember::findOneVal(['id' => $memberId], 'mobile_code');
            $extParams  = [
                'order_id'                   => $orderRow['id'],
                'package_name'               => $equityPackageRow['name'],
                'equity_package_category_id' => $equityPackageCategoryId,
                'resume_id'                  => $resumeId,
            ];
            if ($isUpgrade) {
                //升级
                // 发送短信
                Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS,
                    $mobileCode, json_encode($extParams));
            } else {
                // 发送短信
                Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_ORDER_PAY_SUCCESS, $mobileCode,
                    json_encode($extParams));
            }

            // 给企业微信发一个
            $wxWorkApp = WxWork::getInstance();
            // 价格权益和人
            $env        = Yii::$app->params['environment'];
            $messageTxt = "订单支付成功，订单号：{$orderNo}，实付金额为{$orderRow['real_amount']}元，权益：{$equityPackageRow['name']}，简历ID：{$resumeId}，手机号：{$mobile}";
            if ($env != 'prod') {
                $messageTxt = "{$env}环境 " . $messageTxt;
            }
            $wxWorkApp->robotMessageToPaySuccess($messageTxt);
            // $wxWorkApp->robotMessageToPaySuccess("订单支付成功，订单号：{$orderNo}，价格：{$equityPackageRow['real_amount']}元，权益：{$equityPackageRow['name']}，简历ID：{$resumeId}，手机号：{$mobile}");
            //站内信-系统消息
            //            BaseMemberMessage::send($memberId, BaseMemberMessage::TYPE_RESUME_SYSTEM, '服务购买通知',
            //                '您已成功下单' . $equityPackageRow['name'] . '服务');
        } catch (\Exception $e) {
            $this->logWrite('支付通知-异常-' . $e->getMessage(), $orderNo, $notify, BaseMember::TYPE_PERSON);
        }

        return true;
    }

    // 这里是一个临时方法,用于检测活动

    /**
     * @param $data
     * VIP内测活动专题页，对 内测套餐（产品ID=7），设置白名单，调整服务价格&时长，套餐内容不变：
     *
     * 阶段1：2023-08-15 10:00:00-2023-08-20 17:00:00，价格：1元，套餐时长：7天，白名单人员：30人；
     * 阶段2：2023-08-21 10:00:00-2023-08-23 17:00:00，价格：0.1元，套餐时长：7天，白名单人员：30人；
     * 阶段3：2023-08-24 10:00:00-2023-08-25 9:00:00，价格：0.1元，套餐时长：30天，白名单人员：3人；
     * 阶段4：2023-08-25 10:00:00-2023-08-27 17:00:00，价格：0.1元，套餐时长：90天，白名单人员：1人；
     *
     * 需求说明：
     * 1、白名单用户：以运营提供的“手机号”为准，用户点击【立即开通】时校验登录账号手机是否命中白名单；
     * 2、限制每个活动时段内，仅对应时段的白名单用户可下单购买该内测套餐，限购1单（已支付订单）；
     * （1）某活动时段内，白名单用户首次支付订单成功，即开通对应套餐服务；
     * （2）某活动时段内，已支付的白名单用户，点击【立即开通】时，toast提示3s：您已购买，限购1单！
     * （3）某活动时段内，非白名单用户点击【立即开通】时，toast提示3s：抱歉，当前产品您暂无购买权限！
     * （4）非活动时段内，任一用户点击【立即开通】时，toast提示3s：活动未开始，暂不支持购买，敬请期待！
     * 3、当前活动时段结束，即展示下一个活动时段的套餐信息。
     */
    public function checkActivity($data)
    {
        $activityListConfig = [
            [
                'equityPackageId' => 7,
                'whiteList'       => [
                    '15902090572',
                    '18924120081',
                    '13278880829',
                    '15985570946',
                    '13433856778',
                    '18596251637',
                    '15561583159',
                    '17784459043',
                    '13194350919',
                    '13758871298',
                    '15270553779',
                    '15397868016',
                    '18834012748',
                    '15641034803',
                    '15966607159',
                    '17871254419',
                    '15521007716',
                    '17520453264',
                    '15659730831',
                    '18084723916',
                    '18819171649',
                    '18060241903',
                    '17302290260',
                    '15753825906',
                    '15397868016',
                    '18032513187',
                    '13632458383',
                    '15535436249',
                    '17600096909',
                    '13708151571',
                    '13481044481',
                ],
                'timeBegin'       => '2023-08-15 10:00:00',
                'timeEnd'         => '2023-08-20 17:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [
                    '15902090572',
                    '13632458383',
                    '13433856778',
                    '15561583159',
                    '17784459043',
                    '18819171649',
                    '19070589003',
                    '15659730831',
                    '17871254419',
                    '18084723916',
                    '18060241903',
                    '18834012748',
                ],
                'timeBegin'       => '2023-08-21 10:00:00',
                'timeEnd'         => '2023-08-23 17:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [],
                'timeBegin'       => '2023-08-24 10:00:00',
                'timeEnd'         => '2023-08-25 9:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [],
                'timeBegin'       => '2023-08-25 10:00:00',
                'timeEnd'         => '2023-08-27 17:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [
                    '15561583159',
                    '17871254419',
                    '17784459043',
                    '18001395809',
                ],
                'timeBegin'       => '2023-08-29 18:00:00',
                'timeEnd'         => '2023-09-01 10:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [
                    '15902090572',
                    '17713042927',
                ],
                'timeBegin'       => '2023-09-14 10:00:00',
                'timeEnd'         => '2023-09-14 14:00:00',
            ],
            [
                'equityPackageId' => 7,
                'whiteList'       => [
                    '15064103209',
                    '18279130015',
                    '15902090572',
                ],
                'timeBegin'       => '2023-10-18 10:00:00',
                'timeEnd'         => '2023-10-19 14:00:00',
            ],
        ];

        $activityIndex = 6;

        $config = $activityListConfig[$activityIndex];

        if ($data['equityPackageId'] != $config['equityPackageId']) {
            return true;
        }

        $timeBegin = strtotime($config['timeBegin']);
        $timeEnd   = strtotime($config['timeEnd']);

        if (CUR_TIMESTAMP < $timeBegin || CUR_TIMESTAMP > $timeEnd) {
            // 活动还没开始
            throw new Exception('活动未开始，暂不支持购买，敬请期待！');
        }
        $memberId = $data['memberId'];
        // 开始检查白名单.找到账号对应的手机号
        $member = BaseMember::findOne(['id' => $memberId]);
        if (empty($member)) {
            throw new Exception('用户不存在');
        }
        $mobile = $member->mobile;
        if (empty($mobile)) {
            throw new Exception('用户手机号不存在');
        }
        // 检查是否在白名单中
        if (!in_array($mobile, $config['whiteList'])) {
            throw new Exception('抱歉，当前产品您暂无购买权限！');
        }

        $resumeId = $data['resumeId'];

        // 检查在某段时间内是否已经购买过
        $hasBuy = ResumeOrder::find()
            ->where([
                'resume_id'         => $resumeId,
                'equity_package_id' => $config['equityPackageId'],
            ])
            ->andWhere([
                ' > ',
                'add_time',
                $config['timeBegin'],
            ])
            ->andWhere([
                ' < ',
                'add_time',
                $config['timeEnd'],
            ])
            ->andWhere(['status' => ResumeOrder::STATUS_PAID])
            ->exists();

        if ($hasBuy) {
            throw new Exception('您已购买，限购1单！');
        }
    }

    /**
     * 新活动页
     * @param $data
     * @return bool|void
     * @throws Exception
     */
    public function checkActivityNew($data)
    {
        //活动启动时间
        $activity_start_time = '2023-11-23 15:30:00';
        $activity_end_time   = '2028-11-23 17:30:00';
        //是否限购
        $is_activity_buy    = false;
        $activity_buy_total = 0;
        //是否限制白名单购买权限
        $is_activity_buy_rule = false;
        $activity_buy_user    = [];
        if (empty($activity_start_time) || empty($activity_end_time) || CUR_TIMESTAMP < strtotime($activity_start_time) || CUR_TIMESTAMP > strtotime($activity_end_time)) {
            // 活动还没开始
            throw new Exception('活动未开始，暂不支持购买，敬请期待！');
        }
        //白名单购买权限
        if ($is_activity_buy_rule) {
            $memberId = $data['memberId'];
            // 开始检查白名单.找到账号对应的手机号
            $member = BaseMember::findOne(['id' => $memberId]);
            if (empty($member)) {
                throw new Exception('用户不存在');
            }
            $mobile = $member->mobile;
            if (empty($mobile)) {
                throw new Exception('用户手机号不存在');
            }
            // 检查是否在白名单中
            if (!in_array($mobile, $activity_buy_user)) {
                throw new Exception('抱歉，当前产品您暂无购买权限！');
            }
        }
        //是否限购
        if ($is_activity_buy) {
            $resumeId = $data['resumeId'];
            // 检查在某段时间内是否已经购买过
            $toral_buy = ResumeOrder::find()
                ->where([
                    'resume_id'           => $resumeId,
                    'equity_package_id'   => $data['equityPackageId'],
                    'equity_package_type' => BaseResumeOrder::EQUITY_PACKAGE_TYPE_ACTIVITY,
                ])
                ->andWhere([
                    ' > ',
                    'add_time',
                    $activity_start_time,
                ])
                ->andWhere([
                    ' < ',
                    'add_time',
                    $activity_end_time,
                ])
                ->andWhere(['status' => ResumeOrder::STATUS_PAID])
                ->count();
            if ($toral_buy > $activity_buy_total) {
                throw new Exception('您已购买' . $toral_buy . '单，限购' . $activity_buy_total . '单！感谢您参与此次活动！');
            }
        }

        return true;
    }

    /**
     * 保存快照内容
     * @param $orderId
     * @return void
     * @throws Exception
     */
    public function saveSnapshot($orderId, $vipUpgrade = false, $convertData = [])
    {
        $orderInfo        = BaseResumeOrder::findOne($orderId);
        $equityPackageRow = BaseResumeEquityPackageSetting::findOne(['id' => $orderInfo['equity_package_id']]);

        //权益名称
        $equityPackageName = $equityPackageRow['name'];
        //服务天数
        $serviceDays               = $equityPackageRow['days'];
        $equityPackageCategoryName = BaseResumeEquityPackageCategorySetting::findOneVal(['id' => $equityPackageRow['equity_package_category_id']],
            'name');
        //获取权益列表
        $equityText = ResumeEquityPackageRelationSetting::getEquityInfoText($orderInfo['equity_package_id'],
            $serviceDays);
        //保存快照信息

        try {
            $ip = Yii::$app->request->userIP;
        } catch (\Exception $e) {
            $ip = '';
        }
        $snapshotData = [
            'resume_id'                    => $orderInfo['resume_id'],
            'equity_package_id'            => $orderInfo['equity_package_id'],
            //支付方式
            'payway'                       => $this->payway,
            //平台
            'platform'                     => $this->platform,
            //支付通道
            'pay_channel'                  => $this->payChannel,
            //用户ip
            'ip'                           => $ip,
            //原始金额
            'original_amount'              => $orderInfo['original_amount'],
            //真实金额
            'real_amount'                  => $orderInfo['real_amount'],
            //订单号
            'order_no'                     => $orderInfo['order_no'],
            //服务名称
            'equity_package_name'          => $equityPackageName,
            //服务天数
            'service_days'                 => $serviceDays,
            //大类名称
            'equity_package_category_name' => $equityPackageCategoryName,
            //权益内容
            'equity_content'               => $equityText,
        ];
        //属于会员升级的订单
        if ($vipUpgrade) {
            $snapshotData['vip_grade']    = $vipUpgrade;
            $snapshotData['convert_data'] = $convertData;
        }

        $orderInfo->snapshot_data = json_encode($snapshotData);
        if (!$orderInfo->save()) {
            throw new Exception($orderInfo->getFirstErrorsMessage());
        }

        //新增快照记录
        $snapshotModel                      = new BaseResumeOrderSnapshot();
        $snapshotModel->order_id            = $orderId;
        $snapshotModel->equity_package_name = $equityPackageName;
        $snapshotModel->service_days        = $serviceDays;
        $snapshotModel->equity_content      = $equityText;
        if (!$snapshotModel->save()) {
            throw new Exception($snapshotModel->getFirstErrorsMessage());
        }
    }

    //    /**
    //     * 保存用户付费转化埋点日志
    //     * @param $equityPackageId
    //     * @param $orderNo
    //     * @return void
    //     * @throws \yii\base\NotSupportedException
    //     * @throws \yii\db\Exception
    //     */
    //    private function savePayBuriedPointLog($equityPackageId, $orderNo)
    //    {
    //        $equityPackageRow = BaseResumeEquityPackageSetting::findOne(['id' => $equityPackageId]);
    //        $actionId         = self::getPayBuriedPointActionId($equityPackageRow['equity_package_category_id']);
    //        //创建订单新增日志
    //        $equityPackageCategory = BaseResumeEquityPackageCategorySetting::PAY_LOG_VIP_TYPE_TEXT_LIST[$equityPackageRow['equity_package_category_id']] ?: '';
    //
    //        //拼接数据内容
    //        $payBuriedPointLogData = [
    //            'action_type' => BasePayTransformBuriedPointLog::ACTION_TYPE_CLICK,
    //            'action_id'   => $actionId,
    //            'action_name' => BasePayTransformBuriedPointLog::ACTION_NAME_LIST[$actionId],
    //            'params'      => [
    //                BasePayTransformBuriedPointLog::PARAMS_PRODUCT_ID   => $equityPackageId,
    //                BasePayTransformBuriedPointLog::PARAMS_PRODUCT_NAME => BaseResumeEquityPackageSetting::findOneVal(['id' => $equityPackageId],
    //                    'name'),
    //                BasePayTransformBuriedPointLog::PARAMS_ORDER_NUMBER => $orderNo,
    //                BasePayTransformBuriedPointLog::PARAMS_PRODUCT_TYPE => $equityPackageCategory,
    //                BasePayTransformBuriedPointLog::PARAMS_SOURCE_ENTRY => BasePayTransformBuriedPointLog::getLastViewEntry($actionId),
    //            ],
    //            'platform'    => $this->platform,
    //        ];
    //        if ($this->operation == ResumePaymentServer::PAY_NOTIFY) {
    //            $payBuriedPointLogData['params'][BasePayTransformBuriedPointLog::PARAMS_PAY_PRICE] = $equityPackageRow['real_amount'];
    //        } elseif ($this->operation == ResumePaymentServer::PAY_ORDER) {
    //            $payBuriedPointLogData['params'][BasePayTransformBuriedPointLog::PARAMS_ORDER_PRICE] = $equityPackageRow['real_amount'];
    //        }
    //        BasePayTransformBuriedPointLog::createLog($payBuriedPointLogData);
    //    }
    //
    //    /**
    //     * 根据平台、订单状态类型、套餐类型，获取对应的埋点日志actionId
    //     * @param $equityPackageCategoryId
    //     * @return string
    //     */
    //    public function getPayBuriedPointActionId($equityPackageCategoryId)
    //    {
    //        switch ($equityPackageCategoryId) {
    //            //如果是黄金、钻石会员，是pc、h5的vip页面创建订单
    //            case BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP:
    //            case BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP:
    //                if ($this->operation == ResumePaymentServer::PAY_NOTIFY) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_VIP_PAY_SUCCESS : BasePayTransformBuriedPointLog::ACTION_ID_H5_VIP_PAY_SUCCESS;
    //                } elseif ($this->operation == ResumePaymentServer::PAY_ORDER) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_VIP_CREATE_ORDER : BasePayTransformBuriedPointLog::ACTION_ID_H5_VIP_CREATE_ORDER;
    //                }
    //            case BaseResumeEquityPackageCategorySetting::ID_INSIGHT:
    //                if ($this->operation == ResumePaymentServer::PAY_NOTIFY) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_INSIGHT_PAY_SUCCESS : BasePayTransformBuriedPointLog::ACTION_ID_H5_INSIGHT_PAY_SUCCESS;
    //                } elseif ($this->operation == ResumePaymentServer::PAY_ORDER) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_INSIGHT_CREATE_ORDER : BasePayTransformBuriedPointLog::ACTION_ID_H5_INSIGHT_CREATE_ORDER;
    //                }
    //            case BaseResumeEquityPackageCategorySetting::ID_JOB_FAST:
    //                if ($this->operation == ResumePaymentServer::PAY_NOTIFY) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_HUNT_JOB_PAY_SUCCESS : BasePayTransformBuriedPointLog::ACTION_ID_H5_HUNT_JOB_PAY_SUCCESS;
    //                } elseif ($this->operation == ResumePaymentServer::PAY_ORDER) {
    //                    $actionId = $this->platform == BaseResumeOrder::PLATFORM_WEB ? BasePayTransformBuriedPointLog::ACTION_ID_PC_HUNT_JOB_CREATE_ORDER : BasePayTransformBuriedPointLog::ACTION_ID_H5_HUNT_JOB_CREATE_ORDER;
    //                }
    //        }
    //
    //        return $actionId ?: '';
    //    }

    /**
     * 重新更新一下时间长度
     * @param $equityIds
     */
    public function afterResumeEquity($resumeId, $equityIds)
    {
        //循环权益id
        foreach ($equityIds as $equityId) {
            //获取未过期的各包权益数据
            $min_time = BaseResumeEquityPackage::find()
                ->andWhere([
                    'resume_id'     => $resumeId,
                    'equity_id'     => $equityId,
                    'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
                ])
                ->min('begin_time');
            $max_time = BaseResumeEquityPackage::find()
                ->andWhere([
                    'resume_id'     => $resumeId,
                    'equity_id'     => $equityId,
                    'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
                ])
                ->max('expire_time');
            //获取权益数据
            $equityRow = BaseResumeEquity::findOne([
                'resume_id' => $resumeId,
                'equity_id' => $equityId,
            ]);
            if (!$equityRow) {
                continue;
            }
            $equityRow->begin_time    = $min_time;
            $equityRow->expire_time   = $max_time;
            $equityRow->expire_status = strtotime($max_time) < time() ? BaseResumeEquity::STATUS_EXPIRED : BaseResumeEquity::STATUS_EXPIRE;
            $equityRow->save();
        }
    }

    /**
     * 失效黄金会员权益
     */
    public function goldVipExpire($resumeId)
    {
        //获取所有在生效的黄金会员权益
        BaseResumeEquityPackage::updateAll(['expire_status' => BaseResumeEquityPackage::STATUS_EXPIRED], [
            'resume_id'           => $resumeId,
            'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
            'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
        ]);
        BaseResumeEquityPackageDetail::updateAll(['expire_status' => BaseResumeEquityPackageDetail::STATUS_EXPIRED], [
            'resume_id'           => $resumeId,
            'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
            'expire_status'       => BaseResumeEquityPackageDetail::STATUS_EXPIRE,
        ]);
    }
}
